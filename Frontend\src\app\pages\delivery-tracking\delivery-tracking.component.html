<div class="tracking-container">
  <app-sidebar></app-sidebar>

  <div class="tracking-content">
    <app-header
      title="Suivi des livraisons"
      subtitle="Suivez en temps réel l'état de toutes les livraisons"
    >
      <div class="d-flex gap-2">
        <button class="btn btn-primary" (click)="onNewDelivery()" *ngIf="!isDriverView">
          <i class="fa-solid fa-plus me-2"></i>
          Nouvelle livraison
        </button>
        <button class="btn btn-outline-secondary" (click)="onExport()">
          <i class="fa-solid fa-file-export me-2"></i>
          Exporter
        </button>
      </div>
    </app-header>

    <div class="tracking-body p-4">
      <div *ngIf="loading" class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Chargement...</span>
        </div>
        <p class="mt-2">Chargement des données...</p>
      </div>

      <div *ngIf="error" class="alert alert-danger">
        {{ error }}
      </div>

      <ng-container *ngIf="!loading && !error">
        <div class="row g-4">
          <!-- Filters and List -->
          <div class="col-lg-5">
            <div class="card mb-4">
              <div class="card-body">
                <div class="row g-3">
                  <div class="col-md-6">
                    <label for="statusFilter" class="form-label">Statut</label>
                    <select
                      id="statusFilter"
                      class="form-select"
                      [(ngModel)]="statusFilter"
                      (change)="applyFilters()"
                    >
                      <option value="all">Tous les statuts</option>
                      <option value="Pending">En attente</option>
                      <option value="InTransit">En cours</option>
                      <option value="Delivered">Livré</option>
                      <option value="Delayed">Retardé</option>
                      <option value="Cancelled">Annulé</option>
                    </select>
                  </div>
                  <div class="col-md-6">
                    <label for="searchFilter" class="form-label">Recherche</label>
                    <div class="input-group">
                      <input
                        type="text"
                        id="searchFilter"
                        class="form-control"
                        placeholder="Rechercher..."
                        [(ngModel)]="searchTerm"
                        (input)="applyFilters()"
                      >
                      <span class="input-group-text">
                        <i class="fa-solid fa-search"></i>
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="card">
              <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Livraisons</h5>
                <span class="badge bg-primary">{{ filteredDeliveries.length }}</span>
              </div>
              <div class="card-body p-0">
                <div class="list-group list-group-flush delivery-list">
                  <button
                    *ngFor="let delivery of filteredDeliveries"
                    class="list-group-item list-group-item-action p-3"
                    [class.active]="selectedDelivery?.id === delivery.id"
                    (click)="selectDelivery(delivery)"
                  >
                    <div class="d-flex justify-content-between align-items-center">
                      <div>
                        <h6 class="mb-1">{{ delivery.customerName }}</h6>
                        <p class="text-muted small mb-0">{{ delivery.address }}</p>
                      </div>
                      <div class="text-end">
                        <app-status-badge
                          [status]="getStatusText(delivery.status)"
                          [variant]="getStatusClass(delivery.status)"
                        ></app-status-badge>
                        <p class="text-muted small mt-1">
                          {{ delivery.estimatedDeliveryTime | date:'HH:mm' }}
                        </p>
                      </div>
                    </div>
                  </button>

                  <div *ngIf="filteredDeliveries.length === 0" class="p-4 text-center text-muted">
                    <i class="fa-solid fa-box-open fa-2x mb-3"></i>
                    <p>Aucune livraison trouvée</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Map and Details -->
          <div class="col-lg-7">
            <div class="card mb-4">
              <div class="card-body p-0">
                <app-map-view
                  [deliveries]="selectedDelivery ? [selectedDelivery] : []"
                  [drivers]="getSelectedDrivers()"
                  [height]="300"
                  [center]="getMapCenter()"
                  [zoom]="14"
                ></app-map-view>
              </div>
            </div>

            <div class="card" *ngIf="selectedDelivery">
              <div class="card-header">
                <h5 class="card-title mb-0">Détails de la livraison</h5>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-md-6">
                    <h6 class="text-muted mb-3">Informations générales</h6>
                    <dl class="row mb-0">
                      <dt class="col-sm-5">ID Commande</dt>
                      <dd class="col-sm-7">{{ selectedDelivery.orderId }}</dd>

                      <dt class="col-sm-5">Client</dt>
                      <dd class="col-sm-7">{{ selectedDelivery.customerName }}</dd>

                      <dt class="col-sm-5">Adresse</dt>
                      <dd class="col-sm-7">{{ selectedDelivery.address }}</dd>

                      <dt class="col-sm-5">Statut</dt>
                      <dd class="col-sm-7">
                        <app-status-badge
                          [status]="getStatusText(selectedDelivery.status)"
                          [variant]="getStatusClass(selectedDelivery.status)"
                        ></app-status-badge>
                      </dd>

                      <dt class="col-sm-5">Priorité</dt>
                      <dd class="col-sm-7">{{ getPriorityText(selectedDelivery.priority) }}</dd>

                      <dt class="col-sm-5">Distance</dt>
                      <dd class="col-sm-7">{{ selectedDelivery.distance ? (selectedDelivery.distance | number:'1.1-1') + ' km' : 'N/A' }}</dd>
                    </dl>
                  </div>

                  <div class="col-md-6">
                    <h6 class="text-muted mb-3">Informations de livraison</h6>
                    <dl class="row mb-0">
                      <dt class="col-sm-5">Livreur</dt>
                      <dd class="col-sm-7">{{ selectedDelivery.driverName || 'Non assigné' }}</dd>

                      <dt class="col-sm-5">Heure estimée</dt>
                      <dd class="col-sm-7">{{ formatDate(selectedDelivery.estimatedDeliveryTime) }}</dd>

                      <dt class="col-sm-5">Heure réelle</dt>
                      <dd class="col-sm-7">{{ selectedDelivery.actualDeliveryTime ? formatDate(selectedDelivery.actualDeliveryTime) : 'N/A' }}</dd>

                      <dt class="col-sm-5">Heure de prise</dt>
                      <dd class="col-sm-7">{{ selectedDelivery.pickupTime ? formatDate(selectedDelivery.pickupTime) : 'N/A' }}</dd>

                      <dt class="col-sm-5">Trafic</dt>
                      <dd class="col-sm-7">{{ selectedDelivery.trafficCondition || 'N/A' }}</dd>

                      <dt class="col-sm-5">Météo</dt>
                      <dd class="col-sm-7">{{ selectedDelivery.weatherCondition || 'N/A' }}</dd>
                    </dl>
                  </div>
                </div>

                <div class="mt-4">
                  <h6 class="text-muted mb-3">Notes</h6>
                  <p class="mb-0">{{ selectedDelivery.notes || 'Aucune note' }}</p>
                </div>

                <div class="mt-4">
                  <h6 class="text-muted mb-3">Feedback client</h6>
                  <div *ngIf="selectedDelivery.customerRating" class="mb-2">
                    <div class="d-flex align-items-center">
                      <span class="me-2">Note:</span>
                      <div class="rating">
                        <i *ngFor="let star of [1,2,3,4,5]"
                           class="fa-solid fa-star"
                           [class.text-warning]="star <= selectedDelivery.customerRating!"
                           [class.text-muted]="star > selectedDelivery.customerRating!"></i>
                      </div>
                    </div>
                  </div>
                  <p class="mb-0">{{ selectedDelivery.customerFeedback || 'Aucun feedback' }}</p>
                </div>

                <div class="mt-4 d-flex gap-2" *ngIf="!isDriverView">
                  <button class="btn btn-primary" (click)="onEditDelivery(selectedDelivery)">
                    <i class="fa-solid fa-pen-to-square me-2"></i>
                    Modifier
                  </button>
                  <button class="btn btn-outline-danger" (click)="onDeleteDelivery(selectedDelivery)">
                    <i class="fa-solid fa-trash me-2"></i>
                    Supprimer
                  </button>
                </div>
                <div class="mt-4 d-flex gap-2" *ngIf="isDriverView">
                  <button class="btn btn-primary" (click)="onUpdateDeliveryStatus(selectedDelivery)">
                    <i class="fa-solid fa-sync me-2"></i>
                    Mettre à jour le statut
                  </button>
                </div>
              </div>
            </div>

            <div class="card" *ngIf="!selectedDelivery">
              <div class="card-body text-center p-5">
                <i class="fa-solid fa-box fa-3x text-muted mb-3"></i>
                <h5>Aucune livraison sélectionnée</h5>
                <p class="text-muted">Sélectionnez une livraison dans la liste pour voir les détails</p>
              </div>
            </div>
          </div>
        </div>
      </ng-container>
    </div>
  </div>
</div>

<!-- Delivery Form Modal -->
<div class="modal fade" [class.show]="showDeliveryForm" [style.display]="showDeliveryForm ? 'block' : 'none'" tabindex="-1">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">
          {{ editingDelivery ? 'Modifier la livraison' : 'Nouvelle livraison' }}
        </h5>
        <button type="button" class="btn-close" (click)="onDeliveryFormCancel()"></button>
      </div>
      <div class="modal-body">
        <app-delivery-form
          [delivery]="editingDelivery"
          (submitted)="onDeliveryFormSubmit($event)"
          (cancelled)="onDeliveryFormCancel()"
        ></app-delivery-form>
      </div>
    </div>
  </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" [class.show]="showDeleteConfirm" [style.display]="showDeleteConfirm ? 'block' : 'none'" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Confirmer la suppression</h5>
        <button type="button" class="btn-close" (click)="onDeleteCancel()"></button>
      </div>
      <div class="modal-body">
        <p>Êtes-vous sûr de vouloir supprimer cette livraison ?</p>
        <p><strong>{{ deletingDelivery?.customerName }} - {{ deletingDelivery?.orderId }}</strong></p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="onDeleteCancel()">Annuler</button>
        <button type="button" class="btn btn-danger" (click)="onDeleteConfirm()">Supprimer</button>
      </div>
    </div>
  </div>
</div>

<!-- Status Update Modal -->
<div class="modal fade" [class.show]="showStatusUpdate" [style.display]="showStatusUpdate ? 'block' : 'none'" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Mettre à jour le statut</h5>
        <button type="button" class="btn-close" (click)="onStatusUpdateCancel()"></button>
      </div>
      <div class="modal-body">
        <div class="mb-3">
          <label for="newStatus" class="form-label">Nouveau statut</label>
          <select id="newStatus" class="form-select" [(ngModel)]="newStatus">
            <option value="0">En attente</option>
            <option value="1">En cours</option>
            <option value="2">Livré</option>
            <option value="3">Retardé</option>
            <option value="4">Annulé</option>
          </select>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" (click)="onStatusUpdateCancel()">Annuler</button>
        <button type="button" class="btn btn-primary" (click)="onStatusUpdateConfirm()">Mettre à jour</button>
      </div>
    </div>
  </div>
</div>
