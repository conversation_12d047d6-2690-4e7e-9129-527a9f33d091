{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { UserRole } from '../../core/models/user.model';\nimport { first } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../../core/services/auth.service\";\nimport * as i4 from \"@angular/common\";\nfunction LoginComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵelement(1, \"i\", 31);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.successMessage, \" \");\n  }\n}\nfunction LoginComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵelement(1, \"i\", 33);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction LoginComponent_div_19_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"L'adresse email est requise\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_div_19_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"L'adresse email n'est pas valide\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵtemplate(1, LoginComponent_div_19_div_1_Template, 2, 0, \"div\", 35);\n    i0.ɵɵtemplate(2, LoginComponent_div_19_div_2_Template, 2, 0, \"div\", 35);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.f[\"email\"].errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.f[\"email\"].errors[\"email\"]);\n  }\n}\nfunction LoginComponent_div_27_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Le mot de passe est requis\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵtemplate(1, LoginComponent_div_27_div_1_Template, 2, 0, \"div\", 35);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.f[\"password\"].errors[\"required\"]);\n  }\n}\nfunction LoginComponent_span_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 36);\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"is-invalid\": a0\n  };\n};\nexport let LoginComponent = /*#__PURE__*/(() => {\n  class LoginComponent {\n    constructor(formBuilder, route, router, authService) {\n      this.formBuilder = formBuilder;\n      this.route = route;\n      this.router = router;\n      this.authService = authService;\n      this.loading = false;\n      this.submitted = false;\n      this.error = '';\n      this.returnUrl = '/dashboard';\n      this.successMessage = '';\n      // Redirect to dashboard if already logged in\n      if (this.authService.isAuthenticated()) {\n        this.router.navigate(['/dashboard']);\n      }\n    }\n    ngOnInit() {\n      this.loginForm = this.formBuilder.group({\n        email: ['', [Validators.required, Validators.email]],\n        password: ['', Validators.required]\n      });\n      // Get return url from route parameters or default based on role\n      this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || this.getDefaultRouteForRole();\n      // Get success message from route parameters\n      this.successMessage = this.route.snapshot.queryParams['message'] || '';\n    }\n    // Convenience getter for easy access to form fields\n    get f() {\n      return this.loginForm.controls;\n    }\n    onSubmit() {\n      this.submitted = true;\n      // Stop here if form is invalid\n      if (this.loginForm.invalid) {\n        return;\n      }\n      this.loading = true;\n      this.authService.login({\n        email: this.f['email'].value,\n        password: this.f['password'].value\n      }).pipe(first()).subscribe({\n        next: user => {\n          this.loading = false;\n          console.log('Login successful, user:', user);\n          // Small delay to ensure authentication state is properly set\n          setTimeout(() => {\n            // Redirect based on user role if no specific return URL\n            const redirectUrl = this.route.snapshot.queryParams['returnUrl'] || this.getDefaultRouteForUser(user);\n            console.log('Redirecting to:', redirectUrl);\n            this.router.navigate([redirectUrl]);\n          }, 100);\n        },\n        error: error => {\n          console.error('Login error:', error);\n          this.error = error.error?.message || error.message || 'Erreur de connexion. Veuillez réessayer.';\n          this.loading = false;\n        }\n      });\n    }\n    getDefaultRouteForRole() {\n      // Default route when no user is logged in yet\n      return '/dashboard';\n    }\n    getDefaultRouteForUser(user) {\n      if (!user || !user.role) {\n        console.log('No user or role, defaulting to dashboard');\n        return '/dashboard';\n      }\n      console.log('User role received:', user.role, 'type:', typeof user.role);\n      // Convert string role to number if needed\n      const role = typeof user.role === 'string' ? this.getRoleFromString(user.role) : user.role;\n      console.log('Converted role:', role);\n      switch (role) {\n        case UserRole.Admin:\n        case UserRole.Manager:\n        case UserRole.Dispatcher:\n          return '/dashboard';\n        case UserRole.Driver:\n          return '/driver-dashboard';\n        case UserRole.Customer:\n          return '/customer-portal';\n        default:\n          console.log('Unknown role, defaulting to dashboard');\n          return '/dashboard';\n      }\n    }\n    getRoleFromString(roleString) {\n      const lowerRole = roleString.toLowerCase();\n      console.log('Converting role string:', lowerRole);\n      switch (lowerRole) {\n        case 'admin':\n          return UserRole.Admin;\n        case 'manager':\n          return UserRole.Manager;\n        case 'dispatcher':\n          return UserRole.Dispatcher;\n        case 'driver':\n          return UserRole.Driver;\n        case 'customer':\n          return UserRole.Customer;\n        default:\n          console.log('Unknown role string:', roleString);\n          return UserRole.Customer;\n      }\n    }\n    static {\n      this.ɵfac = function LoginComponent_Factory(t) {\n        return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.AuthService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: LoginComponent,\n        selectors: [[\"app-login\"]],\n        decls: 41,\n        vars: 13,\n        consts: [[1, \"login-container\"], [1, \"login-card\"], [1, \"login-header\", \"text-center\", \"mb-4\"], [1, \"d-flex\", \"align-items-center\", \"justify-content-center\", \"mb-3\"], [1, \"fa-solid\", \"fa-truck\", \"text-primary\", \"fs-2\", \"me-2\"], [1, \"fs-2\", \"fw-bold\", \"text-primary\", \"mb-0\"], [1, \"fs-5\", \"text-muted\"], [\"class\", \"alert alert-success\", \"role\", \"alert\", 4, \"ngIf\"], [\"class\", \"alert alert-danger\", \"role\", \"alert\", 4, \"ngIf\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"mb-3\"], [\"for\", \"email\", 1, \"form-label\"], [1, \"input-group\"], [1, \"input-group-text\"], [1, \"fa-solid\", \"fa-envelope\"], [\"type\", \"email\", \"formControlName\", \"email\", \"id\", \"email\", \"placeholder\", \"Entrez votre adresse email\", 1, \"form-control\", 3, \"ngClass\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [1, \"mb-4\"], [\"for\", \"password\", 1, \"form-label\"], [1, \"fa-solid\", \"fa-lock\"], [\"type\", \"password\", \"formControlName\", \"password\", \"id\", \"password\", \"placeholder\", \"Entrez votre mot de passe\", 1, \"form-control\", 3, \"ngClass\"], [1, \"d-grid\"], [1, \"btn\", \"btn-primary\", \"btn-lg\", 3, \"disabled\"], [\"class\", \"spinner-border spinner-border-sm me-2\", \"role\", \"status\", \"aria-hidden\", \"true\", 4, \"ngIf\"], [1, \"text-center\", \"mt-4\"], [1, \"mb-0\"], [\"routerLink\", \"/signup\", 1, \"text-primary\", \"text-decoration-none\", \"fw-semibold\"], [1, \"text-center\", \"mt-3\"], [1, \"text-muted\"], [1, \"fa-solid\", \"fa-info-circle\", \"me-1\"], [\"role\", \"alert\", 1, \"alert\", \"alert-success\"], [1, \"fa-solid\", \"fa-check-circle\", \"me-2\"], [\"role\", \"alert\", 1, \"alert\", \"alert-danger\"], [1, \"fa-solid\", \"fa-exclamation-triangle\", \"me-2\"], [1, \"invalid-feedback\"], [4, \"ngIf\"], [\"role\", \"status\", \"aria-hidden\", \"true\", 1, \"spinner-border\", \"spinner-border-sm\", \"me-2\"]],\n        template: function LoginComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n            i0.ɵɵelement(4, \"i\", 4);\n            i0.ɵɵelementStart(5, \"h1\", 5);\n            i0.ɵɵtext(6, \"DeliveryDash\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(7, \"h2\", 6);\n            i0.ɵɵtext(8, \"Connexion \\u00E0 la plateforme\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(9, LoginComponent_div_9_Template, 3, 1, \"div\", 7);\n            i0.ɵɵtemplate(10, LoginComponent_div_10_Template, 3, 1, \"div\", 8);\n            i0.ɵɵelementStart(11, \"form\", 9);\n            i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_11_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵelementStart(12, \"div\", 10)(13, \"label\", 11);\n            i0.ɵɵtext(14, \"Adresse email\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(15, \"div\", 12)(16, \"span\", 13);\n            i0.ɵɵelement(17, \"i\", 14);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(18, \"input\", 15);\n            i0.ɵɵtemplate(19, LoginComponent_div_19_Template, 3, 2, \"div\", 16);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(20, \"div\", 17)(21, \"label\", 18);\n            i0.ɵɵtext(22, \"Mot de passe\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(23, \"div\", 12)(24, \"span\", 13);\n            i0.ɵɵelement(25, \"i\", 19);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(26, \"input\", 20);\n            i0.ɵɵtemplate(27, LoginComponent_div_27_Template, 2, 1, \"div\", 16);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(28, \"div\", 21)(29, \"button\", 22);\n            i0.ɵɵtemplate(30, LoginComponent_span_30_Template, 1, 0, \"span\", 23);\n            i0.ɵɵtext(31, \" Connexion \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(32, \"div\", 24)(33, \"p\", 25);\n            i0.ɵɵtext(34, \" Vous n'avez pas de compte? \");\n            i0.ɵɵelementStart(35, \"a\", 26);\n            i0.ɵɵtext(36, \" Cr\\u00E9er un compte \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(37, \"div\", 27)(38, \"small\", 28);\n            i0.ɵɵelement(39, \"i\", 29);\n            i0.ɵɵtext(40, \" Cr\\u00E9ez un compte ou contactez l'administrateur pour acc\\u00E9der \\u00E0 la plateforme \");\n            i0.ɵɵelementEnd()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngIf\", ctx.successMessage);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"formGroup\", ctx.loginForm);\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(9, _c0, ctx.submitted && ctx.f[\"email\"].errors));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"email\"].errors);\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(11, _c0, ctx.submitted && ctx.f[\"password\"].errors));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.submitted && ctx.f[\"password\"].errors);\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"disabled\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          }\n        },\n        dependencies: [i4.NgClass, i4.NgIf, i2.RouterLink, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName],\n        styles: [\".login-container[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;min-height:100vh;background-color:var(--gray-50);background-image:url(/assets/images/map-background.svg);background-size:cover;background-position:center;position:relative}.login-container[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;inset:0;background-color:#fffc}.login-card[_ngcontent-%COMP%]{width:100%;max-width:420px;padding:2.5rem;background-color:#fff;border-radius:1rem;box-shadow:0 10px 15px -3px #0000001a,0 4px 6px -2px #0000000d;position:relative;z-index:1}.login-header[_ngcontent-%COMP%]{margin-bottom:2rem}.input-group-text[_ngcontent-%COMP%]{background-color:#fff}\"]\n      });\n    }\n  }\n  return LoginComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}