{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/common\";\nfunction DriverFormComponent_div_1_li_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1, \" Le nom est requis \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DriverFormComponent_div_1_li_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1, \" Un email valide est requis \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DriverFormComponent_div_1_li_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1, \" Le num\\u00E9ro de t\\u00E9l\\u00E9phone est requis \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DriverFormComponent_div_1_li_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1, \" Le type de v\\u00E9hicule est requis \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DriverFormComponent_div_1_li_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1, \" Le nombre de livraisons par jour doit \\u00EAtre entre 1 et 50 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DriverFormComponent_div_1_li_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1, \" La latitude doit \\u00EAtre entre -90 et 90 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DriverFormComponent_div_1_li_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1, \" La longitude doit \\u00EAtre entre -180 et 180 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DriverFormComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"h6\", 41);\n    i0.ɵɵelement(2, \"i\", 42);\n    i0.ɵɵtext(3, \" Veuillez corriger les erreurs suivantes: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"ul\", 43);\n    i0.ɵɵtemplate(5, DriverFormComponent_div_1_li_5_Template, 2, 0, \"li\", 44);\n    i0.ɵɵtemplate(6, DriverFormComponent_div_1_li_6_Template, 2, 0, \"li\", 44);\n    i0.ɵɵtemplate(7, DriverFormComponent_div_1_li_7_Template, 2, 0, \"li\", 44);\n    i0.ɵɵtemplate(8, DriverFormComponent_div_1_li_8_Template, 2, 0, \"li\", 44);\n    i0.ɵɵtemplate(9, DriverFormComponent_div_1_li_9_Template, 2, 0, \"li\", 44);\n    i0.ɵɵtemplate(10, DriverFormComponent_div_1_li_10_Template, 2, 0, \"li\", 44);\n    i0.ɵɵtemplate(11, DriverFormComponent_div_1_li_11_Template, 2, 0, \"li\", 44);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    let tmp_2_0;\n    let tmp_3_0;\n    let tmp_4_0;\n    let tmp_5_0;\n    let tmp_6_0;\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_0_0 = ctx_r0.driverForm.get(\"name\")) == null ? null : tmp_0_0.invalid) && ((tmp_0_0 = ctx_r0.driverForm.get(\"name\")) == null ? null : tmp_0_0.touched));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx_r0.driverForm.get(\"email\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx_r0.driverForm.get(\"email\")) == null ? null : tmp_1_0.touched));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx_r0.driverForm.get(\"phone\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx_r0.driverForm.get(\"phone\")) == null ? null : tmp_2_0.touched));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx_r0.driverForm.get(\"vehicleType\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx_r0.driverForm.get(\"vehicleType\")) == null ? null : tmp_3_0.touched));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx_r0.driverForm.get(\"maxDeliveriesPerDay\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx_r0.driverForm.get(\"maxDeliveriesPerDay\")) == null ? null : tmp_4_0.touched));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx_r0.driverForm.get(\"latitude\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx_r0.driverForm.get(\"latitude\")) == null ? null : tmp_5_0.touched));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = ctx_r0.driverForm.get(\"longitude\")) == null ? null : tmp_6_0.invalid) && ((tmp_6_0 = ctx_r0.driverForm.get(\"longitude\")) == null ? null : tmp_6_0.touched));\n  }\n}\nfunction DriverFormComponent_option_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r10 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r10.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", option_r10.label, \" \");\n  }\n}\nfunction DriverFormComponent_option_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r11 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r11.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", option_r11.label, \" \");\n  }\n}\nexport class DriverFormComponent {\n  constructor(fb) {\n    this.fb = fb;\n    this.driver = null;\n    this.submitted = new EventEmitter();\n    this.cancelled = new EventEmitter();\n    // Vehicle type options\n    this.vehicleTypeOptions = [{\n      value: 'Voiture',\n      label: 'Voiture'\n    }, {\n      value: 'Moto',\n      label: 'Moto'\n    }, {\n      value: 'Vélo',\n      label: 'Vélo'\n    }, {\n      value: 'Camionnette',\n      label: 'Camionnette'\n    }, {\n      value: 'Camion',\n      label: 'Camion'\n    }];\n    // Status options (backend expects integer enum values)\n    this.statusOptions = [{\n      value: 0,\n      label: 'Disponible'\n    }, {\n      value: 1,\n      label: 'Occupé'\n    }, {\n      value: 2,\n      label: 'En pause'\n    }, {\n      value: 3,\n      label: 'Hors ligne'\n    }];\n    this.driverForm = this.fb.group({\n      name: ['', Validators.required],\n      email: ['', [Validators.required, Validators.email]],\n      phone: ['', Validators.required],\n      vehicleType: ['', Validators.required],\n      vehicleId: [''],\n      licenseNumber: [''],\n      licenseExpiryDate: [''],\n      status: [0, Validators.required],\n      isAvailableForUrgentDeliveries: [true],\n      maxDeliveriesPerDay: [20, [Validators.required, Validators.min(1), Validators.max(50)]],\n      preferredZones: [''],\n      latitude: [48.8566, [Validators.required, Validators.min(-90), Validators.max(90)]],\n      longitude: [2.3522, [Validators.required, Validators.min(-180), Validators.max(180)]]\n    });\n  }\n  ngOnInit() {\n    if (this.driver) {\n      this.driverForm.patchValue({\n        name: this.driver.name,\n        email: this.driver.email,\n        phone: this.driver.phone,\n        vehicleType: this.driver.vehicleType,\n        vehicleId: this.driver.vehicleId || '',\n        licenseNumber: this.driver.licenseNumber || '',\n        licenseExpiryDate: this.driver.licenseExpiryDate ? this.formatDateForInput(this.driver.licenseExpiryDate) : '',\n        status: this.driver.status,\n        isAvailableForUrgentDeliveries: this.driver.isAvailableForUrgentDeliveries,\n        maxDeliveriesPerDay: this.driver.maxDeliveriesPerDay,\n        preferredZones: this.driver.preferredZones || '',\n        latitude: this.driver.currentLocation?.latitude || 48.8566,\n        longitude: this.driver.currentLocation?.longitude || 2.3522\n      });\n    }\n  }\n  formatDateForInput(date) {\n    const d = new Date(date);\n    return d.toISOString().slice(0, 10); // Format for date input (YYYY-MM-DD)\n  }\n\n  onSubmit() {\n    if (this.driverForm.valid) {\n      const formValue = this.driverForm.value;\n      // Map form data to CreateDriverRequest format expected by backend\n      const driverData = {\n        name: formValue.name,\n        email: formValue.email,\n        phone: formValue.phone,\n        vehicleType: formValue.vehicleType,\n        vehicleId: formValue.vehicleId || null,\n        status: formValue.status,\n        currentLocation: {\n          latitude: parseFloat(formValue.latitude),\n          longitude: parseFloat(formValue.longitude)\n        },\n        profilePictureUrl: null,\n        licenseNumber: formValue.licenseNumber || null,\n        licenseExpiryDate: formValue.licenseExpiryDate ? formValue.licenseExpiryDate : null,\n        isAvailableForUrgentDeliveries: formValue.isAvailableForUrgentDeliveries,\n        preferredZones: formValue.preferredZones || null,\n        maxDeliveriesPerDay: formValue.maxDeliveriesPerDay\n      };\n      this.submitted.emit(driverData);\n    } else {\n      // Mark all fields as touched to show validation errors\n      this.markFormGroupTouched(this.driverForm);\n    }\n  }\n  markFormGroupTouched(formGroup) {\n    Object.keys(formGroup.controls).forEach(field => {\n      const control = formGroup.get(field);\n      control?.markAsTouched({\n        onlySelf: true\n      });\n    });\n  }\n  onCancel() {\n    this.cancelled.emit();\n  }\n  static {\n    this.ɵfac = function DriverFormComponent_Factory(t) {\n      return new (t || DriverFormComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DriverFormComponent,\n      selectors: [[\"app-driver-form\"]],\n      inputs: {\n        driver: \"driver\"\n      },\n      outputs: {\n        submitted: \"submitted\",\n        cancelled: \"cancelled\"\n      },\n      decls: 84,\n      vars: 20,\n      consts: [[3, \"formGroup\", \"ngSubmit\"], [\"class\", \"alert alert-danger mb-3\", 4, \"ngIf\"], [1, \"row\"], [1, \"col-md-6\"], [1, \"mb-3\"], [\"for\", \"name\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"name\", \"formControlName\", \"name\", 1, \"form-control\"], [1, \"invalid-feedback\"], [\"for\", \"email\", 1, \"form-label\"], [\"type\", \"email\", \"id\", \"email\", \"formControlName\", \"email\", 1, \"form-control\"], [\"for\", \"phone\", 1, \"form-label\"], [\"type\", \"tel\", \"id\", \"phone\", \"formControlName\", \"phone\", 1, \"form-control\"], [\"for\", \"vehicleType\", 1, \"form-label\"], [\"id\", \"vehicleType\", \"formControlName\", \"vehicleType\", 1, \"form-select\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"vehicleId\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"vehicleId\", \"formControlName\", \"vehicleId\", \"placeholder\", \"Ex: AB-123-CD\", 1, \"form-control\"], [\"for\", \"status\", 1, \"form-label\"], [\"id\", \"status\", \"formControlName\", \"status\", 1, \"form-select\"], [\"for\", \"licenseNumber\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"licenseNumber\", \"formControlName\", \"licenseNumber\", \"placeholder\", \"Ex: 123456789\", 1, \"form-control\"], [\"for\", \"licenseExpiryDate\", 1, \"form-label\"], [\"type\", \"date\", \"id\", \"licenseExpiryDate\", \"formControlName\", \"licenseExpiryDate\", 1, \"form-control\"], [\"for\", \"maxDeliveriesPerDay\", 1, \"form-label\"], [\"type\", \"number\", \"id\", \"maxDeliveriesPerDay\", \"formControlName\", \"maxDeliveriesPerDay\", \"min\", \"1\", \"max\", \"50\", 1, \"form-control\"], [1, \"form-label\"], [1, \"col-6\"], [\"type\", \"number\", \"placeholder\", \"Latitude\", \"formControlName\", \"latitude\", \"step\", \"0.000001\", 1, \"form-control\"], [\"type\", \"number\", \"placeholder\", \"Longitude\", \"formControlName\", \"longitude\", \"step\", \"0.000001\", 1, \"form-control\"], [1, \"form-text\", \"text-muted\"], [\"for\", \"preferredZones\", 1, \"form-label\"], [\"id\", \"preferredZones\", \"formControlName\", \"preferredZones\", \"rows\", \"2\", \"placeholder\", \"Ex: Centre-ville, Banlieue nord...\", 1, \"form-control\"], [1, \"form-check\"], [\"type\", \"checkbox\", \"id\", \"isAvailableForUrgentDeliveries\", \"formControlName\", \"isAvailableForUrgentDeliveries\", 1, \"form-check-input\"], [\"for\", \"isAvailableForUrgentDeliveries\", 1, \"form-check-label\"], [1, \"d-flex\", \"gap-2\", \"justify-content-end\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"disabled\"], [1, \"fa-solid\", \"fa-save\", \"me-2\"], [1, \"alert\", \"alert-danger\", \"mb-3\"], [1, \"alert-heading\"], [1, \"fa-solid\", \"fa-exclamation-triangle\", \"me-2\"], [1, \"mb-0\"], [4, \"ngIf\"], [3, \"value\"]],\n      template: function DriverFormComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"form\", 0);\n          i0.ɵɵlistener(\"ngSubmit\", function DriverFormComponent_Template_form_ngSubmit_0_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵtemplate(1, DriverFormComponent_div_1_Template, 12, 7, \"div\", 1);\n          i0.ɵɵelementStart(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"label\", 5);\n          i0.ɵɵtext(6, \"Nom complet *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(7, \"input\", 6);\n          i0.ɵɵelementStart(8, \"div\", 7);\n          i0.ɵɵtext(9, \" Le nom est requis \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 4)(11, \"label\", 8);\n          i0.ɵɵtext(12, \"Email *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(13, \"input\", 9);\n          i0.ɵɵelementStart(14, \"div\", 7);\n          i0.ɵɵtext(15, \" Un email valide est requis \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"div\", 4)(17, \"label\", 10);\n          i0.ɵɵtext(18, \"T\\u00E9l\\u00E9phone *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(19, \"input\", 11);\n          i0.ɵɵelementStart(20, \"div\", 7);\n          i0.ɵɵtext(21, \" Le num\\u00E9ro de t\\u00E9l\\u00E9phone est requis \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(22, \"div\", 4)(23, \"label\", 12);\n          i0.ɵɵtext(24, \"Type de v\\u00E9hicule *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"select\", 13)(26, \"option\", 14);\n          i0.ɵɵtext(27, \"S\\u00E9lectionner un type de v\\u00E9hicule\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(28, DriverFormComponent_option_28_Template, 2, 2, \"option\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"div\", 7);\n          i0.ɵɵtext(30, \" Le type de v\\u00E9hicule est requis \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(31, \"div\", 4)(32, \"label\", 16);\n          i0.ɵɵtext(33, \"Immatriculation du v\\u00E9hicule\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(34, \"input\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"div\", 4)(36, \"label\", 18);\n          i0.ɵɵtext(37, \"Statut *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"select\", 19);\n          i0.ɵɵtemplate(39, DriverFormComponent_option_39_Template, 2, 2, \"option\", 15);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(40, \"div\", 3)(41, \"div\", 4)(42, \"label\", 20);\n          i0.ɵɵtext(43, \"Num\\u00E9ro de permis\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(44, \"input\", 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"div\", 4)(46, \"label\", 22);\n          i0.ɵɵtext(47, \"Date d'expiration du permis\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(48, \"input\", 23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"div\", 4)(50, \"label\", 24);\n          i0.ɵɵtext(51, \"Livraisons max par jour *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(52, \"input\", 25);\n          i0.ɵɵelementStart(53, \"div\", 7);\n          i0.ɵɵtext(54, \" Nombre entre 1 et 50 requis \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(55, \"div\", 4)(56, \"label\", 26);\n          i0.ɵɵtext(57, \"Position actuelle\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"div\", 2)(59, \"div\", 27);\n          i0.ɵɵelement(60, \"input\", 28);\n          i0.ɵɵelementStart(61, \"div\", 7);\n          i0.ɵɵtext(62, \" Latitude invalide \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(63, \"div\", 27);\n          i0.ɵɵelement(64, \"input\", 29);\n          i0.ɵɵelementStart(65, \"div\", 7);\n          i0.ɵɵtext(66, \" Longitude invalide \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(67, \"small\", 30);\n          i0.ɵɵtext(68, \" Position par d\\u00E9faut: Paris (48.8566, 2.3522) \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(69, \"div\", 4)(70, \"label\", 31);\n          i0.ɵɵtext(71, \"Zones pr\\u00E9f\\u00E9r\\u00E9es\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(72, \"textarea\", 32);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"div\", 4)(74, \"div\", 33);\n          i0.ɵɵelement(75, \"input\", 34);\n          i0.ɵɵelementStart(76, \"label\", 35);\n          i0.ɵɵtext(77, \" Disponible pour les livraisons urgentes \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(78, \"div\", 36)(79, \"button\", 37);\n          i0.ɵɵlistener(\"click\", function DriverFormComponent_Template_button_click_79_listener() {\n            return ctx.onCancel();\n          });\n          i0.ɵɵtext(80, \"Annuler\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(81, \"button\", 38);\n          i0.ɵɵelement(82, \"i\", 39);\n          i0.ɵɵtext(83);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          let tmp_2_0;\n          let tmp_3_0;\n          let tmp_4_0;\n          let tmp_5_0;\n          let tmp_8_0;\n          let tmp_9_0;\n          let tmp_10_0;\n          i0.ɵɵproperty(\"formGroup\", ctx.driverForm);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.driverForm.invalid && ctx.driverForm.touched);\n          i0.ɵɵadvance(6);\n          i0.ɵɵclassProp(\"is-invalid\", ((tmp_2_0 = ctx.driverForm.get(\"name\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.driverForm.get(\"name\")) == null ? null : tmp_2_0.touched));\n          i0.ɵɵadvance(6);\n          i0.ɵɵclassProp(\"is-invalid\", ((tmp_3_0 = ctx.driverForm.get(\"email\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx.driverForm.get(\"email\")) == null ? null : tmp_3_0.touched));\n          i0.ɵɵadvance(6);\n          i0.ɵɵclassProp(\"is-invalid\", ((tmp_4_0 = ctx.driverForm.get(\"phone\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx.driverForm.get(\"phone\")) == null ? null : tmp_4_0.touched));\n          i0.ɵɵadvance(6);\n          i0.ɵɵclassProp(\"is-invalid\", ((tmp_5_0 = ctx.driverForm.get(\"vehicleType\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx.driverForm.get(\"vehicleType\")) == null ? null : tmp_5_0.touched));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.vehicleTypeOptions);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngForOf\", ctx.statusOptions);\n          i0.ɵɵadvance(13);\n          i0.ɵɵclassProp(\"is-invalid\", ((tmp_8_0 = ctx.driverForm.get(\"maxDeliveriesPerDay\")) == null ? null : tmp_8_0.invalid) && ((tmp_8_0 = ctx.driverForm.get(\"maxDeliveriesPerDay\")) == null ? null : tmp_8_0.touched));\n          i0.ɵɵadvance(8);\n          i0.ɵɵclassProp(\"is-invalid\", ((tmp_9_0 = ctx.driverForm.get(\"latitude\")) == null ? null : tmp_9_0.invalid) && ((tmp_9_0 = ctx.driverForm.get(\"latitude\")) == null ? null : tmp_9_0.touched));\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"is-invalid\", ((tmp_10_0 = ctx.driverForm.get(\"longitude\")) == null ? null : tmp_10_0.invalid) && ((tmp_10_0 = ctx.driverForm.get(\"longitude\")) == null ? null : tmp_10_0.touched));\n          i0.ɵɵadvance(17);\n          i0.ɵɵproperty(\"disabled\", !ctx.driverForm.valid);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.driver ? \"Mettre \\u00E0 jour\" : \"Cr\\u00E9er le livreur\", \" \");\n        }\n      },\n      dependencies: [i2.NgForOf, i2.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.CheckboxControlValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MinValidator, i1.MaxValidator, i1.FormGroupDirective, i1.FormControlName],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵtemplate", "DriverFormComponent_div_1_li_5_Template", "DriverFormComponent_div_1_li_6_Template", "DriverFormComponent_div_1_li_7_Template", "DriverFormComponent_div_1_li_8_Template", "DriverFormComponent_div_1_li_9_Template", "DriverFormComponent_div_1_li_10_Template", "DriverFormComponent_div_1_li_11_Template", "ɵɵadvance", "ɵɵproperty", "tmp_0_0", "ctx_r0", "driverForm", "get", "invalid", "touched", "tmp_1_0", "tmp_2_0", "tmp_3_0", "tmp_4_0", "tmp_5_0", "tmp_6_0", "option_r10", "value", "ɵɵtextInterpolate1", "label", "option_r11", "DriverFormComponent", "constructor", "fb", "driver", "submitted", "cancelled", "vehicleTypeOptions", "statusOptions", "group", "name", "required", "email", "phone", "vehicleType", "vehicleId", "licenseNumber", "licenseExpiryDate", "status", "isAvailableForUrgentDeliveries", "maxDeliveriesPerDay", "min", "max", "preferredZones", "latitude", "longitude", "ngOnInit", "patchValue", "formatDateForInput", "currentLocation", "date", "d", "Date", "toISOString", "slice", "onSubmit", "valid", "formValue", "driverData", "parseFloat", "profilePictureUrl", "emit", "markFormGroupTouched", "formGroup", "Object", "keys", "controls", "for<PERSON>ach", "field", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "onlySelf", "onCancel", "ɵɵdirectiveInject", "i1", "FormBuilder", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "DriverFormComponent_Template", "rf", "ctx", "ɵɵlistener", "DriverFormComponent_Template_form_ngSubmit_0_listener", "DriverFormComponent_div_1_Template", "DriverFormComponent_option_28_Template", "DriverFormComponent_option_39_Template", "DriverFormComponent_Template_button_click_79_listener", "ɵɵclassProp", "tmp_8_0", "tmp_9_0", "tmp_10_0"], "sources": ["C:\\Users\\<USER>\\delivery-dash-optimiser\\frontend\\src\\app\\shared\\components\\driver-form\\driver-form.component.ts", "C:\\Users\\<USER>\\delivery-dash-optimiser\\frontend\\src\\app\\shared\\components\\driver-form\\driver-form.component.html"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Driver, DriverStatus } from '@core/models/driver.model';\n\n@Component({\n  selector: 'app-driver-form',\n  templateUrl: './driver-form.component.html',\n  styleUrls: ['./driver-form.component.scss']\n})\nexport class DriverFormComponent implements OnInit {\n  @Input() driver: Driver | null = null;\n  @Output() submitted = new EventEmitter<any>();\n  @Output() cancelled = new EventEmitter<void>();\n\n  driverForm: FormGroup;\n\n  // Vehicle type options\n  vehicleTypeOptions = [\n    { value: 'Voiture', label: 'Voiture' },\n    { value: 'Moto', label: 'Mo<PERSON>' },\n    { value: 'Vélo', label: 'Vélo' },\n    { value: '<PERSON>ionnette', label: '<PERSON>ionnette' },\n    { value: 'Camion', label: 'Camion' }\n  ];\n\n  // Status options (backend expects integer enum values)\n  statusOptions = [\n    { value: 0, label: 'Disponible' },\n    { value: 1, label: 'Occupé' },\n    { value: 2, label: 'En pause' },\n    { value: 3, label: 'Hors ligne' }\n  ];\n\n  constructor(private fb: FormBuilder) {\n    this.driverForm = this.fb.group({\n      name: ['', Validators.required],\n      email: ['', [Validators.required, Validators.email]],\n      phone: ['', Validators.required],\n      vehicleType: ['', Validators.required],\n      vehicleId: [''],\n      licenseNumber: [''],\n      licenseExpiryDate: [''],\n      status: [0, Validators.required], // 0 = Available\n      isAvailableForUrgentDeliveries: [true],\n      maxDeliveriesPerDay: [20, [Validators.required, Validators.min(1), Validators.max(50)]],\n      preferredZones: [''],\n      latitude: [48.8566, [Validators.required, Validators.min(-90), Validators.max(90)]],\n      longitude: [2.3522, [Validators.required, Validators.min(-180), Validators.max(180)]]\n    });\n  }\n\n  ngOnInit() {\n    if (this.driver) {\n      this.driverForm.patchValue({\n        name: this.driver.name,\n        email: this.driver.email,\n        phone: this.driver.phone,\n        vehicleType: this.driver.vehicleType,\n        vehicleId: this.driver.vehicleId || '',\n        licenseNumber: this.driver.licenseNumber || '',\n        licenseExpiryDate: this.driver.licenseExpiryDate ? this.formatDateForInput(this.driver.licenseExpiryDate) : '',\n        status: this.driver.status,\n        isAvailableForUrgentDeliveries: this.driver.isAvailableForUrgentDeliveries,\n        maxDeliveriesPerDay: this.driver.maxDeliveriesPerDay,\n        preferredZones: this.driver.preferredZones || '',\n        latitude: this.driver.currentLocation?.latitude || 48.8566,\n        longitude: this.driver.currentLocation?.longitude || 2.3522\n      });\n    }\n  }\n\n  private formatDateForInput(date: Date | string): string {\n    const d = new Date(date);\n    return d.toISOString().slice(0, 10); // Format for date input (YYYY-MM-DD)\n  }\n\n  onSubmit() {\n    if (this.driverForm.valid) {\n      const formValue = this.driverForm.value;\n\n      // Map form data to CreateDriverRequest format expected by backend\n      const driverData = {\n        name: formValue.name,\n        email: formValue.email,\n        phone: formValue.phone,\n        vehicleType: formValue.vehicleType,\n        vehicleId: formValue.vehicleId || null,\n        status: formValue.status,\n        currentLocation: {\n          latitude: parseFloat(formValue.latitude),\n          longitude: parseFloat(formValue.longitude)\n        },\n        profilePictureUrl: null,\n        licenseNumber: formValue.licenseNumber || null,\n        licenseExpiryDate: formValue.licenseExpiryDate ? formValue.licenseExpiryDate : null,\n        isAvailableForUrgentDeliveries: formValue.isAvailableForUrgentDeliveries,\n        preferredZones: formValue.preferredZones || null,\n        maxDeliveriesPerDay: formValue.maxDeliveriesPerDay\n      };\n\n      this.submitted.emit(driverData);\n    } else {\n      // Mark all fields as touched to show validation errors\n      this.markFormGroupTouched(this.driverForm);\n    }\n  }\n\n  private markFormGroupTouched(formGroup: any) {\n    Object.keys(formGroup.controls).forEach(field => {\n      const control = formGroup.get(field);\n      control?.markAsTouched({ onlySelf: true });\n    });\n  }\n\n  onCancel() {\n    this.cancelled.emit();\n  }\n}\n", "<form [formGroup]=\"driverForm\" (ngSubmit)=\"onSubmit()\">\n  <!-- Validation Summary -->\n  <div *ngIf=\"driverForm.invalid && driverForm.touched\" class=\"alert alert-danger mb-3\">\n    <h6 class=\"alert-heading\">\n      <i class=\"fa-solid fa-exclamation-triangle me-2\"></i>\n      <PERSON><PERSON><PERSON>z corriger les erreurs suivantes:\n    </h6>\n    <ul class=\"mb-0\">\n      <li *ngIf=\"driverForm.get('name')?.invalid && driverForm.get('name')?.touched\">\n        Le nom est requis\n      </li>\n      <li *ngIf=\"driverForm.get('email')?.invalid && driverForm.get('email')?.touched\">\n        Un email valide est requis\n      </li>\n      <li *ngIf=\"driverForm.get('phone')?.invalid && driverForm.get('phone')?.touched\">\n        Le numéro de téléphone est requis\n      </li>\n      <li *ngIf=\"driverForm.get('vehicleType')?.invalid && driverForm.get('vehicleType')?.touched\">\n        Le type de véhicule est requis\n      </li>\n      <li *ngIf=\"driverForm.get('maxDeliveriesPerDay')?.invalid && driverForm.get('maxDeliveriesPerDay')?.touched\">\n        Le nombre de livraisons par jour doit être entre 1 et 50\n      </li>\n      <li *ngIf=\"driverForm.get('latitude')?.invalid && driverForm.get('latitude')?.touched\">\n        La latitude doit être entre -90 et 90\n      </li>\n      <li *ngIf=\"driverForm.get('longitude')?.invalid && driverForm.get('longitude')?.touched\">\n        La longitude doit être entre -180 et 180\n      </li>\n    </ul>\n  </div>\n\n  <div class=\"row\">\n    <!-- Left Column -->\n    <div class=\"col-md-6\">\n      <div class=\"mb-3\">\n        <label for=\"name\" class=\"form-label\">Nom complet *</label>\n        <input type=\"text\" class=\"form-control\" id=\"name\" formControlName=\"name\"\n               [class.is-invalid]=\"driverForm.get('name')?.invalid && driverForm.get('name')?.touched\">\n        <div class=\"invalid-feedback\">\n          Le nom est requis\n        </div>\n      </div>\n\n      <div class=\"mb-3\">\n        <label for=\"email\" class=\"form-label\">Email *</label>\n        <input type=\"email\" class=\"form-control\" id=\"email\" formControlName=\"email\"\n               [class.is-invalid]=\"driverForm.get('email')?.invalid && driverForm.get('email')?.touched\">\n        <div class=\"invalid-feedback\">\n          Un email valide est requis\n        </div>\n      </div>\n\n      <div class=\"mb-3\">\n        <label for=\"phone\" class=\"form-label\">Téléphone *</label>\n        <input type=\"tel\" class=\"form-control\" id=\"phone\" formControlName=\"phone\"\n               [class.is-invalid]=\"driverForm.get('phone')?.invalid && driverForm.get('phone')?.touched\">\n        <div class=\"invalid-feedback\">\n          Le numéro de téléphone est requis\n        </div>\n      </div>\n\n      <div class=\"mb-3\">\n        <label for=\"vehicleType\" class=\"form-label\">Type de véhicule *</label>\n        <select class=\"form-select\" id=\"vehicleType\" formControlName=\"vehicleType\"\n                [class.is-invalid]=\"driverForm.get('vehicleType')?.invalid && driverForm.get('vehicleType')?.touched\">\n          <option value=\"\">Sélectionner un type de véhicule</option>\n          <option *ngFor=\"let option of vehicleTypeOptions\" [value]=\"option.value\">\n            {{ option.label }}\n          </option>\n        </select>\n        <div class=\"invalid-feedback\">\n          Le type de véhicule est requis\n        </div>\n      </div>\n\n      <div class=\"mb-3\">\n        <label for=\"vehicleId\" class=\"form-label\">Immatriculation du véhicule</label>\n        <input type=\"text\" class=\"form-control\" id=\"vehicleId\" formControlName=\"vehicleId\"\n               placeholder=\"Ex: AB-123-CD\">\n      </div>\n\n      <div class=\"mb-3\">\n        <label for=\"status\" class=\"form-label\">Statut *</label>\n        <select class=\"form-select\" id=\"status\" formControlName=\"status\">\n          <option *ngFor=\"let option of statusOptions\" [value]=\"option.value\">\n            {{ option.label }}\n          </option>\n        </select>\n      </div>\n    </div>\n\n    <!-- Right Column -->\n    <div class=\"col-md-6\">\n      <div class=\"mb-3\">\n        <label for=\"licenseNumber\" class=\"form-label\">Numéro de permis</label>\n        <input type=\"text\" class=\"form-control\" id=\"licenseNumber\" formControlName=\"licenseNumber\"\n               placeholder=\"Ex: 123456789\">\n      </div>\n\n      <div class=\"mb-3\">\n        <label for=\"licenseExpiryDate\" class=\"form-label\">Date d'expiration du permis</label>\n        <input type=\"date\" class=\"form-control\" id=\"licenseExpiryDate\" formControlName=\"licenseExpiryDate\">\n      </div>\n\n      <div class=\"mb-3\">\n        <label for=\"maxDeliveriesPerDay\" class=\"form-label\">Livraisons max par jour *</label>\n        <input type=\"number\" class=\"form-control\" id=\"maxDeliveriesPerDay\" formControlName=\"maxDeliveriesPerDay\"\n               min=\"1\" max=\"50\" [class.is-invalid]=\"driverForm.get('maxDeliveriesPerDay')?.invalid && driverForm.get('maxDeliveriesPerDay')?.touched\">\n        <div class=\"invalid-feedback\">\n          Nombre entre 1 et 50 requis\n        </div>\n      </div>\n\n      <div class=\"mb-3\">\n        <label class=\"form-label\">Position actuelle</label>\n        <div class=\"row\">\n          <div class=\"col-6\">\n            <input type=\"number\" class=\"form-control\" placeholder=\"Latitude\" formControlName=\"latitude\"\n                   step=\"0.000001\" [class.is-invalid]=\"driverForm.get('latitude')?.invalid && driverForm.get('latitude')?.touched\">\n            <div class=\"invalid-feedback\">\n              Latitude invalide\n            </div>\n          </div>\n          <div class=\"col-6\">\n            <input type=\"number\" class=\"form-control\" placeholder=\"Longitude\" formControlName=\"longitude\"\n                   step=\"0.000001\" [class.is-invalid]=\"driverForm.get('longitude')?.invalid && driverForm.get('longitude')?.touched\">\n            <div class=\"invalid-feedback\">\n              Longitude invalide\n            </div>\n          </div>\n        </div>\n        <small class=\"form-text text-muted\">\n          Position par défaut: Paris (48.8566, 2.3522)\n        </small>\n      </div>\n\n      <div class=\"mb-3\">\n        <label for=\"preferredZones\" class=\"form-label\">Zones préférées</label>\n        <textarea class=\"form-control\" id=\"preferredZones\" formControlName=\"preferredZones\" rows=\"2\"\n                  placeholder=\"Ex: Centre-ville, Banlieue nord...\"></textarea>\n      </div>\n\n      <div class=\"mb-3\">\n        <div class=\"form-check\">\n          <input class=\"form-check-input\" type=\"checkbox\" id=\"isAvailableForUrgentDeliveries\"\n                 formControlName=\"isAvailableForUrgentDeliveries\">\n          <label class=\"form-check-label\" for=\"isAvailableForUrgentDeliveries\">\n            Disponible pour les livraisons urgentes\n          </label>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <div class=\"d-flex gap-2 justify-content-end\">\n    <button type=\"button\" class=\"btn btn-secondary\" (click)=\"onCancel()\">Annuler</button>\n    <button type=\"submit\" class=\"btn btn-primary\" [disabled]=\"!driverForm.valid\">\n      <i class=\"fa-solid fa-save me-2\"></i>\n      {{ driver ? 'Mettre à jour' : 'Créer le livreur' }}\n    </button>\n  </div>\n</form>\n"], "mappings": "AAAA,SAAmCA,YAAY,QAAgB,eAAe;AAC9E,SAAiCC,UAAU,QAAQ,gBAAgB;;;;;;ICO7DC,EAAA,CAAAC,cAAA,SAA+E;IAC7ED,EAAA,CAAAE,MAAA,0BACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IACLH,EAAA,CAAAC,cAAA,SAAiF;IAC/ED,EAAA,CAAAE,MAAA,mCACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IACLH,EAAA,CAAAC,cAAA,SAAiF;IAC/ED,EAAA,CAAAE,MAAA,yDACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IACLH,EAAA,CAAAC,cAAA,SAA6F;IAC3FD,EAAA,CAAAE,MAAA,4CACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IACLH,EAAA,CAAAC,cAAA,SAA6G;IAC3GD,EAAA,CAAAE,MAAA,sEACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IACLH,EAAA,CAAAC,cAAA,SAAuF;IACrFD,EAAA,CAAAE,MAAA,mDACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IACLH,EAAA,CAAAC,cAAA,SAAyF;IACvFD,EAAA,CAAAE,MAAA,sDACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IA1BTH,EAAA,CAAAC,cAAA,cAAsF;IAElFD,EAAA,CAAAI,SAAA,YAAqD;IACrDJ,EAAA,CAAAE,MAAA,iDACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,aAAiB;IACfD,EAAA,CAAAK,UAAA,IAAAC,uCAAA,iBAEK;IACLN,EAAA,CAAAK,UAAA,IAAAE,uCAAA,iBAEK;IACLP,EAAA,CAAAK,UAAA,IAAAG,uCAAA,iBAEK;IACLR,EAAA,CAAAK,UAAA,IAAAI,uCAAA,iBAEK;IACLT,EAAA,CAAAK,UAAA,IAAAK,uCAAA,iBAEK;IACLV,EAAA,CAAAK,UAAA,KAAAM,wCAAA,iBAEK;IACLX,EAAA,CAAAK,UAAA,KAAAO,wCAAA,iBAEK;IACPZ,EAAA,CAAAG,YAAA,EAAK;;;;;;;;;;;IArBEH,EAAA,CAAAa,SAAA,GAAwE;IAAxEb,EAAA,CAAAc,UAAA,WAAAC,OAAA,GAAAC,MAAA,CAAAC,UAAA,CAAAC,GAAA,2BAAAH,OAAA,CAAAI,OAAA,OAAAJ,OAAA,GAAAC,MAAA,CAAAC,UAAA,CAAAC,GAAA,2BAAAH,OAAA,CAAAK,OAAA,EAAwE;IAGxEpB,EAAA,CAAAa,SAAA,GAA0E;IAA1Eb,EAAA,CAAAc,UAAA,WAAAO,OAAA,GAAAL,MAAA,CAAAC,UAAA,CAAAC,GAAA,4BAAAG,OAAA,CAAAF,OAAA,OAAAE,OAAA,GAAAL,MAAA,CAAAC,UAAA,CAAAC,GAAA,4BAAAG,OAAA,CAAAD,OAAA,EAA0E;IAG1EpB,EAAA,CAAAa,SAAA,GAA0E;IAA1Eb,EAAA,CAAAc,UAAA,WAAAQ,OAAA,GAAAN,MAAA,CAAAC,UAAA,CAAAC,GAAA,4BAAAI,OAAA,CAAAH,OAAA,OAAAG,OAAA,GAAAN,MAAA,CAAAC,UAAA,CAAAC,GAAA,4BAAAI,OAAA,CAAAF,OAAA,EAA0E;IAG1EpB,EAAA,CAAAa,SAAA,GAAsF;IAAtFb,EAAA,CAAAc,UAAA,WAAAS,OAAA,GAAAP,MAAA,CAAAC,UAAA,CAAAC,GAAA,kCAAAK,OAAA,CAAAJ,OAAA,OAAAI,OAAA,GAAAP,MAAA,CAAAC,UAAA,CAAAC,GAAA,kCAAAK,OAAA,CAAAH,OAAA,EAAsF;IAGtFpB,EAAA,CAAAa,SAAA,GAAsG;IAAtGb,EAAA,CAAAc,UAAA,WAAAU,OAAA,GAAAR,MAAA,CAAAC,UAAA,CAAAC,GAAA,0CAAAM,OAAA,CAAAL,OAAA,OAAAK,OAAA,GAAAR,MAAA,CAAAC,UAAA,CAAAC,GAAA,0CAAAM,OAAA,CAAAJ,OAAA,EAAsG;IAGtGpB,EAAA,CAAAa,SAAA,GAAgF;IAAhFb,EAAA,CAAAc,UAAA,WAAAW,OAAA,GAAAT,MAAA,CAAAC,UAAA,CAAAC,GAAA,+BAAAO,OAAA,CAAAN,OAAA,OAAAM,OAAA,GAAAT,MAAA,CAAAC,UAAA,CAAAC,GAAA,+BAAAO,OAAA,CAAAL,OAAA,EAAgF;IAGhFpB,EAAA,CAAAa,SAAA,GAAkF;IAAlFb,EAAA,CAAAc,UAAA,WAAAY,OAAA,GAAAV,MAAA,CAAAC,UAAA,CAAAC,GAAA,gCAAAQ,OAAA,CAAAP,OAAA,OAAAO,OAAA,GAAAV,MAAA,CAAAC,UAAA,CAAAC,GAAA,gCAAAQ,OAAA,CAAAN,OAAA,EAAkF;;;;;IAyCnFpB,EAAA,CAAAC,cAAA,iBAAyE;IACvED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFyCH,EAAA,CAAAc,UAAA,UAAAa,UAAA,CAAAC,KAAA,CAAsB;IACtE5B,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA6B,kBAAA,MAAAF,UAAA,CAAAG,KAAA,MACF;;;;;IAgBA9B,EAAA,CAAAC,cAAA,iBAAoE;IAClED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFoCH,EAAA,CAAAc,UAAA,UAAAiB,UAAA,CAAAH,KAAA,CAAsB;IACjE5B,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAA6B,kBAAA,MAAAE,UAAA,CAAAD,KAAA,MACF;;;AD9EV,OAAM,MAAOE,mBAAmB;EAwB9BC,YAAoBC,EAAe;IAAf,KAAAA,EAAE,GAAFA,EAAE;IAvBb,KAAAC,MAAM,GAAkB,IAAI;IAC3B,KAAAC,SAAS,GAAG,IAAItC,YAAY,EAAO;IACnC,KAAAuC,SAAS,GAAG,IAAIvC,YAAY,EAAQ;IAI9C;IACA,KAAAwC,kBAAkB,GAAG,CACnB;MAAEV,KAAK,EAAE,SAAS;MAAEE,KAAK,EAAE;IAAS,CAAE,EACtC;MAAEF,KAAK,EAAE,MAAM;MAAEE,KAAK,EAAE;IAAM,CAAE,EAChC;MAAEF,KAAK,EAAE,MAAM;MAAEE,KAAK,EAAE;IAAM,CAAE,EAChC;MAAEF,KAAK,EAAE,aAAa;MAAEE,KAAK,EAAE;IAAa,CAAE,EAC9C;MAAEF,KAAK,EAAE,QAAQ;MAAEE,KAAK,EAAE;IAAQ,CAAE,CACrC;IAED;IACA,KAAAS,aAAa,GAAG,CACd;MAAEX,KAAK,EAAE,CAAC;MAAEE,KAAK,EAAE;IAAY,CAAE,EACjC;MAAEF,KAAK,EAAE,CAAC;MAAEE,KAAK,EAAE;IAAQ,CAAE,EAC7B;MAAEF,KAAK,EAAE,CAAC;MAAEE,KAAK,EAAE;IAAU,CAAE,EAC/B;MAAEF,KAAK,EAAE,CAAC;MAAEE,KAAK,EAAE;IAAY,CAAE,CAClC;IAGC,IAAI,CAACb,UAAU,GAAG,IAAI,CAACiB,EAAE,CAACM,KAAK,CAAC;MAC9BC,IAAI,EAAE,CAAC,EAAE,EAAE1C,UAAU,CAAC2C,QAAQ,CAAC;MAC/BC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC5C,UAAU,CAAC2C,QAAQ,EAAE3C,UAAU,CAAC4C,KAAK,CAAC,CAAC;MACpDC,KAAK,EAAE,CAAC,EAAE,EAAE7C,UAAU,CAAC2C,QAAQ,CAAC;MAChCG,WAAW,EAAE,CAAC,EAAE,EAAE9C,UAAU,CAAC2C,QAAQ,CAAC;MACtCI,SAAS,EAAE,CAAC,EAAE,CAAC;MACfC,aAAa,EAAE,CAAC,EAAE,CAAC;MACnBC,iBAAiB,EAAE,CAAC,EAAE,CAAC;MACvBC,MAAM,EAAE,CAAC,CAAC,EAAElD,UAAU,CAAC2C,QAAQ,CAAC;MAChCQ,8BAA8B,EAAE,CAAC,IAAI,CAAC;MACtCC,mBAAmB,EAAE,CAAC,EAAE,EAAE,CAACpD,UAAU,CAAC2C,QAAQ,EAAE3C,UAAU,CAACqD,GAAG,CAAC,CAAC,CAAC,EAAErD,UAAU,CAACsD,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;MACvFC,cAAc,EAAE,CAAC,EAAE,CAAC;MACpBC,QAAQ,EAAE,CAAC,OAAO,EAAE,CAACxD,UAAU,CAAC2C,QAAQ,EAAE3C,UAAU,CAACqD,GAAG,CAAC,CAAC,EAAE,CAAC,EAAErD,UAAU,CAACsD,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;MACnFG,SAAS,EAAE,CAAC,MAAM,EAAE,CAACzD,UAAU,CAAC2C,QAAQ,EAAE3C,UAAU,CAACqD,GAAG,CAAC,CAAC,GAAG,CAAC,EAAErD,UAAU,CAACsD,GAAG,CAAC,GAAG,CAAC,CAAC;KACrF,CAAC;EACJ;EAEAI,QAAQA,CAAA;IACN,IAAI,IAAI,CAACtB,MAAM,EAAE;MACf,IAAI,CAAClB,UAAU,CAACyC,UAAU,CAAC;QACzBjB,IAAI,EAAE,IAAI,CAACN,MAAM,CAACM,IAAI;QACtBE,KAAK,EAAE,IAAI,CAACR,MAAM,CAACQ,KAAK;QACxBC,KAAK,EAAE,IAAI,CAACT,MAAM,CAACS,KAAK;QACxBC,WAAW,EAAE,IAAI,CAACV,MAAM,CAACU,WAAW;QACpCC,SAAS,EAAE,IAAI,CAACX,MAAM,CAACW,SAAS,IAAI,EAAE;QACtCC,aAAa,EAAE,IAAI,CAACZ,MAAM,CAACY,aAAa,IAAI,EAAE;QAC9CC,iBAAiB,EAAE,IAAI,CAACb,MAAM,CAACa,iBAAiB,GAAG,IAAI,CAACW,kBAAkB,CAAC,IAAI,CAACxB,MAAM,CAACa,iBAAiB,CAAC,GAAG,EAAE;QAC9GC,MAAM,EAAE,IAAI,CAACd,MAAM,CAACc,MAAM;QAC1BC,8BAA8B,EAAE,IAAI,CAACf,MAAM,CAACe,8BAA8B;QAC1EC,mBAAmB,EAAE,IAAI,CAAChB,MAAM,CAACgB,mBAAmB;QACpDG,cAAc,EAAE,IAAI,CAACnB,MAAM,CAACmB,cAAc,IAAI,EAAE;QAChDC,QAAQ,EAAE,IAAI,CAACpB,MAAM,CAACyB,eAAe,EAAEL,QAAQ,IAAI,OAAO;QAC1DC,SAAS,EAAE,IAAI,CAACrB,MAAM,CAACyB,eAAe,EAAEJ,SAAS,IAAI;OACtD,CAAC;;EAEN;EAEQG,kBAAkBA,CAACE,IAAmB;IAC5C,MAAMC,CAAC,GAAG,IAAIC,IAAI,CAACF,IAAI,CAAC;IACxB,OAAOC,CAAC,CAACE,WAAW,EAAE,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;EACvC;;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACjD,UAAU,CAACkD,KAAK,EAAE;MACzB,MAAMC,SAAS,GAAG,IAAI,CAACnD,UAAU,CAACW,KAAK;MAEvC;MACA,MAAMyC,UAAU,GAAG;QACjB5B,IAAI,EAAE2B,SAAS,CAAC3B,IAAI;QACpBE,KAAK,EAAEyB,SAAS,CAACzB,KAAK;QACtBC,KAAK,EAAEwB,SAAS,CAACxB,KAAK;QACtBC,WAAW,EAAEuB,SAAS,CAACvB,WAAW;QAClCC,SAAS,EAAEsB,SAAS,CAACtB,SAAS,IAAI,IAAI;QACtCG,MAAM,EAAEmB,SAAS,CAACnB,MAAM;QACxBW,eAAe,EAAE;UACfL,QAAQ,EAAEe,UAAU,CAACF,SAAS,CAACb,QAAQ,CAAC;UACxCC,SAAS,EAAEc,UAAU,CAACF,SAAS,CAACZ,SAAS;SAC1C;QACDe,iBAAiB,EAAE,IAAI;QACvBxB,aAAa,EAAEqB,SAAS,CAACrB,aAAa,IAAI,IAAI;QAC9CC,iBAAiB,EAAEoB,SAAS,CAACpB,iBAAiB,GAAGoB,SAAS,CAACpB,iBAAiB,GAAG,IAAI;QACnFE,8BAA8B,EAAEkB,SAAS,CAAClB,8BAA8B;QACxEI,cAAc,EAAEc,SAAS,CAACd,cAAc,IAAI,IAAI;QAChDH,mBAAmB,EAAEiB,SAAS,CAACjB;OAChC;MAED,IAAI,CAACf,SAAS,CAACoC,IAAI,CAACH,UAAU,CAAC;KAChC,MAAM;MACL;MACA,IAAI,CAACI,oBAAoB,CAAC,IAAI,CAACxD,UAAU,CAAC;;EAE9C;EAEQwD,oBAAoBA,CAACC,SAAc;IACzCC,MAAM,CAACC,IAAI,CAACF,SAAS,CAACG,QAAQ,CAAC,CAACC,OAAO,CAACC,KAAK,IAAG;MAC9C,MAAMC,OAAO,GAAGN,SAAS,CAACxD,GAAG,CAAC6D,KAAK,CAAC;MACpCC,OAAO,EAAEC,aAAa,CAAC;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;IAC5C,CAAC,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAAC9C,SAAS,CAACmC,IAAI,EAAE;EACvB;;;uBA3GWxC,mBAAmB,EAAAhC,EAAA,CAAAoF,iBAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAnBtD,mBAAmB;MAAAuD,SAAA;MAAAC,MAAA;QAAArD,MAAA;MAAA;MAAAsD,OAAA;QAAArD,SAAA;QAAAC,SAAA;MAAA;MAAAqD,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCThC/F,EAAA,CAAAC,cAAA,cAAuD;UAAxBD,EAAA,CAAAiG,UAAA,sBAAAC,sDAAA;YAAA,OAAYF,GAAA,CAAA9B,QAAA,EAAU;UAAA,EAAC;UAEpDlE,EAAA,CAAAK,UAAA,IAAA8F,kCAAA,kBA4BM;UAENnG,EAAA,CAAAC,cAAA,aAAiB;UAI0BD,EAAA,CAAAE,MAAA,oBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC1DH,EAAA,CAAAI,SAAA,eAC+F;UAC/FJ,EAAA,CAAAC,cAAA,aAA8B;UAC5BD,EAAA,CAAAE,MAAA,0BACF;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAGRH,EAAA,CAAAC,cAAA,cAAkB;UACsBD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACrDH,EAAA,CAAAI,SAAA,gBACiG;UACjGJ,EAAA,CAAAC,cAAA,cAA8B;UAC5BD,EAAA,CAAAE,MAAA,oCACF;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAGRH,EAAA,CAAAC,cAAA,cAAkB;UACsBD,EAAA,CAAAE,MAAA,6BAAW;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACzDH,EAAA,CAAAI,SAAA,iBACiG;UACjGJ,EAAA,CAAAC,cAAA,cAA8B;UAC5BD,EAAA,CAAAE,MAAA,0DACF;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAGRH,EAAA,CAAAC,cAAA,cAAkB;UAC4BD,EAAA,CAAAE,MAAA,+BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtEH,EAAA,CAAAC,cAAA,kBAC8G;UAC3FD,EAAA,CAAAE,MAAA,kDAAgC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC1DH,EAAA,CAAAK,UAAA,KAAA+F,sCAAA,qBAES;UACXpG,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,cAA8B;UAC5BD,EAAA,CAAAE,MAAA,6CACF;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAGRH,EAAA,CAAAC,cAAA,cAAkB;UAC0BD,EAAA,CAAAE,MAAA,wCAA2B;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC7EH,EAAA,CAAAI,SAAA,iBACmC;UACrCJ,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,cAAkB;UACuBD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvDH,EAAA,CAAAC,cAAA,kBAAiE;UAC/DD,EAAA,CAAAK,UAAA,KAAAgG,sCAAA,qBAES;UACXrG,EAAA,CAAAG,YAAA,EAAS;UAKbH,EAAA,CAAAC,cAAA,cAAsB;UAE4BD,EAAA,CAAAE,MAAA,6BAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtEH,EAAA,CAAAI,SAAA,iBACmC;UACrCJ,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,cAAkB;UACkCD,EAAA,CAAAE,MAAA,mCAA2B;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACrFH,EAAA,CAAAI,SAAA,iBAAmG;UACrGJ,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,cAAkB;UACoCD,EAAA,CAAAE,MAAA,iCAAyB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACrFH,EAAA,CAAAI,SAAA,iBAC8I;UAC9IJ,EAAA,CAAAC,cAAA,cAA8B;UAC5BD,EAAA,CAAAE,MAAA,qCACF;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAGRH,EAAA,CAAAC,cAAA,cAAkB;UACUD,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACnDH,EAAA,CAAAC,cAAA,cAAiB;UAEbD,EAAA,CAAAI,SAAA,iBACuH;UACvHJ,EAAA,CAAAC,cAAA,cAA8B;UAC5BD,EAAA,CAAAE,MAAA,2BACF;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAERH,EAAA,CAAAC,cAAA,eAAmB;UACjBD,EAAA,CAAAI,SAAA,iBACyH;UACzHJ,EAAA,CAAAC,cAAA,cAA8B;UAC5BD,EAAA,CAAAE,MAAA,4BACF;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAGVH,EAAA,CAAAC,cAAA,iBAAoC;UAClCD,EAAA,CAAAE,MAAA,2DACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAGVH,EAAA,CAAAC,cAAA,cAAkB;UAC+BD,EAAA,CAAAE,MAAA,sCAAe;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtEH,EAAA,CAAAI,SAAA,oBACsE;UACxEJ,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,cAAkB;UAEdD,EAAA,CAAAI,SAAA,iBACwD;UACxDJ,EAAA,CAAAC,cAAA,iBAAqE;UACnED,EAAA,CAAAE,MAAA,iDACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAMhBH,EAAA,CAAAC,cAAA,eAA8C;UACID,EAAA,CAAAiG,UAAA,mBAAAK,sDAAA;YAAA,OAASN,GAAA,CAAAb,QAAA,EAAU;UAAA,EAAC;UAACnF,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACrFH,EAAA,CAAAC,cAAA,kBAA6E;UAC3ED,EAAA,CAAAI,SAAA,aAAqC;UACrCJ,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;;;;;UAhKPH,EAAA,CAAAc,UAAA,cAAAkF,GAAA,CAAA/E,UAAA,CAAwB;UAEtBjB,EAAA,CAAAa,SAAA,GAA8C;UAA9Cb,EAAA,CAAAc,UAAA,SAAAkF,GAAA,CAAA/E,UAAA,CAAAE,OAAA,IAAA6E,GAAA,CAAA/E,UAAA,CAAAG,OAAA,CAA8C;UAoCvCpB,EAAA,CAAAa,SAAA,GAAuF;UAAvFb,EAAA,CAAAuG,WAAA,iBAAAjF,OAAA,GAAA0E,GAAA,CAAA/E,UAAA,CAAAC,GAAA,2BAAAI,OAAA,CAAAH,OAAA,OAAAG,OAAA,GAAA0E,GAAA,CAAA/E,UAAA,CAAAC,GAAA,2BAAAI,OAAA,CAAAF,OAAA,EAAuF;UASvFpB,EAAA,CAAAa,SAAA,GAAyF;UAAzFb,EAAA,CAAAuG,WAAA,iBAAAhF,OAAA,GAAAyE,GAAA,CAAA/E,UAAA,CAAAC,GAAA,4BAAAK,OAAA,CAAAJ,OAAA,OAAAI,OAAA,GAAAyE,GAAA,CAAA/E,UAAA,CAAAC,GAAA,4BAAAK,OAAA,CAAAH,OAAA,EAAyF;UASzFpB,EAAA,CAAAa,SAAA,GAAyF;UAAzFb,EAAA,CAAAuG,WAAA,iBAAA/E,OAAA,GAAAwE,GAAA,CAAA/E,UAAA,CAAAC,GAAA,4BAAAM,OAAA,CAAAL,OAAA,OAAAK,OAAA,GAAAwE,GAAA,CAAA/E,UAAA,CAAAC,GAAA,4BAAAM,OAAA,CAAAJ,OAAA,EAAyF;UASxFpB,EAAA,CAAAa,SAAA,GAAqG;UAArGb,EAAA,CAAAuG,WAAA,iBAAA9E,OAAA,GAAAuE,GAAA,CAAA/E,UAAA,CAAAC,GAAA,kCAAAO,OAAA,CAAAN,OAAA,OAAAM,OAAA,GAAAuE,GAAA,CAAA/E,UAAA,CAAAC,GAAA,kCAAAO,OAAA,CAAAL,OAAA,EAAqG;UAEhFpB,EAAA,CAAAa,SAAA,GAAqB;UAArBb,EAAA,CAAAc,UAAA,YAAAkF,GAAA,CAAA1D,kBAAA,CAAqB;UAkBrBtC,EAAA,CAAAa,SAAA,IAAgB;UAAhBb,EAAA,CAAAc,UAAA,YAAAkF,GAAA,CAAAzD,aAAA,CAAgB;UAuBrBvC,EAAA,CAAAa,SAAA,IAAqH;UAArHb,EAAA,CAAAuG,WAAA,iBAAAC,OAAA,GAAAR,GAAA,CAAA/E,UAAA,CAAAC,GAAA,0CAAAsF,OAAA,CAAArF,OAAA,OAAAqF,OAAA,GAAAR,GAAA,CAAA/E,UAAA,CAAAC,GAAA,0CAAAsF,OAAA,CAAApF,OAAA,EAAqH;UAWlHpB,EAAA,CAAAa,SAAA,GAA+F;UAA/Fb,EAAA,CAAAuG,WAAA,iBAAAE,OAAA,GAAAT,GAAA,CAAA/E,UAAA,CAAAC,GAAA,+BAAAuF,OAAA,CAAAtF,OAAA,OAAAsF,OAAA,GAAAT,GAAA,CAAA/E,UAAA,CAAAC,GAAA,+BAAAuF,OAAA,CAAArF,OAAA,EAA+F;UAO/FpB,EAAA,CAAAa,SAAA,GAAiG;UAAjGb,EAAA,CAAAuG,WAAA,iBAAAG,QAAA,GAAAV,GAAA,CAAA/E,UAAA,CAAAC,GAAA,gCAAAwF,QAAA,CAAAvF,OAAA,OAAAuF,QAAA,GAAAV,GAAA,CAAA/E,UAAA,CAAAC,GAAA,gCAAAwF,QAAA,CAAAtF,OAAA,EAAiG;UA+BlFpB,EAAA,CAAAa,SAAA,IAA8B;UAA9Bb,EAAA,CAAAc,UAAA,cAAAkF,GAAA,CAAA/E,UAAA,CAAAkD,KAAA,CAA8B;UAE1EnE,EAAA,CAAAa,SAAA,GACF;UADEb,EAAA,CAAA6B,kBAAA,MAAAmE,GAAA,CAAA7D,MAAA,uDACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}