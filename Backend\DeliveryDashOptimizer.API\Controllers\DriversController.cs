using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Security.Claims;
using System.Linq;
using System.ComponentModel.DataAnnotations;
using DeliveryDashOptimizer.API.Models;
using DeliveryDashOptimizer.API.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;

namespace DeliveryDashOptimizer.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class DriversController : ControllerBase
    {
        private readonly IDriverService _driverService;
        private readonly IDeliveryService _deliveryService;
        private readonly ILogger<DriversController> _logger;

        public DriversController(IDriverService driverService, IDeliveryService deliveryService, ILogger<DriversController> logger)
        {
            _driverService = driverService;
            _deliveryService = deliveryService;
            _logger = logger;
        }

        [HttpGet]
        [Authorize(Roles = "Admin,Manager,Dispatcher")]
        public async Task<ActionResult<IEnumerable<Driver>>> GetDrivers()
        {
            try
            {
                var drivers = await _driverService.GetAllDriversAsync();
                return Ok(drivers);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving drivers");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("{id}")]
        [Authorize(Roles = "Admin,Manager,Dispatcher,Driver")]
        public async Task<ActionResult<Driver>> GetDriver(string id)
        {
            try
            {
                var driver = await _driverService.GetDriverByIdAsync(id);
                if (driver == null)
                {
                    return NotFound();
                }
                return Ok(driver);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error retrieving driver with ID {id}");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("status/{status}")]
        [Authorize(Roles = "Admin,Manager,Dispatcher")]
        public async Task<ActionResult<IEnumerable<Driver>>> GetDriversByStatus(string status)
        {
            try
            {
                if (!Enum.TryParse<DriverStatus>(status, true, out var driverStatus))
                {
                    return BadRequest("Invalid status");
                }

                var drivers = await _driverService.GetDriversByStatusAsync(driverStatus);
                return Ok(drivers);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error retrieving drivers with status {status}");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPost]
        [Authorize(Roles = "Admin,Manager")]
        public async Task<ActionResult<Driver>> CreateDriver([FromBody] CreateDriverRequest request)
        {
            try
            {
                // Log the incoming request for debugging
                _logger.LogInformation("CreateDriver request received: {@Request}", request);

                // Validate the request
                if (!ModelState.IsValid)
                {
                    _logger.LogWarning("ModelState validation failed: {@ModelState}", ModelState);
                    return BadRequest(ModelState);
                }

                // Create driver from request
                var driver = new Driver
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = request.Name,
                    Email = request.Email,
                    Phone = request.Phone,
                    VehicleType = request.VehicleType,
                    VehicleId = request.VehicleId,
                    Rating = 0,
                    TotalDeliveries = 0,
                    OnTimeRate = 0,
                    Status = request.Status,
                    CurrentLocation = request.CurrentLocation,
                    TodayDeliveries = 0,
                    AvgDeliveryTime = 0,
                    LastActive = DateTime.UtcNow,
                    ProfilePictureUrl = request.ProfilePictureUrl,
                    HireDate = DateTime.UtcNow,
                    LicenseNumber = request.LicenseNumber,
                    LicenseExpiryDate = request.LicenseExpiryDate ?? DateTime.UtcNow.AddYears(5),
                    IsAvailableForUrgentDeliveries = request.IsAvailableForUrgentDeliveries,
                    PreferredZones = request.PreferredZones,
                    MaxDeliveriesPerDay = request.MaxDeliveriesPerDay
                };

                var createdDriver = await _driverService.CreateDriverAsync(driver);
                return CreatedAtAction(nameof(GetDriver), new { id = createdDriver.Id }, createdDriver);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating driver");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPut("{id}")]
        [Authorize(Roles = "Admin,Manager,Driver")]
        public async Task<IActionResult> UpdateDriver(string id, [FromBody] Driver driver)
        {
            if (id != driver.Id)
            {
                return BadRequest();
            }

            try
            {
                await _driverService.UpdateDriverAsync(driver);
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error updating driver with ID {id}");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpDelete("{id}")]
        [Authorize(Roles = "Admin,Manager")]
        public async Task<IActionResult> DeleteDriver(string id)
        {
            try
            {
                await _driverService.DeleteDriverAsync(id);
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error deleting driver with ID {id}");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPut("{id}/status")]
        [Authorize(Roles = "Admin,Manager,Dispatcher,Driver")]
        public async Task<ActionResult<Driver>> UpdateDriverStatus(string id, [FromBody] UpdateDriverStatusRequest request)
        {
            try
            {
                var driver = await _driverService.UpdateDriverStatusAsync(id, request.Status);
                if (driver == null)
                {
                    return NotFound();
                }
                return Ok(driver);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error updating driver status for ID {id}");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPut("{id}/location")]
        [Authorize(Roles = "Admin,Manager,Dispatcher,Driver")]
        public async Task<ActionResult<Driver>> UpdateDriverLocation(string id, [FromBody] UpdateDriverLocationRequest request)
        {
            try
            {
                var driver = await _driverService.UpdateDriverLocationAsync(id, request.Location);
                if (driver == null)
                {
                    return NotFound();
                }
                return Ok(driver);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error updating driver location for ID {id}");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("{id}/performance")]
        [Authorize(Roles = "Admin,Manager,Dispatcher,Driver")]
        public async Task<ActionResult<DriverPerformance>> GetDriverPerformance(string id)
        {
            try
            {
                var performance = await _driverService.GetDriverPerformanceAsync(id);
                if (performance == null)
                {
                    return NotFound();
                }
                return Ok(performance);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error retrieving driver performance for ID {id}");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("{id}/deliveries")]
        [Authorize(Roles = "Admin,Manager,Dispatcher,Driver")]
        public async Task<ActionResult<IEnumerable<Delivery>>> GetDriverDeliveries(string id)
        {
            try
            {
                var deliveries = await _deliveryService.GetDeliveriesByDriverIdAsync(id);
                return Ok(deliveries);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error retrieving deliveries for driver ID {id}");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("me")]
        [Authorize]
        public async Task<ActionResult<Driver>> GetCurrentDriver()
        {
            try
            {
                var userEmail = User.FindFirst(ClaimTypes.Email)?.Value;
                if (string.IsNullOrEmpty(userEmail))
                {
                    return BadRequest("User email not found in token");
                }

                // Find driver by email (assuming driver email matches user email)
                var drivers = await _driverService.GetAllDriversAsync();
                var driver = drivers.FirstOrDefault(d => d.Email == userEmail);

                if (driver == null)
                {
                    return NotFound("Driver profile not found");
                }

                return Ok(driver);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving current driver profile");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("me/deliveries")]
        [Authorize]
        public async Task<ActionResult<IEnumerable<Delivery>>> GetCurrentDriverDeliveries()
        {
            try
            {
                var userEmail = User.FindFirst(ClaimTypes.Email)?.Value;
                if (string.IsNullOrEmpty(userEmail))
                {
                    return BadRequest("User email not found in token");
                }

                // Find driver by email
                var drivers = await _driverService.GetAllDriversAsync();
                var driver = drivers.FirstOrDefault(d => d.Email == userEmail);

                if (driver == null)
                {
                    return NotFound("Driver profile not found");
                }

                var deliveries = await _deliveryService.GetDeliveriesByDriverIdAsync(driver.Id);
                return Ok(deliveries);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving current driver deliveries");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("available")]
        [Authorize(Roles = "Admin,Manager,Dispatcher")]
        public async Task<ActionResult<IEnumerable<Driver>>> GetAvailableDriversInArea([FromQuery] double latitude, [FromQuery] double longitude, [FromQuery] double radiusKm)
        {
            try
            {
                var drivers = await _driverService.GetAvailableDriversInAreaAsync(latitude, longitude, radiusKm);
                return Ok(drivers);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving available drivers in area");
                return StatusCode(500, "Internal server error");
            }
        }

    }

    public class CreateDriverRequest
    {
        [Required]
        public string Name { get; set; }

        [Required]
        [EmailAddress]
        public string Email { get; set; }

        [Required]
        public string Phone { get; set; }

        [Required]
        public string VehicleType { get; set; }

        public string? VehicleId { get; set; }

        [Required]
        public DriverStatus Status { get; set; }

        [Required]
        public GeoCoordinates CurrentLocation { get; set; }

        public string? ProfilePictureUrl { get; set; }

        public string? LicenseNumber { get; set; }

        public DateTime? LicenseExpiryDate { get; set; }

        public bool IsAvailableForUrgentDeliveries { get; set; } = true;

        public string? PreferredZones { get; set; }

        [Range(1, 50)]
        public int MaxDeliveriesPerDay { get; set; } = 20;
    }

    public class UpdateDriverStatusRequest
    {
        public DriverStatus Status { get; set; }
    }

    public class UpdateDriverLocationRequest
    {
        public GeoCoordinates Location { get; set; }
    }
}
