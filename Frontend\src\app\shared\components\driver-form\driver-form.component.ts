import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Driver, DriverStatus } from '@core/models/driver.model';

@Component({
  selector: 'app-driver-form',
  templateUrl: './driver-form.component.html',
  styleUrls: ['./driver-form.component.scss']
})
export class DriverFormComponent implements OnInit {
  @Input() driver: Driver | null = null;
  @Output() submitted = new EventEmitter<any>();
  @Output() cancelled = new EventEmitter<void>();

  driverForm: FormGroup;

  // Vehicle type options
  vehicleTypeOptions = [
    { value: 'Voiture', label: 'Voiture' },
    { value: 'Moto', label: 'Mo<PERSON>' },
    { value: 'Vélo', label: 'Vélo' },
    { value: '<PERSON>ionnette', label: '<PERSON>ionnette' },
    { value: 'Camion', label: 'Camion' }
  ];

  // Status options (backend expects integer enum values)
  statusOptions = [
    { value: 0, label: 'Disponible' },
    { value: 1, label: 'Occupé' },
    { value: 2, label: 'En pause' },
    { value: 3, label: 'Hors ligne' }
  ];

  constructor(private fb: FormBuilder) {
    this.driverForm = this.fb.group({
      name: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      phone: ['', Validators.required],
      vehicleType: ['', Validators.required],
      vehicleId: [''],
      licenseNumber: [''],
      licenseExpiryDate: [''],
      status: [0, Validators.required], // 0 = Available
      isAvailableForUrgentDeliveries: [true],
      maxDeliveriesPerDay: [20, [Validators.required, Validators.min(1), Validators.max(50)]],
      preferredZones: [''],
      latitude: [48.8566, [Validators.required, Validators.min(-90), Validators.max(90)]],
      longitude: [2.3522, [Validators.required, Validators.min(-180), Validators.max(180)]]
    });
  }

  ngOnInit() {
    if (this.driver) {
      this.driverForm.patchValue({
        name: this.driver.name,
        email: this.driver.email,
        phone: this.driver.phone,
        vehicleType: this.driver.vehicleType,
        vehicleId: this.driver.vehicleId || '',
        licenseNumber: this.driver.licenseNumber || '',
        licenseExpiryDate: this.driver.licenseExpiryDate ? this.formatDateForInput(this.driver.licenseExpiryDate) : '',
        status: this.driver.status,
        isAvailableForUrgentDeliveries: this.driver.isAvailableForUrgentDeliveries,
        maxDeliveriesPerDay: this.driver.maxDeliveriesPerDay,
        preferredZones: this.driver.preferredZones || '',
        latitude: this.driver.currentLocation?.latitude || 48.8566,
        longitude: this.driver.currentLocation?.longitude || 2.3522
      });
    }
  }

  private formatDateForInput(date: Date | string): string {
    const d = new Date(date);
    return d.toISOString().slice(0, 10); // Format for date input (YYYY-MM-DD)
  }

  onSubmit() {
    if (this.driverForm.valid) {
      const formValue = this.driverForm.value;

      // Map form data to CreateDriverRequest format expected by backend
      const driverData = {
        name: formValue.name,
        email: formValue.email,
        phone: formValue.phone,
        vehicleType: formValue.vehicleType,
        vehicleId: formValue.vehicleId || null,
        status: formValue.status,
        currentLocation: {
          latitude: parseFloat(formValue.latitude),
          longitude: parseFloat(formValue.longitude)
        },
        profilePictureUrl: null,
        licenseNumber: formValue.licenseNumber || null,
        licenseExpiryDate: formValue.licenseExpiryDate ? formValue.licenseExpiryDate : null,
        isAvailableForUrgentDeliveries: formValue.isAvailableForUrgentDeliveries,
        preferredZones: formValue.preferredZones || null,
        maxDeliveriesPerDay: formValue.maxDeliveriesPerDay
      };

      this.submitted.emit(driverData);
    } else {
      // Mark all fields as touched to show validation errors
      this.markFormGroupTouched(this.driverForm);
    }
  }

  private markFormGroupTouched(formGroup: any) {
    Object.keys(formGroup.controls).forEach(field => {
      const control = formGroup.get(field);
      control?.markAsTouched({ onlySelf: true });
    });
  }

  onCancel() {
    this.cancelled.emit();
  }
}
