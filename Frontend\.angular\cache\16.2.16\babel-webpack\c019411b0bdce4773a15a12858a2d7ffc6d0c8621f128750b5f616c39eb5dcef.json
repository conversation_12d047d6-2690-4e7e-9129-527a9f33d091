{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/delivery-dash-optimiser/Frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport { DeliveryStatus } from '../../core/models/delivery.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"../../core/services/delivery.service\";\nimport * as i4 from \"../../core/services/real-time.service\";\nimport * as i5 from \"../../core/services/auth.service\";\nimport * as i6 from \"@angular/common\";\nfunction CustomerPortalComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵelement(1, \"i\", 27);\n    i0.ɵɵelementStart(2, \"span\", 28);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r0.currentUser.firstName, \" \", ctx_r0.currentUser.lastName, \"\");\n  }\n}\nfunction CustomerPortalComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"div\", 30)(2, \"span\", 31);\n    i0.ɵɵtext(3, \"Chargement...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 32);\n    i0.ɵɵtext(5, \"Recherche de votre livraison...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CustomerPortalComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵelement(1, \"i\", 34);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.error, \" \");\n  }\n}\nfunction CustomerPortalComponent_div_33_div_1_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"div\", 79)(2, \"div\", 80);\n    i0.ɵɵelement(3, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h6\", 81);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const step_r13 = ctx.$implicit;\n    const i_r14 = ctx.index;\n    const ctx_r8 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"completed\", ctx_r8.isStatusCompleted(i_r14))(\"active\", ctx_r8.isStatusActive(i_r14));\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(step_r13.icon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"text-primary\", ctx_r8.isStatusActive(i_r14))(\"text-success\", ctx_r8.isStatusCompleted(i_r14) && !ctx_r8.isStatusActive(i_r14));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", step_r13.label, \" \");\n  }\n}\nfunction CustomerPortalComponent_div_33_div_1_div_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵelement(1, \"div\", 82);\n    i0.ɵɵelementStart(2, \"div\", 61)(3, \"h6\", 62);\n    i0.ɵɵtext(4, \"Prise en charge\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 63);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r9.formatDate(ctx_r9.delivery.pickupTime));\n  }\n}\nfunction CustomerPortalComponent_div_33_div_1_div_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵelement(1, \"div\", 60);\n    i0.ɵɵelementStart(2, \"div\", 61)(3, \"h6\", 62);\n    i0.ɵɵtext(4, \"Livraison effectu\\u00E9e\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 63);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r10.formatDate(ctx_r10.delivery.actualDeliveryTime));\n  }\n}\nfunction CustomerPortalComponent_div_33_div_1_div_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 83)(1, \"h6\", 84);\n    i0.ɵɵtext(2, \"Instructions sp\\u00E9ciales\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 85);\n    i0.ɵɵelement(4, \"i\", 86);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r11.delivery.notes, \" \");\n  }\n}\nfunction CustomerPortalComponent_div_33_div_1_div_92_div_6_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 92);\n  }\n  if (rf & 2) {\n    const star_r19 = ctx.$implicit;\n    const ctx_r18 = i0.ɵɵnextContext(5);\n    i0.ɵɵclassProp(\"text-warning\", star_r19 <= ctx_r18.delivery.customerRating)(\"text-muted\", star_r19 > ctx_r18.delivery.customerRating);\n  }\n}\nconst _c0 = function () {\n  return [1, 2, 3, 4, 5];\n};\nfunction CustomerPortalComponent_div_33_div_1_div_92_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 89)(1, \"p\", 73);\n    i0.ɵɵtext(2, \"Note donn\\u00E9e:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 90);\n    i0.ɵɵtemplate(4, CustomerPortalComponent_div_33_div_1_div_92_div_6_i_4_Template, 1, 4, \"i\", 91);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction CustomerPortalComponent_div_33_div_1_div_92_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 89)(1, \"p\", 73);\n    i0.ɵɵtext(2, \"Commentaire:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 93);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r16.delivery.customerFeedback);\n  }\n}\nfunction CustomerPortalComponent_div_33_div_1_div_92_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"p\", 93);\n    i0.ɵɵtext(2, \"Merci de nous faire part de votre exp\\u00E9rience!\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 94);\n    i0.ɵɵelement(4, \"i\", 87);\n    i0.ɵɵtext(5, \" Donner mon avis \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CustomerPortalComponent_div_33_div_1_div_92_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"div\", 71)(2, \"h6\", 47);\n    i0.ɵɵelement(3, \"i\", 87);\n    i0.ɵɵtext(4, \" Votre avis \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 36);\n    i0.ɵɵtemplate(6, CustomerPortalComponent_div_33_div_1_div_92_div_6_Template, 5, 2, \"div\", 88);\n    i0.ɵɵtemplate(7, CustomerPortalComponent_div_33_div_1_div_92_div_7_Template, 5, 1, \"div\", 88);\n    i0.ɵɵtemplate(8, CustomerPortalComponent_div_33_div_1_div_92_div_8_Template, 6, 0, \"div\", 25);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r12.delivery.customerRating);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r12.delivery.customerFeedback);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r12.delivery.customerRating);\n  }\n}\nfunction CustomerPortalComponent_div_33_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 35)(2, \"div\", 36)(3, \"h5\", 37);\n    i0.ɵɵelement(4, \"i\", 38);\n    i0.ɵɵtext(5, \" \\u00C9tat de votre livraison \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 39);\n    i0.ɵɵelement(7, \"div\", 40);\n    i0.ɵɵelementStart(8, \"div\", 41);\n    i0.ɵɵtemplate(9, CustomerPortalComponent_div_33_div_1_div_9_Template, 6, 11, \"div\", 42);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(10, \"div\", 43)(11, \"div\", 44)(12, \"div\", 45)(13, \"div\", 46)(14, \"h5\", 47);\n    i0.ɵɵelement(15, \"i\", 48);\n    i0.ɵɵtext(16, \" D\\u00E9tails de la livraison \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\", 49);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 36)(20, \"div\", 50)(21, \"div\", 51)(22, \"label\", 52);\n    i0.ɵɵtext(23, \"Num\\u00E9ro de commande\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"p\", 53);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 51)(27, \"label\", 52);\n    i0.ɵɵtext(28, \"Client\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"p\", 53);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 54)(32, \"label\", 52);\n    i0.ɵɵtext(33, \"Adresse de livraison\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"p\", 53);\n    i0.ɵɵelement(35, \"i\", 55);\n    i0.ɵɵtext(36);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(37, \"div\", 51)(38, \"label\", 52);\n    i0.ɵɵtext(39, \"Livreur\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"p\", 53);\n    i0.ɵɵelement(41, \"i\", 56);\n    i0.ɵɵtext(42);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(43, \"div\", 51)(44, \"label\", 52);\n    i0.ɵɵtext(45, \"Priorit\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"span\", 49);\n    i0.ɵɵtext(47);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(48, \"hr\");\n    i0.ɵɵelementStart(49, \"h6\", 57);\n    i0.ɵɵtext(50, \"Chronologie\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"div\", 58)(52, \"div\", 59);\n    i0.ɵɵelement(53, \"div\", 60);\n    i0.ɵɵelementStart(54, \"div\", 61)(55, \"h6\", 62);\n    i0.ɵɵtext(56, \"Commande cr\\u00E9\\u00E9e\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(57, \"p\", 63);\n    i0.ɵɵtext(58);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(59, CustomerPortalComponent_div_33_div_1_div_59_Template, 7, 1, \"div\", 64);\n    i0.ɵɵtemplate(60, CustomerPortalComponent_div_33_div_1_div_60_Template, 7, 1, \"div\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(61, CustomerPortalComponent_div_33_div_1_div_61_Template, 6, 1, \"div\", 65);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(62, \"div\", 66)(63, \"div\", 35)(64, \"div\", 67);\n    i0.ɵɵelement(65, \"i\", 68);\n    i0.ɵɵelementStart(66, \"h5\", 69);\n    i0.ɵɵtext(67, \"Temps estim\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(68, \"p\", 70);\n    i0.ɵɵtext(69);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(70, \"p\", 63);\n    i0.ɵɵtext(71);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(72, \"div\", 35)(73, \"div\", 71)(74, \"h6\", 47);\n    i0.ɵɵelement(75, \"i\", 72);\n    i0.ɵɵtext(76, \" Contact \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(77, \"div\", 36)(78, \"p\", 73)(79, \"strong\");\n    i0.ɵɵtext(80, \"Service client:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(81, \"br\");\n    i0.ɵɵelementStart(82, \"a\", 74);\n    i0.ɵɵelement(83, \"i\", 72);\n    i0.ɵɵtext(84, \" 01 23 45 67 89 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(85, \"p\", 75)(86, \"strong\");\n    i0.ɵɵtext(87, \"Email:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(88, \"br\");\n    i0.ɵɵelementStart(89, \"a\", 76);\n    i0.ɵɵelement(90, \"i\", 77);\n    i0.ɵɵtext(91, \" <EMAIL> \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(92, CustomerPortalComponent_div_33_div_1_div_92_Template, 9, 3, \"div\", 78);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.statusSteps);\n    i0.ɵɵadvance(8);\n    i0.ɵɵclassMap(\"bg-\" + ctx_r6.getStatusClass(ctx_r6.delivery.status));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.getStatusText(ctx_r6.delivery.status), \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r6.delivery.orderId);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r6.delivery.customerName);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.delivery.address, \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.delivery.driverName, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassMap(ctx_r6.delivery.priority === \"Urgent\" ? \"bg-danger\" : ctx_r6.delivery.priority === \"High\" ? \"bg-warning\" : ctx_r6.delivery.priority === \"Medium\" ? \"bg-info\" : \"bg-secondary\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.delivery.priority, \" \");\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate(ctx_r6.formatDate(ctx_r6.delivery.createdAt));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.delivery.pickupTime);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.delivery.actualDeliveryTime);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.delivery.notes);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r6.getEstimatedTimeRemaining());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" Livraison pr\\u00E9vue le \", ctx_r6.formatDate(ctx_r6.delivery.estimatedDeliveryTime), \" \");\n    i0.ɵɵadvance(21);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.delivery.status === \"Delivered\");\n  }\n}\nfunction CustomerPortalComponent_div_33_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵelement(1, \"i\", 95);\n    i0.ɵɵelementStart(2, \"h4\", 93);\n    i0.ɵɵtext(3, \"Entrez votre num\\u00E9ro de suivi\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 93);\n    i0.ɵɵtext(5, \"Saisissez votre num\\u00E9ro de commande ou de suivi dans le champ ci-dessus\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CustomerPortalComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, CustomerPortalComponent_div_33_div_1_Template, 93, 18, \"div\", 25);\n    i0.ɵɵtemplate(2, CustomerPortalComponent_div_33_div_2_Template, 6, 0, \"div\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.delivery && !ctx_r3.loading && !ctx_r3.error);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.trackingId && !ctx_r3.loading);\n  }\n}\nfunction CustomerPortalComponent_div_34_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 108)(1, \"div\", 30)(2, \"span\", 31);\n    i0.ɵɵtext(3, \"Chargement...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction CustomerPortalComponent_div_34_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 108);\n    i0.ɵɵelement(1, \"i\", 109);\n    i0.ɵɵelementStart(2, \"h5\", 93);\n    i0.ɵɵtext(3, \"Aucune commande trouv\\u00E9e\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 93);\n    i0.ɵɵtext(5, \"Vous n'avez pas encore pass\\u00E9 de commande.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CustomerPortalComponent_div_34_div_26_tr_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"strong\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"br\");\n    i0.ɵɵelementStart(5, \"small\", 93);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵelement(10, \"i\", 113);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\")(13, \"span\", 49);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"td\")(16, \"button\", 114);\n    i0.ɵɵlistener(\"click\", function CustomerPortalComponent_div_34_div_26_tr_15_Template_button_click_16_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r26);\n      const order_r24 = restoredCtx.$implicit;\n      const ctx_r25 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r25.viewOrderDetails(order_r24));\n    });\n    i0.ɵɵelement(17, \"i\", 115);\n    i0.ɵɵtext(18, \" Voir \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"button\", 116);\n    i0.ɵɵlistener(\"click\", function CustomerPortalComponent_div_34_div_26_tr_15_Template_button_click_19_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r26);\n      const order_r24 = restoredCtx.$implicit;\n      const ctx_r27 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r27.trackOrder(order_r24.orderId));\n    });\n    i0.ɵɵelement(20, \"i\", 18);\n    i0.ɵɵtext(21, \" Suivre \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const order_r24 = ctx.$implicit;\n    const ctx_r23 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(order_r24.orderId);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(order_r24.customerName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r23.formatDate(order_r24.createdAt));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", order_r24.address, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(\"bg-\" + ctx_r23.getStatusClass(order_r24.status));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r23.getStatusText(order_r24.status), \" \");\n  }\n}\nfunction CustomerPortalComponent_div_34_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 110)(1, \"table\", 111)(2, \"thead\")(3, \"tr\")(4, \"th\");\n    i0.ɵɵtext(5, \"Commande\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7, \"Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"Adresse\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Statut\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\");\n    i0.ɵɵtext(13, \"Actions\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"tbody\");\n    i0.ɵɵtemplate(15, CustomerPortalComponent_div_34_div_26_tr_15_Template, 22, 7, \"tr\", 112);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r22.filteredOrders);\n  }\n}\nfunction CustomerPortalComponent_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 41)(2, \"div\", 54)(3, \"div\", 45)(4, \"div\", 46)(5, \"h5\", 47);\n    i0.ɵɵelement(6, \"i\", 96);\n    i0.ɵɵtext(7, \" Mes Commandes \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 97)(9, \"select\", 98);\n    i0.ɵɵlistener(\"ngModelChange\", function CustomerPortalComponent_div_34_Template_select_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r28 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r28.orderFilter = $event);\n    })(\"change\", function CustomerPortalComponent_div_34_Template_select_change_9_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r30 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r30.filterOrders());\n    });\n    i0.ɵɵelementStart(10, \"option\", 99);\n    i0.ɵɵtext(11, \"Toutes les commandes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"option\", 100);\n    i0.ɵɵtext(13, \"En attente\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"option\", 101);\n    i0.ɵɵtext(15, \"En cours\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"option\", 102);\n    i0.ɵɵtext(17, \"Livr\\u00E9es\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"option\", 103);\n    i0.ɵɵtext(19, \"Annul\\u00E9es\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"button\", 104);\n    i0.ɵɵlistener(\"click\", function CustomerPortalComponent_div_34_Template_button_click_20_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r31 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r31.loadOrders());\n    });\n    i0.ɵɵelement(21, \"i\", 105);\n    i0.ɵɵtext(22, \" Actualiser \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(23, \"div\", 36);\n    i0.ɵɵtemplate(24, CustomerPortalComponent_div_34_div_24_Template, 4, 0, \"div\", 106);\n    i0.ɵɵtemplate(25, CustomerPortalComponent_div_34_div_25_Template, 6, 0, \"div\", 106);\n    i0.ɵɵtemplate(26, CustomerPortalComponent_div_34_div_26_Template, 16, 1, \"div\", 107);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngModel\", ctx_r4.orderFilter);\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.loadingOrders);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.loadingOrders && ctx_r4.orders.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.loadingOrders && ctx_r4.orders.length > 0);\n  }\n}\nfunction CustomerPortalComponent_div_35_span_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Sauvegarder\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerPortalComponent_div_35_span_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Sauvegarde...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerPortalComponent_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r35 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 43)(2, \"div\", 44)(3, \"div\", 45)(4, \"div\", 71)(5, \"h5\", 47);\n    i0.ɵɵelement(6, \"i\", 117);\n    i0.ɵɵtext(7, \" Informations Personnelles \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 36)(9, \"form\", 118);\n    i0.ɵɵlistener(\"ngSubmit\", function CustomerPortalComponent_div_35_Template_form_ngSubmit_9_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r34 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r34.updateProfile());\n    });\n    i0.ɵɵelementStart(10, \"div\", 50)(11, \"div\", 51)(12, \"label\", 119);\n    i0.ɵɵtext(13, \"Pr\\u00E9nom\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(14, \"input\", 120);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 51)(16, \"label\", 119);\n    i0.ɵɵtext(17, \"Nom\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(18, \"input\", 121);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 54)(20, \"label\", 119);\n    i0.ɵɵtext(21, \"Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"input\", 122);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 51)(24, \"label\", 119);\n    i0.ɵɵtext(25, \"T\\u00E9l\\u00E9phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(26, \"input\", 123);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 51)(28, \"label\", 119);\n    i0.ɵɵtext(29, \"Date de naissance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(30, \"input\", 124);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\", 54)(32, \"label\", 119);\n    i0.ɵɵtext(33, \"Adresse\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(34, \"textarea\", 125);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(35, \"hr\");\n    i0.ɵɵelementStart(36, \"div\", 126)(37, \"button\", 127);\n    i0.ɵɵlistener(\"click\", function CustomerPortalComponent_div_35_Template_button_click_37_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r36 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r36.resetProfile());\n    });\n    i0.ɵɵelement(38, \"i\", 128);\n    i0.ɵɵtext(39, \" Annuler \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"button\", 129);\n    i0.ɵɵelement(41, \"i\", 130);\n    i0.ɵɵtemplate(42, CustomerPortalComponent_div_35_span_42_Template, 2, 0, \"span\", 25);\n    i0.ɵɵtemplate(43, CustomerPortalComponent_div_35_span_43_Template, 2, 0, \"span\", 25);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(44, \"div\", 66)(45, \"div\", 35)(46, \"div\", 71)(47, \"h6\", 47);\n    i0.ɵɵelement(48, \"i\", 131);\n    i0.ɵɵtext(49, \" Param\\u00E8tres du Compte \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(50, \"div\", 36)(51, \"div\", 132)(52, \"button\", 133);\n    i0.ɵɵlistener(\"click\", function CustomerPortalComponent_div_35_Template_button_click_52_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r37 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r37.changePassword());\n    });\n    i0.ɵɵelement(53, \"i\", 134);\n    i0.ɵɵtext(54, \" Changer le mot de passe \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(55, \"button\", 116);\n    i0.ɵɵlistener(\"click\", function CustomerPortalComponent_div_35_Template_button_click_55_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r38 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r38.manageNotifications());\n    });\n    i0.ɵɵelement(56, \"i\", 135);\n    i0.ɵɵtext(57, \" Notifications \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"button\", 136);\n    i0.ɵɵlistener(\"click\", function CustomerPortalComponent_div_35_Template_button_click_58_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r39 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r39.downloadData());\n    });\n    i0.ɵɵelement(59, \"i\", 137);\n    i0.ɵɵtext(60, \" T\\u00E9l\\u00E9charger mes donn\\u00E9es \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(61, \"div\", 45)(62, \"div\", 71)(63, \"h6\", 47);\n    i0.ɵɵelement(64, \"i\", 138);\n    i0.ɵɵtext(65, \" Statistiques \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(66, \"div\", 36)(67, \"div\", 139)(68, \"div\", 140)(69, \"div\", 141)(70, \"h4\", 142);\n    i0.ɵɵtext(71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(72, \"small\", 93);\n    i0.ɵɵtext(73, \"Commandes\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(74, \"div\", 140)(75, \"div\", 141)(76, \"h4\", 143);\n    i0.ɵɵtext(77);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(78, \"small\", 93);\n    i0.ɵɵtext(79, \"Livr\\u00E9es\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(80, \"div\", 54)(81, \"div\", 141)(82, \"h5\", 144);\n    i0.ɵɵtext(83);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(84, \"small\", 93);\n    i0.ɵɵtext(85, \"Note moyenne donn\\u00E9e\");\n    i0.ɵɵelementEnd()()()()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"formGroup\", ctx_r5.profileForm);\n    i0.ɵɵadvance(31);\n    i0.ɵɵproperty(\"disabled\", !ctx_r5.profileForm.valid || ctx_r5.updatingProfile);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r5.updatingProfile);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.updatingProfile);\n    i0.ɵɵadvance(28);\n    i0.ɵɵtextInterpolate(ctx_r5.userStats.totalOrders);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r5.userStats.deliveredOrders);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r5.userStats.averageRating, \"/5\");\n  }\n}\nexport let CustomerPortalComponent = /*#__PURE__*/(() => {\n  class CustomerPortalComponent {\n    constructor(route, router, formBuilder, deliveryService, realTimeService, authService) {\n      this.route = route;\n      this.router = router;\n      this.formBuilder = formBuilder;\n      this.deliveryService = deliveryService;\n      this.realTimeService = realTimeService;\n      this.authService = authService;\n      // Tab management\n      this.activeTab = 'tracking';\n      // User data\n      this.currentUser = null;\n      // Tracking tab\n      this.delivery = null;\n      this.loading = true;\n      this.error = '';\n      this.trackingId = '';\n      // Orders tab\n      this.orders = [];\n      this.filteredOrders = [];\n      this.loadingOrders = false;\n      this.orderFilter = 'all';\n      this.updatingProfile = false;\n      this.userStats = {\n        totalOrders: 0,\n        deliveredOrders: 0,\n        averageRating: 0\n      };\n      // Real-time updates\n      this.subscriptions = [];\n      // Status tracking\n      this.statusSteps = [{\n        key: DeliveryStatus.Pending,\n        label: 'Commande confirmée',\n        icon: 'fa-check-circle'\n      }, {\n        key: DeliveryStatus.InTransit,\n        label: 'En cours de livraison',\n        icon: 'fa-truck'\n      }, {\n        key: DeliveryStatus.Delivered,\n        label: 'Livré',\n        icon: 'fa-box-check'\n      }];\n      this.profileForm = this.formBuilder.group({\n        firstName: ['', Validators.required],\n        lastName: ['', Validators.required],\n        email: ['', [Validators.required, Validators.email]],\n        phone: [''],\n        dateOfBirth: [''],\n        address: ['']\n      });\n    }\n    ngOnInit() {\n      // Load current user\n      this.currentUser = this.authService.getCurrentUser();\n      if (this.currentUser) {\n        this.initializeProfileForm();\n        this.loadUserStats();\n      }\n      this.route.params.subscribe(params => {\n        this.trackingId = params['trackingId'] || '';\n        if (this.trackingId) {\n          this.setActiveTab('tracking');\n          this.loadDelivery();\n          this.setupRealTimeUpdates();\n        } else {\n          // No tracking ID provided, stop loading and show search form\n          this.loading = false;\n        }\n      });\n      // Load orders if on orders tab\n      if (this.activeTab === 'orders') {\n        this.loadOrders();\n      }\n    }\n    ngOnDestroy() {\n      this.subscriptions.forEach(sub => sub.unsubscribe());\n      this.realTimeService.stopConnection();\n    }\n    loadDelivery() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        try {\n          _this.loading = true;\n          _this.error = '';\n          // Try to find delivery by order ID or delivery ID\n          const deliveryByOrderId = yield _this.deliveryService.getDeliveryByOrderId(_this.trackingId).toPromise();\n          _this.delivery = deliveryByOrderId || null;\n          if (!_this.delivery) {\n            const deliveryById = yield _this.deliveryService.getDelivery(_this.trackingId).toPromise();\n            _this.delivery = deliveryById || null;\n          }\n          if (!_this.delivery) {\n            _this.error = 'Livraison non trouvée. Vérifiez votre numéro de suivi.';\n          }\n        } catch (error) {\n          console.error('Error loading delivery:', error);\n          _this.error = 'Erreur lors du chargement des informations de livraison.';\n        } finally {\n          _this.loading = false;\n        }\n      })();\n    }\n    setupRealTimeUpdates() {\n      this.realTimeService.startConnection().then(() => {\n        console.log('Connected to real-time tracking');\n        // Subscribe to delivery updates\n        this.subscriptions.push(this.realTimeService.deliveryUpdates$.subscribe(updatedDelivery => {\n          if (updatedDelivery && this.delivery && updatedDelivery.id === this.delivery.id) {\n            this.delivery = {\n              ...this.delivery,\n              ...updatedDelivery\n            };\n          }\n        }));\n      }).catch(err => {\n        console.error('Error connecting to real-time tracking', err);\n      });\n    }\n    trackDelivery() {\n      if (this.trackingId.trim()) {\n        this.error = ''; // Clear any previous errors\n        this.loadDelivery();\n        this.setupRealTimeUpdates();\n      }\n    }\n    getStatusIndex(status) {\n      return this.statusSteps.findIndex(step => step.key === status);\n    }\n    isStatusCompleted(stepIndex) {\n      if (!this.delivery) return false;\n      const currentIndex = this.getStatusIndex(this.delivery.status);\n      return stepIndex <= currentIndex;\n    }\n    isStatusActive(stepIndex) {\n      if (!this.delivery) return false;\n      const currentIndex = this.getStatusIndex(this.delivery.status);\n      return stepIndex === currentIndex;\n    }\n    getStatusText(status) {\n      switch (status) {\n        case DeliveryStatus.Pending:\n          return 'En attente';\n        case DeliveryStatus.InTransit:\n          return 'En cours';\n        case DeliveryStatus.Delivered:\n          return 'Livré';\n        case DeliveryStatus.Delayed:\n          return 'Retardé';\n        case DeliveryStatus.Cancelled:\n          return 'Annulé';\n        default:\n          return 'Inconnu';\n      }\n    }\n    getStatusClass(status) {\n      switch (status) {\n        case DeliveryStatus.Pending:\n          return 'warning';\n        case DeliveryStatus.InTransit:\n          return 'info';\n        case DeliveryStatus.Delivered:\n          return 'success';\n        case DeliveryStatus.Delayed:\n          return 'danger';\n        case DeliveryStatus.Cancelled:\n          return 'secondary';\n        default:\n          return 'secondary';\n      }\n    }\n    formatDate(date) {\n      if (!date) return 'Non défini';\n      const d = new Date(date);\n      return d.toLocaleDateString('fr-FR', {\n        day: '2-digit',\n        month: '2-digit',\n        year: 'numeric',\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    }\n    getEstimatedTimeRemaining() {\n      if (!this.delivery || !this.delivery.estimatedDeliveryTime) {\n        return 'Non disponible';\n      }\n      const now = new Date();\n      const estimated = new Date(this.delivery.estimatedDeliveryTime);\n      const diffMs = estimated.getTime() - now.getTime();\n      if (diffMs <= 0) {\n        return 'Livraison prévue';\n      }\n      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));\n      const diffMinutes = Math.floor(diffMs % (1000 * 60 * 60) / (1000 * 60));\n      if (diffHours > 0) {\n        return `${diffHours}h ${diffMinutes}min`;\n      } else {\n        return `${diffMinutes}min`;\n      }\n    }\n    // Tab management\n    setActiveTab(tab) {\n      this.activeTab = tab;\n      if (tab === 'orders' && this.orders.length === 0) {\n        this.loadOrders();\n      }\n    }\n    // Logout functionality\n    logout() {\n      this.authService.logout().subscribe({\n        next: () => {\n          // Small delay to ensure authentication state is cleared\n          setTimeout(() => {\n            this.router.navigate(['/login']);\n          }, 100);\n        },\n        error: error => {\n          console.error('Logout error:', error);\n          // Navigate anyway since local storage is already cleared\n          setTimeout(() => {\n            this.router.navigate(['/login']);\n          }, 100);\n        }\n      });\n    }\n    // Orders management\n    loadOrders() {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        if (!_this2.currentUser) return;\n        try {\n          _this2.loadingOrders = true;\n          // Get all deliveries for the current user\n          _this2.orders = (yield _this2.deliveryService.getDeliveriesByCustomer(_this2.currentUser.id).toPromise()) || [];\n          _this2.filterOrders();\n        } catch (error) {\n          console.error('Error loading orders:', error);\n        } finally {\n          _this2.loadingOrders = false;\n        }\n      })();\n    }\n    filterOrders() {\n      if (this.orderFilter === 'all') {\n        this.filteredOrders = [...this.orders];\n      } else {\n        this.filteredOrders = this.orders.filter(order => {\n          switch (this.orderFilter) {\n            case 'pending':\n              return order.status === DeliveryStatus.Pending;\n            case 'in-transit':\n              return order.status === DeliveryStatus.InTransit;\n            case 'delivered':\n              return order.status === DeliveryStatus.Delivered;\n            case 'cancelled':\n              return order.status === DeliveryStatus.Cancelled;\n            default:\n              return true;\n          }\n        });\n      }\n    }\n    viewOrderDetails(order) {\n      // Navigate to tracking with this order\n      this.trackingId = order.orderId;\n      this.setActiveTab('tracking');\n      this.trackDelivery();\n    }\n    trackOrder(orderId) {\n      this.trackingId = orderId;\n      this.setActiveTab('tracking');\n      this.trackDelivery();\n    }\n    // Profile management\n    initializeProfileForm() {\n      if (this.currentUser) {\n        this.profileForm.patchValue({\n          firstName: this.currentUser.firstName,\n          lastName: this.currentUser.lastName,\n          email: this.currentUser.email,\n          phone: this.currentUser.phone || '',\n          dateOfBirth: this.currentUser.dateOfBirth || '',\n          address: this.currentUser.address || ''\n        });\n      }\n    }\n    updateProfile() {\n      var _this3 = this;\n      return _asyncToGenerator(function* () {\n        if (!_this3.profileForm.valid || !_this3.currentUser) return;\n        try {\n          _this3.updatingProfile = true;\n          const formData = _this3.profileForm.value;\n          // Update user profile\n          yield _this3.authService.updateProfile({\n            ..._this3.currentUser,\n            ...formData\n          }).toPromise();\n          // Update local user data\n          _this3.currentUser = {\n            ..._this3.currentUser,\n            ...formData\n          };\n          // Show success message (you can implement a toast service)\n          console.log('Profile updated successfully');\n        } catch (error) {\n          console.error('Error updating profile:', error);\n        } finally {\n          _this3.updatingProfile = false;\n        }\n      })();\n    }\n    resetProfile() {\n      this.initializeProfileForm();\n    }\n    // Account settings\n    changePassword() {\n      // Implement password change modal/dialog\n      console.log('Change password clicked');\n    }\n    manageNotifications() {\n      // Implement notification settings\n      console.log('Manage notifications clicked');\n    }\n    downloadData() {\n      // Implement data download\n      console.log('Download data clicked');\n    }\n    // User statistics\n    loadUserStats() {\n      var _this4 = this;\n      return _asyncToGenerator(function* () {\n        if (!_this4.currentUser) return;\n        try {\n          // Load user statistics\n          const orders = (yield _this4.deliveryService.getDeliveriesByCustomer(_this4.currentUser.id).toPromise()) || [];\n          _this4.userStats = {\n            totalOrders: orders.length,\n            deliveredOrders: orders.filter(o => o.status === DeliveryStatus.Delivered).length,\n            averageRating: _this4.calculateAverageRating(orders)\n          };\n        } catch (error) {\n          console.error('Error loading user stats:', error);\n        }\n      })();\n    }\n    calculateAverageRating(orders) {\n      const ratedOrders = orders.filter(o => o.customerRating && o.customerRating > 0);\n      if (ratedOrders.length === 0) return 0;\n      const totalRating = ratedOrders.reduce((sum, order) => sum + (order.customerRating || 0), 0);\n      return Math.round(totalRating / ratedOrders.length * 10) / 10;\n    }\n    static {\n      this.ɵfac = function CustomerPortalComponent_Factory(t) {\n        return new (t || CustomerPortalComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.DeliveryService), i0.ɵɵdirectiveInject(i4.RealTimeService), i0.ɵɵdirectiveInject(i5.AuthService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: CustomerPortalComponent,\n        selectors: [[\"app-customer-portal\"]],\n        decls: 36,\n        vars: 13,\n        consts: [[1, \"customer-portal\"], [1, \"container-fluid\"], [1, \"portal-header\", \"bg-primary\", \"text-white\", \"py-3\", \"mb-4\"], [1, \"container\"], [1, \"row\", \"align-items-center\"], [1, \"col-md-4\"], [1, \"h4\", \"mb-0\"], [1, \"fa-solid\", \"fa-truck\", \"me-2\"], [1, \"col-md-4\", \"text-center\"], [1, \"input-group\", 2, \"max-width\", \"300px\", \"margin\", \"0 auto\"], [\"type\", \"text\", \"placeholder\", \"Num\\u00E9ro de suivi\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\", \"keyup.enter\"], [\"type\", \"button\", 1, \"btn\", \"btn-light\", 3, \"click\"], [1, \"fa-solid\", \"fa-search\"], [1, \"col-md-4\", \"text-md-end\"], [1, \"d-flex\", \"align-items-center\", \"justify-content-md-end\", \"gap-3\"], [\"class\", \"d-flex align-items-center\", 4, \"ngIf\"], [\"role\", \"group\", 1, \"btn-group\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-light\", \"btn-sm\", 3, \"click\"], [1, \"fa-solid\", \"fa-search\", \"me-1\"], [1, \"fa-solid\", \"fa-list\", \"me-1\"], [1, \"fa-solid\", \"fa-user\", \"me-1\"], [\"title\", \"Se d\\u00E9connecter\", 1, \"btn\", \"btn-outline-light\", \"btn-sm\", 3, \"click\"], [1, \"fa-solid\", \"fa-sign-out-alt\", \"me-1\"], [\"class\", \"text-center py-5\", 4, \"ngIf\"], [\"class\", \"alert alert-danger\", \"role\", \"alert\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"d-flex\", \"align-items-center\"], [1, \"fa-solid\", \"fa-user-circle\", \"fa-lg\", \"me-2\"], [1, \"small\"], [1, \"text-center\", \"py-5\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"mt-3\", \"text-muted\"], [\"role\", \"alert\", 1, \"alert\", \"alert-danger\"], [1, \"fa-solid\", \"fa-exclamation-triangle\", \"me-2\"], [1, \"card\", \"mb-4\"], [1, \"card-body\"], [1, \"card-title\", \"mb-4\"], [1, \"fa-solid\", \"fa-route\", \"me-2\"], [1, \"progress-tracker\"], [1, \"progress-line\"], [1, \"row\"], [\"class\", \"col-md-4\", 4, \"ngFor\", \"ngForOf\"], [1, \"row\", \"g-4\"], [1, \"col-lg-8\"], [1, \"card\"], [1, \"card-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"card-title\", \"mb-0\"], [1, \"fa-solid\", \"fa-info-circle\", \"me-2\"], [1, \"badge\"], [1, \"row\", \"g-3\"], [1, \"col-md-6\"], [1, \"form-label\", \"text-muted\"], [1, \"fw-bold\"], [1, \"col-12\"], [1, \"fa-solid\", \"fa-location-dot\", \"me-2\", \"text-primary\"], [1, \"fa-solid\", \"fa-user\", \"me-2\", \"text-info\"], [1, \"text-muted\", \"mb-3\"], [1, \"timeline\"], [1, \"timeline-item\"], [1, \"timeline-marker\", \"bg-success\"], [1, \"timeline-content\"], [1, \"mb-1\"], [1, \"text-muted\", \"small\", \"mb-0\"], [\"class\", \"timeline-item\", 4, \"ngIf\"], [\"class\", \"mt-4\", 4, \"ngIf\"], [1, \"col-lg-4\"], [1, \"card-body\", \"text-center\"], [1, \"fa-solid\", \"fa-clock\", \"fa-2x\", \"text-primary\", \"mb-3\"], [1, \"card-title\"], [1, \"h4\", \"text-primary\", \"mb-2\"], [1, \"card-header\"], [1, \"fa-solid\", \"fa-phone\", \"me-2\"], [1, \"mb-2\"], [\"href\", \"tel:+33123456789\", 1, \"text-decoration-none\"], [1, \"mb-0\"], [\"href\", \"mailto:<EMAIL>\", 1, \"text-decoration-none\"], [1, \"fa-solid\", \"fa-envelope\", \"me-2\"], [\"class\", \"card\", 4, \"ngIf\"], [1, \"progress-step\", \"text-center\"], [1, \"step-icon\"], [1, \"step-title\", \"mt-2\"], [1, \"timeline-marker\", \"bg-info\"], [1, \"mt-4\"], [1, \"text-muted\", \"mb-2\"], [1, \"alert\", \"alert-info\"], [1, \"fa-solid\", \"fa-note-sticky\", \"me-2\"], [1, \"fa-solid\", \"fa-star\", \"me-2\"], [\"class\", \"mb-3\", 4, \"ngIf\"], [1, \"mb-3\"], [1, \"rating\"], [\"class\", \"fa-solid fa-star\", 3, \"text-warning\", \"text-muted\", 4, \"ngFor\", \"ngForOf\"], [1, \"fa-solid\", \"fa-star\"], [1, \"text-muted\"], [1, \"btn\", \"btn-primary\", \"btn-sm\"], [1, \"fa-solid\", \"fa-search\", \"fa-3x\", \"text-muted\", \"mb-3\"], [1, \"fa-solid\", \"fa-list\", \"me-2\"], [1, \"d-flex\", \"gap-2\"], [1, \"form-select\", \"form-select-sm\", 3, \"ngModel\", \"ngModelChange\", \"change\"], [\"value\", \"all\"], [\"value\", \"pending\"], [\"value\", \"in-transit\"], [\"value\", \"delivered\"], [\"value\", \"cancelled\"], [1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\"], [1, \"fa-solid\", \"fa-refresh\", \"me-1\"], [\"class\", \"text-center py-4\", 4, \"ngIf\"], [\"class\", \"table-responsive\", 4, \"ngIf\"], [1, \"text-center\", \"py-4\"], [1, \"fa-solid\", \"fa-box-open\", \"fa-3x\", \"text-muted\", \"mb-3\"], [1, \"table-responsive\"], [1, \"table\", \"table-hover\"], [4, \"ngFor\", \"ngForOf\"], [1, \"fa-solid\", \"fa-location-dot\", \"me-1\", \"text-primary\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fa-solid\", \"fa-eye\", \"me-1\"], [1, \"btn\", \"btn-outline-info\", \"btn-sm\", 3, \"click\"], [1, \"fa-solid\", \"fa-user\", \"me-2\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"form-label\"], [\"type\", \"text\", \"formControlName\", \"firstName\", 1, \"form-control\"], [\"type\", \"text\", \"formControlName\", \"lastName\", 1, \"form-control\"], [\"type\", \"email\", \"formControlName\", \"email\", 1, \"form-control\"], [\"type\", \"tel\", \"formControlName\", \"phone\", 1, \"form-control\"], [\"type\", \"date\", \"formControlName\", \"dateOfBirth\", 1, \"form-control\"], [\"rows\", \"3\", \"formControlName\", \"address\", 1, \"form-control\"], [1, \"d-flex\", \"justify-content-between\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", 3, \"click\"], [1, \"fa-solid\", \"fa-undo\", \"me-1\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"disabled\"], [1, \"fa-solid\", \"fa-save\", \"me-1\"], [1, \"fa-solid\", \"fa-cog\", \"me-2\"], [1, \"d-grid\", \"gap-2\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", 3, \"click\"], [1, \"fa-solid\", \"fa-key\", \"me-1\"], [1, \"fa-solid\", \"fa-bell\", \"me-1\"], [1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", 3, \"click\"], [1, \"fa-solid\", \"fa-download\", \"me-1\"], [1, \"fa-solid\", \"fa-chart-bar\", \"me-2\"], [1, \"row\", \"g-3\", \"text-center\"], [1, \"col-6\"], [1, \"border\", \"rounded\", \"p-2\"], [1, \"text-primary\", \"mb-1\"], [1, \"text-success\", \"mb-1\"], [1, \"text-info\", \"mb-1\"]],\n        template: function CustomerPortalComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"h1\", 6);\n            i0.ɵɵelement(7, \"i\", 7);\n            i0.ɵɵtext(8, \" Espace Client \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(9, \"div\", 8)(10, \"div\", 9)(11, \"input\", 10);\n            i0.ɵɵlistener(\"ngModelChange\", function CustomerPortalComponent_Template_input_ngModelChange_11_listener($event) {\n              return ctx.trackingId = $event;\n            })(\"keyup.enter\", function CustomerPortalComponent_Template_input_keyup_enter_11_listener() {\n              return ctx.trackDelivery();\n            });\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(12, \"button\", 11);\n            i0.ɵɵlistener(\"click\", function CustomerPortalComponent_Template_button_click_12_listener() {\n              return ctx.trackDelivery();\n            });\n            i0.ɵɵelement(13, \"i\", 12);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(14, \"div\", 13)(15, \"div\", 14);\n            i0.ɵɵtemplate(16, CustomerPortalComponent_div_16_Template, 4, 2, \"div\", 15);\n            i0.ɵɵelementStart(17, \"div\", 16)(18, \"button\", 17);\n            i0.ɵɵlistener(\"click\", function CustomerPortalComponent_Template_button_click_18_listener() {\n              return ctx.setActiveTab(\"tracking\");\n            });\n            i0.ɵɵelement(19, \"i\", 18);\n            i0.ɵɵtext(20, \" Suivi \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(21, \"button\", 17);\n            i0.ɵɵlistener(\"click\", function CustomerPortalComponent_Template_button_click_21_listener() {\n              return ctx.setActiveTab(\"orders\");\n            });\n            i0.ɵɵelement(22, \"i\", 19);\n            i0.ɵɵtext(23, \" Commandes \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(24, \"button\", 17);\n            i0.ɵɵlistener(\"click\", function CustomerPortalComponent_Template_button_click_24_listener() {\n              return ctx.setActiveTab(\"profile\");\n            });\n            i0.ɵɵelement(25, \"i\", 20);\n            i0.ɵɵtext(26, \" Profil \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(27, \"button\", 21);\n            i0.ɵɵlistener(\"click\", function CustomerPortalComponent_Template_button_click_27_listener() {\n              return ctx.logout();\n            });\n            i0.ɵɵelement(28, \"i\", 22);\n            i0.ɵɵtext(29, \" D\\u00E9connexion \");\n            i0.ɵɵelementEnd()()()()()();\n            i0.ɵɵelementStart(30, \"div\", 3);\n            i0.ɵɵtemplate(31, CustomerPortalComponent_div_31_Template, 6, 0, \"div\", 23);\n            i0.ɵɵtemplate(32, CustomerPortalComponent_div_32_Template, 3, 1, \"div\", 24);\n            i0.ɵɵtemplate(33, CustomerPortalComponent_div_33_Template, 3, 2, \"div\", 25);\n            i0.ɵɵtemplate(34, CustomerPortalComponent_div_34_Template, 27, 4, \"div\", 25);\n            i0.ɵɵtemplate(35, CustomerPortalComponent_div_35_Template, 86, 7, \"div\", 25);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(11);\n            i0.ɵɵproperty(\"ngModel\", ctx.trackingId);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ctx.currentUser);\n            i0.ɵɵadvance(2);\n            i0.ɵɵclassProp(\"active\", ctx.activeTab === \"tracking\");\n            i0.ɵɵadvance(3);\n            i0.ɵɵclassProp(\"active\", ctx.activeTab === \"orders\");\n            i0.ɵɵadvance(3);\n            i0.ɵɵclassProp(\"active\", ctx.activeTab === \"profile\");\n            i0.ɵɵadvance(7);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"tracking\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"orders\");\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"profile\");\n          }\n        },\n        dependencies: [i6.NgForOf, i6.NgIf, i2.ɵNgNoValidate, i2.NgSelectOption, i2.ɵNgSelectMultipleOption, i2.DefaultValueAccessor, i2.SelectControlValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.NgModel, i2.FormGroupDirective, i2.FormControlName],\n        styles: [\".customer-portal[_ngcontent-%COMP%]{min-height:100vh;background-color:#f8f9fa;font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,sans-serif}.customer-portal[_ngcontent-%COMP%]   .portal-header[_ngcontent-%COMP%]{background:linear-gradient(135deg,#007bff 0%,#0056b3 100%);box-shadow:0 4px 20px #007bff40;position:relative;overflow:hidden}.customer-portal[_ngcontent-%COMP%]   .portal-header[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;inset:0;background:url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 100 100\\\"><circle cx=\\\"20\\\" cy=\\\"20\\\" r=\\\"2\\\" fill=\\\"rgba(255,255,255,0.1)\\\"/><circle cx=\\\"80\\\" cy=\\\"40\\\" r=\\\"1\\\" fill=\\\"rgba(255,255,255,0.1)\\\"/><circle cx=\\\"40\\\" cy=\\\"80\\\" r=\\\"1.5\\\" fill=\\\"rgba(255,255,255,0.1)\\\"/></svg>');pointer-events:none}.customer-portal[_ngcontent-%COMP%]   .portal-header[_ngcontent-%COMP%]   .portal-content[_ngcontent-%COMP%]{position:relative;z-index:1}.customer-portal[_ngcontent-%COMP%]   .portal-header[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]{background:rgba(255,255,255,.1);border-radius:12px;padding:4px;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.customer-portal[_ngcontent-%COMP%]   .portal-header[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{border:none;border-radius:8px;transition:all .3s cubic-bezier(.4,0,.2,1);font-weight:500;padding:8px 16px}.customer-portal[_ngcontent-%COMP%]   .portal-header[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.active[_ngcontent-%COMP%]{background-color:#ffffff40;border-color:#fff6;color:#fff;transform:translateY(-1px);box-shadow:0 4px 12px #00000026}.customer-portal[_ngcontent-%COMP%]   .portal-header[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover:not(.active){background-color:#ffffff26;transform:translateY(-1px)}.customer-portal[_ngcontent-%COMP%]   .portal-header[_ngcontent-%COMP%]   .search-input[_ngcontent-%COMP%]{background:rgba(255,255,255,.15);border:1px solid rgba(255,255,255,.3);-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border-radius:10px;color:#fff;transition:all .3s ease}.customer-portal[_ngcontent-%COMP%]   .portal-header[_ngcontent-%COMP%]   .search-input[_ngcontent-%COMP%]::placeholder{color:#ffffffb3}.customer-portal[_ngcontent-%COMP%]   .portal-header[_ngcontent-%COMP%]   .search-input[_ngcontent-%COMP%]:focus{background:rgba(255,255,255,.25);border-color:#ffffff80;box-shadow:0 0 0 3px #ffffff1a;outline:none}.customer-portal[_ngcontent-%COMP%]   .progress-tracker[_ngcontent-%COMP%]{position:relative;padding:3rem 0;background:linear-gradient(135deg,#ffffff 0%,#f8f9fa 100%);border-radius:16px;margin:2rem 0;box-shadow:0 8px 32px #0000000f}.customer-portal[_ngcontent-%COMP%]   .progress-tracker[_ngcontent-%COMP%]   .progress-line[_ngcontent-%COMP%]{position:absolute;top:50%;left:12.5%;right:12.5%;height:3px;background:linear-gradient(90deg,#e9ecef 0%,#dee2e6 100%);border-radius:2px;z-index:1;transform:translateY(-50%)}.customer-portal[_ngcontent-%COMP%]   .progress-tracker[_ngcontent-%COMP%]   .progress-line[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;top:0;left:0;height:100%;width:66.66%;background:linear-gradient(90deg,#28a745 0%,#20c997 100%);border-radius:2px;transition:width .8s ease-in-out}.customer-portal[_ngcontent-%COMP%]   .progress-tracker[_ngcontent-%COMP%]   .progress-step[_ngcontent-%COMP%]{position:relative;z-index:2}.customer-portal[_ngcontent-%COMP%]   .progress-tracker[_ngcontent-%COMP%]   .progress-step[_ngcontent-%COMP%]   .step-icon[_ngcontent-%COMP%]{width:70px;height:70px;border-radius:50%;background:linear-gradient(135deg,#e9ecef 0%,#dee2e6 100%);color:#6c757d;display:flex;align-items:center;justify-content:center;margin:0 auto;font-size:1.75rem;font-weight:600;transition:all .4s cubic-bezier(.4,0,.2,1);border:4px solid #e9ecef;box-shadow:0 4px 20px #00000014}.customer-portal[_ngcontent-%COMP%]   .progress-tracker[_ngcontent-%COMP%]   .progress-step[_ngcontent-%COMP%]   .step-icon.active[_ngcontent-%COMP%]{background:linear-gradient(135deg,#007bff 0%,#0056b3 100%);color:#fff;border-color:#007bff;box-shadow:0 0 0 6px #007bff33,0 8px 32px #007bff4d;animation:_ngcontent-%COMP%_pulse-glow 2s infinite;transform:scale(1.1)}.customer-portal[_ngcontent-%COMP%]   .progress-tracker[_ngcontent-%COMP%]   .progress-step[_ngcontent-%COMP%]   .step-icon.completed[_ngcontent-%COMP%]{background:linear-gradient(135deg,#28a745 0%,#20c997 100%);color:#fff;border-color:#28a745;box-shadow:0 4px 20px #28a7454d;transform:scale(1.05)}.customer-portal[_ngcontent-%COMP%]   .progress-tracker[_ngcontent-%COMP%]   .progress-step[_ngcontent-%COMP%]   .step-icon[_ngcontent-%COMP%]:hover{transform:scale(1.15);box-shadow:0 8px 32px #00000026}.customer-portal[_ngcontent-%COMP%]   .progress-tracker[_ngcontent-%COMP%]   .progress-step[_ngcontent-%COMP%]   .step-title[_ngcontent-%COMP%]{font-size:.95rem;font-weight:600;margin-top:.75rem;color:#495057;transition:color .3s ease}.customer-portal[_ngcontent-%COMP%]   .progress-tracker[_ngcontent-%COMP%]   .progress-step[_ngcontent-%COMP%]   .step-title.active[_ngcontent-%COMP%]{color:#007bff}.customer-portal[_ngcontent-%COMP%]   .progress-tracker[_ngcontent-%COMP%]   .progress-step[_ngcontent-%COMP%]   .step-title.completed[_ngcontent-%COMP%]{color:#28a745}.customer-portal[_ngcontent-%COMP%]   .progress-tracker[_ngcontent-%COMP%]   .progress-step[_ngcontent-%COMP%]   .step-description[_ngcontent-%COMP%]{font-size:.8rem;color:#6c757d;margin-top:.25rem;opacity:.8}.customer-portal[_ngcontent-%COMP%]   .timeline[_ngcontent-%COMP%]{position:relative;padding-left:2.5rem;background:white;border-radius:12px;padding:2rem 2rem 2rem 3rem;box-shadow:0 4px 20px #00000014}.customer-portal[_ngcontent-%COMP%]   .timeline[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;left:1rem;top:2rem;bottom:2rem;width:3px;background:linear-gradient(180deg,#007bff 0%,#e9ecef 100%);border-radius:2px}.customer-portal[_ngcontent-%COMP%]   .timeline[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]{position:relative;margin-bottom:2rem;padding:1rem;background:#f8f9fa;border-radius:8px;transition:all .3s ease}.customer-portal[_ngcontent-%COMP%]   .timeline[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]:hover{background:#ffffff;box-shadow:0 4px 16px #00000014;transform:translate(4px)}.customer-portal[_ngcontent-%COMP%]   .timeline[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-marker[_ngcontent-%COMP%]{position:absolute;left:-2.5rem;top:1.5rem;width:14px;height:14px;border-radius:50%;border:3px solid white;background:#007bff;box-shadow:0 0 0 3px #e9ecef;transition:all .3s ease}.customer-portal[_ngcontent-%COMP%]   .timeline[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-marker.completed[_ngcontent-%COMP%]{background:#28a745;box-shadow:0 0 0 3px #28a74533}.customer-portal[_ngcontent-%COMP%]   .timeline[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-marker.active[_ngcontent-%COMP%]{background:#007bff;box-shadow:0 0 0 3px #007bff4d;animation:_ngcontent-%COMP%_pulse-marker 2s infinite}.customer-portal[_ngcontent-%COMP%]   .timeline[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-content[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{margin-bottom:.5rem;font-weight:600;color:#495057;font-size:1rem}.customer-portal[_ngcontent-%COMP%]   .timeline[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-bottom:0;font-size:.875rem;color:#6c757d;line-height:1.5}.customer-portal[_ngcontent-%COMP%]   .timeline[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-content[_ngcontent-%COMP%]   .timeline-meta[_ngcontent-%COMP%]{font-size:.75rem;color:#adb5bd;margin-top:.5rem}.customer-portal[_ngcontent-%COMP%]   .rating[_ngcontent-%COMP%]   .fa-star[_ngcontent-%COMP%], .customer-portal[_ngcontent-%COMP%]   .rating[_ngcontent-%COMP%]   .star-icon[_ngcontent-%COMP%]{font-size:1.3rem;margin-right:.25rem;color:#ffc107;transition:all .2s ease}.customer-portal[_ngcontent-%COMP%]   .rating[_ngcontent-%COMP%]   .fa-star[_ngcontent-%COMP%]:hover, .customer-portal[_ngcontent-%COMP%]   .rating[_ngcontent-%COMP%]   .star-icon[_ngcontent-%COMP%]:hover{transform:scale(1.2);filter:brightness(1.1)}.customer-portal[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]{border:none;box-shadow:0 4px 24px #0000000f;border-radius:16px;transition:all .3s cubic-bezier(.4,0,.2,1);background:white}.customer-portal[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]:hover{box-shadow:0 8px 40px #0000001f;transform:translateY(-2px)}.customer-portal[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f8f9fa 0%,#ffffff 100%);border-bottom:1px solid rgba(0,0,0,.05);border-radius:16px 16px 0 0!important;padding:1.5rem 2rem}.customer-portal[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%]{font-weight:600;color:#495057;font-size:1.25rem;margin:0}.customer-portal[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-subtitle[_ngcontent-%COMP%]{color:#6c757d;font-size:.9rem;margin-top:.25rem}.customer-portal[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%]{padding:2rem}.customer-portal[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   .card-footer[_ngcontent-%COMP%]{background:rgba(248,249,250,.5);border-top:1px solid rgba(0,0,0,.05);border-radius:0 0 16px 16px;padding:1rem 2rem}.customer-portal[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]{font-size:.75rem;font-weight:500;padding:.5rem .875rem;border-radius:8px;letter-spacing:.025em;text-transform:uppercase;transition:all .2s ease}.customer-portal[_ngcontent-%COMP%]   .badge.badge-primary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#007bff 0%,#0056b3 100%);color:#fff}.customer-portal[_ngcontent-%COMP%]   .badge.badge-success[_ngcontent-%COMP%]{background:linear-gradient(135deg,#28a745 0%,#20c997 100%);color:#fff}.customer-portal[_ngcontent-%COMP%]   .badge.badge-warning[_ngcontent-%COMP%]{background:linear-gradient(135deg,#ffc107 0%,#fd7e14 100%);color:#212529}.customer-portal[_ngcontent-%COMP%]   .badge.badge-danger[_ngcontent-%COMP%]{background:linear-gradient(135deg,#dc3545 0%,#c82333 100%);color:#fff}.customer-portal[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 4px 12px #00000026}.customer-portal[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]{border:none;border-radius:12px;padding:1.25rem 1.5rem;border-left:4px solid;box-shadow:0 4px 16px #0000000d}.customer-portal[_ngcontent-%COMP%]   .alert.alert-primary[_ngcontent-%COMP%]{background:linear-gradient(135deg,rgba(0,123,255,.1) 0%,rgba(0,123,255,.05) 100%);border-left-color:#007bff;color:#004085}.customer-portal[_ngcontent-%COMP%]   .alert.alert-success[_ngcontent-%COMP%]{background:linear-gradient(135deg,rgba(40,167,69,.1) 0%,rgba(40,167,69,.05) 100%);border-left-color:#28a745;color:#155724}.customer-portal[_ngcontent-%COMP%]   .alert.alert-warning[_ngcontent-%COMP%]{background:linear-gradient(135deg,rgba(255,193,7,.1) 0%,rgba(255,193,7,.05) 100%);border-left-color:#ffc107;color:#856404}.customer-portal[_ngcontent-%COMP%]   .alert.alert-danger[_ngcontent-%COMP%]{background:linear-gradient(135deg,rgba(220,53,69,.1) 0%,rgba(220,53,69,.05) 100%);border-left-color:#dc3545;color:#721c24}.customer-portal[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{border-radius:8px;font-weight:500;padding:.625rem 1.25rem;font-size:.875rem;line-height:1.5;transition:all .2s cubic-bezier(.4,0,.2,1);border:2px solid transparent;text-decoration:none;display:inline-flex;align-items:center;gap:.5rem}.customer-portal[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 6px 20px #00000026}.customer-portal[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:active{transform:translateY(0)}.customer-portal[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]{background:linear-gradient(135deg,#007bff 0%,#0056b3 100%);color:#fff;border-color:#007bff}.customer-portal[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#0056b3 0%,#004085 100%);border-color:#0056b3}.customer-portal[_ngcontent-%COMP%]   .btn.btn-success[_ngcontent-%COMP%]{background:linear-gradient(135deg,#28a745 0%,#20c997 100%);color:#fff;border-color:#28a745}.customer-portal[_ngcontent-%COMP%]   .btn.btn-success[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,#20c997 0%,#17a2b8 100%);border-color:#20c997}.customer-portal[_ngcontent-%COMP%]   .btn.btn-outline-primary[_ngcontent-%COMP%]{background:transparent;color:#007bff;border-color:#007bff}.customer-portal[_ngcontent-%COMP%]   .btn.btn-outline-primary[_ngcontent-%COMP%]:hover{background:#007bff;color:#fff}.customer-portal[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]{border-radius:8px;border:2px solid #e9ecef;padding:.75rem 1rem;font-size:.875rem;transition:all .3s ease;background:white}.customer-portal[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus{border-color:#007bff;box-shadow:0 0 0 .2rem #007bff40;outline:none;background:#ffffff}.customer-portal[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]::placeholder{color:#adb5bd}.customer-portal[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:hover{border-color:#ced4da}.customer-portal[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{border-radius:0 8px 8px 0;border-left:none}.customer-portal[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]{border-radius:8px 0 0 8px}.customer-portal[_ngcontent-%COMP%]   .stats-card[_ngcontent-%COMP%]{background:white;border:2px solid #e9ecef;border-radius:12px;padding:1.5rem;transition:all .3s cubic-bezier(.4,0,.2,1);position:relative;overflow:hidden}.customer-portal[_ngcontent-%COMP%]   .stats-card[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:0;right:0;height:4px;background:linear-gradient(90deg,#007bff 0%,#20c997 100%);transform:scaleX(0);transition:transform .3s ease}.customer-portal[_ngcontent-%COMP%]   .stats-card[_ngcontent-%COMP%]:hover{border-color:#007bff;background:linear-gradient(135deg,rgba(0,123,255,.02) 0%,rgba(32,201,151,.02) 100%);transform:translateY(-4px);box-shadow:0 8px 32px #007bff26}.customer-portal[_ngcontent-%COMP%]   .stats-card[_ngcontent-%COMP%]:hover:before{transform:scaleX(1)}.customer-portal[_ngcontent-%COMP%]   .stats-card[_ngcontent-%COMP%]   .stats-icon[_ngcontent-%COMP%]{width:50px;height:50px;border-radius:12px;background:linear-gradient(135deg,#007bff 0%,#20c997 100%);color:#fff;display:flex;align-items:center;justify-content:center;font-size:1.5rem;margin-bottom:1rem}.customer-portal[_ngcontent-%COMP%]   .stats-card[_ngcontent-%COMP%]   .stats-value[_ngcontent-%COMP%]{font-size:2rem;font-weight:700;color:#495057;margin-bottom:.25rem}.customer-portal[_ngcontent-%COMP%]   .stats-card[_ngcontent-%COMP%]   .stats-label[_ngcontent-%COMP%]{color:#6c757d;font-size:.875rem;font-weight:500}.table-responsive[_ngcontent-%COMP%]{border-radius:12px;overflow:hidden;box-shadow:0 4px 20px #00000014;background:white}.table[_ngcontent-%COMP%]{margin-bottom:0}.table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f8f9fa 0%,#ffffff 100%);border-top:none;border-bottom:2px solid #e9ecef;font-weight:600;color:#495057;font-size:.875rem;text-transform:uppercase;letter-spacing:.025em;padding:1rem}.table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{padding:1rem;border-bottom:1px solid #f1f3f4;vertical-align:middle}.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]{transition:all .2s ease}.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,rgba(0,123,255,.03) 0%,rgba(0,123,255,.01) 100%);transform:scale(1.005)}.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:last-child   td[_ngcontent-%COMP%]{border-bottom:none}.form-label[_ngcontent-%COMP%]{font-weight:600;color:#495057;margin-bottom:.5rem;font-size:.875rem;text-transform:uppercase;letter-spacing:.025em}.form-group[_ngcontent-%COMP%]{margin-bottom:1.5rem}.form-group[_ngcontent-%COMP%]   .form-text[_ngcontent-%COMP%]{color:#6c757d;font-size:.8rem;margin-top:.25rem}@keyframes _ngcontent-%COMP%_pulse-glow{0%{box-shadow:0 0 0 6px #007bff33,0 8px 32px #007bff4d}50%{box-shadow:0 0 0 10px #007bff1a,0 8px 32px #007bff66}to{box-shadow:0 0 0 6px #007bff33,0 8px 32px #007bff4d}}@keyframes _ngcontent-%COMP%_pulse-marker{0%{transform:scale(1);box-shadow:0 0 0 3px #007bff4d}50%{transform:scale(1.1);box-shadow:0 0 0 6px #007bff33}to{transform:scale(1);box-shadow:0 0 0 3px #007bff4d}}@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(30px)}to{opacity:1;transform:translateY(0)}}.fade-in-up[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fadeInUp .6s ease-out}@media (max-width: 768px){.customer-portal[_ngcontent-%COMP%]   .portal-header[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]{flex-direction:column;width:100%}.customer-portal[_ngcontent-%COMP%]   .portal-header[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{width:100%;margin-bottom:.25rem;justify-content:center}.customer-portal[_ngcontent-%COMP%]   .portal-header[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:last-child{margin-bottom:0}.customer-portal[_ngcontent-%COMP%]   .portal-header[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]{margin-top:1rem;max-width:100%!important}.customer-portal[_ngcontent-%COMP%]   .progress-tracker[_ngcontent-%COMP%]{padding:2rem 1rem}.customer-portal[_ngcontent-%COMP%]   .progress-tracker[_ngcontent-%COMP%]   .progress-line[_ngcontent-%COMP%]{display:none}.customer-portal[_ngcontent-%COMP%]   .progress-tracker[_ngcontent-%COMP%]   .progress-step[_ngcontent-%COMP%]{margin-bottom:2rem}.customer-portal[_ngcontent-%COMP%]   .progress-tracker[_ngcontent-%COMP%]   .progress-step[_ngcontent-%COMP%]:last-child{margin-bottom:0}.customer-portal[_ngcontent-%COMP%]   .progress-tracker[_ngcontent-%COMP%]   .progress-step[_ngcontent-%COMP%]   .step-icon[_ngcontent-%COMP%]{width:60px;height:60px;font-size:1.5rem}.customer-portal[_ngcontent-%COMP%]   .timeline[_ngcontent-%COMP%]{padding-left:2rem}.customer-portal[_ngcontent-%COMP%]   .timeline[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-marker[_ngcontent-%COMP%]{left:-2rem}.customer-portal[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]{margin-bottom:1rem}.customer-portal[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%], .customer-portal[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%], .customer-portal[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   .card-footer[_ngcontent-%COMP%]{padding:1rem}.customer-portal[_ngcontent-%COMP%]   .stats-card[_ngcontent-%COMP%]{margin-bottom:1rem;padding:1rem}.customer-portal[_ngcontent-%COMP%]   .stats-card[_ngcontent-%COMP%]   .stats-value[_ngcontent-%COMP%]{font-size:1.5rem}.customer-portal[_ngcontent-%COMP%]   .table-responsive[_ngcontent-%COMP%]{font-size:.8rem}.customer-portal[_ngcontent-%COMP%]   .table-responsive[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .customer-portal[_ngcontent-%COMP%]   .table-responsive[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{padding:.5rem}}@media (max-width: 576px){.customer-portal[_ngcontent-%COMP%]   .portal-header[_ngcontent-%COMP%]{padding:1rem}.customer-portal[_ngcontent-%COMP%]   .portal-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:1.75rem}.customer-portal[_ngcontent-%COMP%]   .progress-tracker[_ngcontent-%COMP%]   .step-icon[_ngcontent-%COMP%]{width:50px;height:50px;font-size:1.25rem}.customer-portal[_ngcontent-%COMP%]   .progress-tracker[_ngcontent-%COMP%]   .step-title[_ngcontent-%COMP%]{font-size:.8rem}}@media (prefers-color-scheme: dark){.customer-portal[_ngcontent-%COMP%]{background-color:#1a1a1a}.customer-portal[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]{background:#2d2d2d;color:#e9ecef}.customer-portal[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]{background:linear-gradient(135deg,#2d2d2d 0%,#3a3a3a 100%);border-bottom-color:#ffffff1a}.customer-portal[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%]{color:#e9ecef}.customer-portal[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]{background:#3a3a3a;border-color:#4a4a4a;color:#e9ecef}.customer-portal[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus{border-color:#007bff;background:#4a4a4a}.customer-portal[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{background:linear-gradient(135deg,#2d2d2d 0%,#3a3a3a 100%);color:#e9ecef;border-bottom-color:#4a4a4a}.customer-portal[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{border-bottom-color:#3a3a3a;color:#e9ecef}.customer-portal[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,rgba(0,123,255,.1) 0%,rgba(0,123,255,.05) 100%)}.customer-portal[_ngcontent-%COMP%]   .stats-card[_ngcontent-%COMP%]{background:#2d2d2d;border-color:#4a4a4a}.customer-portal[_ngcontent-%COMP%]   .stats-card[_ngcontent-%COMP%]:hover{background:linear-gradient(135deg,rgba(0,123,255,.05) 0%,rgba(32,201,151,.05) 100%)}.customer-portal[_ngcontent-%COMP%]   .stats-card[_ngcontent-%COMP%]   .stats-value[_ngcontent-%COMP%]{color:#e9ecef}.customer-portal[_ngcontent-%COMP%]   .stats-card[_ngcontent-%COMP%]   .stats-label[_ngcontent-%COMP%]{color:#adb5bd}}\"]\n      });\n    }\n  }\n  return CustomerPortalComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}