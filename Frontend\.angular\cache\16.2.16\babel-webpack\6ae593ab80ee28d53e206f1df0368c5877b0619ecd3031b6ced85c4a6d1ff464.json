{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/common\";\nfunction DriverFormComponent_option_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 39);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r2.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", option_r2.label, \" \");\n  }\n}\nfunction DriverFormComponent_option_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 39);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r3.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", option_r3.label, \" \");\n  }\n}\nexport let DriverFormComponent = /*#__PURE__*/(() => {\n  class DriverFormComponent {\n    constructor(fb) {\n      this.fb = fb;\n      this.driver = null;\n      this.submitted = new EventEmitter();\n      this.cancelled = new EventEmitter();\n      // Vehicle type options\n      this.vehicleTypeOptions = [{\n        value: 'Voiture',\n        label: 'Voiture'\n      }, {\n        value: 'Moto',\n        label: 'Moto'\n      }, {\n        value: 'Vélo',\n        label: 'Vélo'\n      }, {\n        value: 'Camionnette',\n        label: 'Camionnette'\n      }, {\n        value: 'Camion',\n        label: 'Camion'\n      }];\n      // Status options (backend expects integer enum values)\n      this.statusOptions = [{\n        value: 0,\n        label: 'Disponible'\n      }, {\n        value: 1,\n        label: 'Occupé'\n      }, {\n        value: 2,\n        label: 'En pause'\n      }, {\n        value: 3,\n        label: 'Hors ligne'\n      }];\n      this.driverForm = this.fb.group({\n        name: ['', Validators.required],\n        email: ['', [Validators.required, Validators.email]],\n        phone: ['', Validators.required],\n        vehicleType: ['', Validators.required],\n        vehicleId: [''],\n        licenseNumber: [''],\n        licenseExpiryDate: [''],\n        status: [0, Validators.required],\n        isAvailableForUrgentDeliveries: [true],\n        maxDeliveriesPerDay: [20, [Validators.required, Validators.min(1), Validators.max(50)]],\n        preferredZones: [''],\n        latitude: [48.8566, [Validators.required, Validators.min(-90), Validators.max(90)]],\n        longitude: [2.3522, [Validators.required, Validators.min(-180), Validators.max(180)]]\n      });\n    }\n    ngOnInit() {\n      if (this.driver) {\n        this.driverForm.patchValue({\n          name: this.driver.name,\n          email: this.driver.email,\n          phone: this.driver.phone,\n          vehicleType: this.driver.vehicleType,\n          vehicleId: this.driver.vehicleId || '',\n          licenseNumber: this.driver.licenseNumber || '',\n          licenseExpiryDate: this.driver.licenseExpiryDate ? this.formatDateForInput(this.driver.licenseExpiryDate) : '',\n          status: this.driver.status,\n          isAvailableForUrgentDeliveries: this.driver.isAvailableForUrgentDeliveries,\n          maxDeliveriesPerDay: this.driver.maxDeliveriesPerDay,\n          preferredZones: this.driver.preferredZones || '',\n          latitude: this.driver.currentLocation?.latitude || 48.8566,\n          longitude: this.driver.currentLocation?.longitude || 2.3522\n        });\n      }\n    }\n    formatDateForInput(date) {\n      const d = new Date(date);\n      return d.toISOString().slice(0, 10); // Format for date input (YYYY-MM-DD)\n    }\n\n    onSubmit() {\n      if (this.driverForm.valid) {\n        const formValue = this.driverForm.value;\n        // Map form data to driver object expected by backend\n        const driverData = {\n          id: '',\n          name: formValue.name,\n          email: formValue.email,\n          phone: formValue.phone,\n          vehicleType: formValue.vehicleType,\n          vehicleId: formValue.vehicleId || null,\n          rating: this.driver?.rating || 0,\n          totalDeliveries: this.driver?.totalDeliveries || 0,\n          onTimeRate: this.driver?.onTimeRate || 0,\n          status: formValue.status,\n          currentLocation: {\n            latitude: parseFloat(formValue.latitude),\n            longitude: parseFloat(formValue.longitude)\n          },\n          todayDeliveries: this.driver?.todayDeliveries || 0,\n          avgDeliveryTime: this.driver?.avgDeliveryTime || 0,\n          lastActive: this.driver?.lastActive || new Date().toISOString(),\n          profilePictureUrl: this.driver?.profilePictureUrl || null,\n          hireDate: this.driver?.hireDate || new Date().toISOString(),\n          licenseNumber: formValue.licenseNumber || null,\n          // LicenseExpiryDate is required in backend, so provide a default if empty\n          licenseExpiryDate: formValue.licenseExpiryDate ? new Date(formValue.licenseExpiryDate).toISOString() : new Date(Date.now() + 5 * 365 * 24 * 60 * 60 * 1000).toISOString(),\n          isAvailableForUrgentDeliveries: formValue.isAvailableForUrgentDeliveries,\n          preferredZones: formValue.preferredZones || null,\n          maxDeliveriesPerDay: formValue.maxDeliveriesPerDay\n        };\n        this.submitted.emit(driverData);\n      }\n    }\n    onCancel() {\n      this.cancelled.emit();\n    }\n    static {\n      this.ɵfac = function DriverFormComponent_Factory(t) {\n        return new (t || DriverFormComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: DriverFormComponent,\n        selectors: [[\"app-driver-form\"]],\n        inputs: {\n          driver: \"driver\"\n        },\n        outputs: {\n          submitted: \"submitted\",\n          cancelled: \"cancelled\"\n        },\n        decls: 83,\n        vars: 19,\n        consts: [[3, \"formGroup\", \"ngSubmit\"], [1, \"row\"], [1, \"col-md-6\"], [1, \"mb-3\"], [\"for\", \"name\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"name\", \"formControlName\", \"name\", 1, \"form-control\"], [1, \"invalid-feedback\"], [\"for\", \"email\", 1, \"form-label\"], [\"type\", \"email\", \"id\", \"email\", \"formControlName\", \"email\", 1, \"form-control\"], [\"for\", \"phone\", 1, \"form-label\"], [\"type\", \"tel\", \"id\", \"phone\", \"formControlName\", \"phone\", 1, \"form-control\"], [\"for\", \"vehicleType\", 1, \"form-label\"], [\"id\", \"vehicleType\", \"formControlName\", \"vehicleType\", 1, \"form-select\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"vehicleId\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"vehicleId\", \"formControlName\", \"vehicleId\", \"placeholder\", \"Ex: AB-123-CD\", 1, \"form-control\"], [\"for\", \"status\", 1, \"form-label\"], [\"id\", \"status\", \"formControlName\", \"status\", 1, \"form-select\"], [\"for\", \"licenseNumber\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"licenseNumber\", \"formControlName\", \"licenseNumber\", \"placeholder\", \"Ex: 123456789\", 1, \"form-control\"], [\"for\", \"licenseExpiryDate\", 1, \"form-label\"], [\"type\", \"date\", \"id\", \"licenseExpiryDate\", \"formControlName\", \"licenseExpiryDate\", 1, \"form-control\"], [\"for\", \"maxDeliveriesPerDay\", 1, \"form-label\"], [\"type\", \"number\", \"id\", \"maxDeliveriesPerDay\", \"formControlName\", \"maxDeliveriesPerDay\", \"min\", \"1\", \"max\", \"50\", 1, \"form-control\"], [1, \"form-label\"], [1, \"col-6\"], [\"type\", \"number\", \"placeholder\", \"Latitude\", \"formControlName\", \"latitude\", \"step\", \"0.000001\", 1, \"form-control\"], [\"type\", \"number\", \"placeholder\", \"Longitude\", \"formControlName\", \"longitude\", \"step\", \"0.000001\", 1, \"form-control\"], [1, \"form-text\", \"text-muted\"], [\"for\", \"preferredZones\", 1, \"form-label\"], [\"id\", \"preferredZones\", \"formControlName\", \"preferredZones\", \"rows\", \"2\", \"placeholder\", \"Ex: Centre-ville, Banlieue nord...\", 1, \"form-control\"], [1, \"form-check\"], [\"type\", \"checkbox\", \"id\", \"isAvailableForUrgentDeliveries\", \"formControlName\", \"isAvailableForUrgentDeliveries\", 1, \"form-check-input\"], [\"for\", \"isAvailableForUrgentDeliveries\", 1, \"form-check-label\"], [1, \"d-flex\", \"gap-2\", \"justify-content-end\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"disabled\"], [1, \"fa-solid\", \"fa-save\", \"me-2\"], [3, \"value\"]],\n        template: function DriverFormComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"form\", 0);\n            i0.ɵɵlistener(\"ngSubmit\", function DriverFormComponent_Template_form_ngSubmit_0_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"label\", 4);\n            i0.ɵɵtext(5, \"Nom complet *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(6, \"input\", 5);\n            i0.ɵɵelementStart(7, \"div\", 6);\n            i0.ɵɵtext(8, \" Le nom est requis \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(9, \"div\", 3)(10, \"label\", 7);\n            i0.ɵɵtext(11, \"Email *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(12, \"input\", 8);\n            i0.ɵɵelementStart(13, \"div\", 6);\n            i0.ɵɵtext(14, \" Un email valide est requis \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(15, \"div\", 3)(16, \"label\", 9);\n            i0.ɵɵtext(17, \"T\\u00E9l\\u00E9phone *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(18, \"input\", 10);\n            i0.ɵɵelementStart(19, \"div\", 6);\n            i0.ɵɵtext(20, \" Le num\\u00E9ro de t\\u00E9l\\u00E9phone est requis \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(21, \"div\", 3)(22, \"label\", 11);\n            i0.ɵɵtext(23, \"Type de v\\u00E9hicule *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(24, \"select\", 12)(25, \"option\", 13);\n            i0.ɵɵtext(26, \"S\\u00E9lectionner un type de v\\u00E9hicule\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(27, DriverFormComponent_option_27_Template, 2, 2, \"option\", 14);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(28, \"div\", 6);\n            i0.ɵɵtext(29, \" Le type de v\\u00E9hicule est requis \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(30, \"div\", 3)(31, \"label\", 15);\n            i0.ɵɵtext(32, \"Immatriculation du v\\u00E9hicule\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(33, \"input\", 16);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(34, \"div\", 3)(35, \"label\", 17);\n            i0.ɵɵtext(36, \"Statut *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(37, \"select\", 18);\n            i0.ɵɵtemplate(38, DriverFormComponent_option_38_Template, 2, 2, \"option\", 14);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(39, \"div\", 2)(40, \"div\", 3)(41, \"label\", 19);\n            i0.ɵɵtext(42, \"Num\\u00E9ro de permis\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(43, \"input\", 20);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(44, \"div\", 3)(45, \"label\", 21);\n            i0.ɵɵtext(46, \"Date d'expiration du permis\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(47, \"input\", 22);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(48, \"div\", 3)(49, \"label\", 23);\n            i0.ɵɵtext(50, \"Livraisons max par jour *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(51, \"input\", 24);\n            i0.ɵɵelementStart(52, \"div\", 6);\n            i0.ɵɵtext(53, \" Nombre entre 1 et 50 requis \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(54, \"div\", 3)(55, \"label\", 25);\n            i0.ɵɵtext(56, \"Position actuelle\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(57, \"div\", 1)(58, \"div\", 26);\n            i0.ɵɵelement(59, \"input\", 27);\n            i0.ɵɵelementStart(60, \"div\", 6);\n            i0.ɵɵtext(61, \" Latitude invalide \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(62, \"div\", 26);\n            i0.ɵɵelement(63, \"input\", 28);\n            i0.ɵɵelementStart(64, \"div\", 6);\n            i0.ɵɵtext(65, \" Longitude invalide \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(66, \"small\", 29);\n            i0.ɵɵtext(67, \" Position par d\\u00E9faut: Paris (48.8566, 2.3522) \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(68, \"div\", 3)(69, \"label\", 30);\n            i0.ɵɵtext(70, \"Zones pr\\u00E9f\\u00E9r\\u00E9es\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(71, \"textarea\", 31);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(72, \"div\", 3)(73, \"div\", 32);\n            i0.ɵɵelement(74, \"input\", 33);\n            i0.ɵɵelementStart(75, \"label\", 34);\n            i0.ɵɵtext(76, \" Disponible pour les livraisons urgentes \");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(77, \"div\", 35)(78, \"button\", 36);\n            i0.ɵɵlistener(\"click\", function DriverFormComponent_Template_button_click_78_listener() {\n              return ctx.onCancel();\n            });\n            i0.ɵɵtext(79, \"Annuler\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(80, \"button\", 37);\n            i0.ɵɵelement(81, \"i\", 38);\n            i0.ɵɵtext(82);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            let tmp_1_0;\n            let tmp_2_0;\n            let tmp_3_0;\n            let tmp_4_0;\n            let tmp_7_0;\n            let tmp_8_0;\n            let tmp_9_0;\n            i0.ɵɵproperty(\"formGroup\", ctx.driverForm);\n            i0.ɵɵadvance(6);\n            i0.ɵɵclassProp(\"is-invalid\", ((tmp_1_0 = ctx.driverForm.get(\"name\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx.driverForm.get(\"name\")) == null ? null : tmp_1_0.touched));\n            i0.ɵɵadvance(6);\n            i0.ɵɵclassProp(\"is-invalid\", ((tmp_2_0 = ctx.driverForm.get(\"email\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.driverForm.get(\"email\")) == null ? null : tmp_2_0.touched));\n            i0.ɵɵadvance(6);\n            i0.ɵɵclassProp(\"is-invalid\", ((tmp_3_0 = ctx.driverForm.get(\"phone\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx.driverForm.get(\"phone\")) == null ? null : tmp_3_0.touched));\n            i0.ɵɵadvance(6);\n            i0.ɵɵclassProp(\"is-invalid\", ((tmp_4_0 = ctx.driverForm.get(\"vehicleType\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx.driverForm.get(\"vehicleType\")) == null ? null : tmp_4_0.touched));\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngForOf\", ctx.vehicleTypeOptions);\n            i0.ɵɵadvance(11);\n            i0.ɵɵproperty(\"ngForOf\", ctx.statusOptions);\n            i0.ɵɵadvance(13);\n            i0.ɵɵclassProp(\"is-invalid\", ((tmp_7_0 = ctx.driverForm.get(\"maxDeliveriesPerDay\")) == null ? null : tmp_7_0.invalid) && ((tmp_7_0 = ctx.driverForm.get(\"maxDeliveriesPerDay\")) == null ? null : tmp_7_0.touched));\n            i0.ɵɵadvance(8);\n            i0.ɵɵclassProp(\"is-invalid\", ((tmp_8_0 = ctx.driverForm.get(\"latitude\")) == null ? null : tmp_8_0.invalid) && ((tmp_8_0 = ctx.driverForm.get(\"latitude\")) == null ? null : tmp_8_0.touched));\n            i0.ɵɵadvance(4);\n            i0.ɵɵclassProp(\"is-invalid\", ((tmp_9_0 = ctx.driverForm.get(\"longitude\")) == null ? null : tmp_9_0.invalid) && ((tmp_9_0 = ctx.driverForm.get(\"longitude\")) == null ? null : tmp_9_0.touched));\n            i0.ɵɵadvance(17);\n            i0.ɵɵproperty(\"disabled\", !ctx.driverForm.valid);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate1(\" \", ctx.driver ? \"Mettre \\u00E0 jour\" : \"Cr\\u00E9er le livreur\", \" \");\n          }\n        },\n        dependencies: [i2.NgForOf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.CheckboxControlValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MinValidator, i1.MaxValidator, i1.FormGroupDirective, i1.FormControlName]\n      });\n    }\n  }\n  return DriverFormComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}