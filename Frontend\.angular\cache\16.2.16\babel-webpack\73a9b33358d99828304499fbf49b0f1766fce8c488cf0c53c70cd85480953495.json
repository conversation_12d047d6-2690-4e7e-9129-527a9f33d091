{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { OptimizationObjective } from '../../core/models/route-optimization.model';\nimport { DeliveryStatus, TrafficCondition, WeatherCondition } from '../../core/models/delivery.model';\nimport { DriverStatus } from '../../core/models/driver.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../core/services/optimization.service\";\nimport * as i3 from \"../../core/services/delivery.service\";\nimport * as i4 from \"../../core/services/driver.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"../../shared/components/sidebar/sidebar.component\";\nimport * as i7 from \"../../shared/components/header/header.component\";\nimport * as i8 from \"../../shared/components/status-badge/status-badge.component\";\nimport * as i9 from \"../../shared/components/map-view/map-view.component\";\nfunction RouteOptimizationComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"div\", 8)(2, \"span\", 9);\n    i0.ɵɵtext(3, \"Chargement...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 10);\n    i0.ɵɵtext(5, \"Chargement des donn\\u00E9es...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction RouteOptimizationComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction RouteOptimizationComponent_ng_container_7_option_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const driver_r14 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", driver_r14.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", driver_r14.name, \" (\", driver_r14.vehicleType, \") \");\n  }\n}\nfunction RouteOptimizationComponent_ng_container_7_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵtext(1, \" Veuillez s\\u00E9lectionner un livreur \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RouteOptimizationComponent_ng_container_7_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"input\", 51);\n    i0.ɵɵlistener(\"change\", function RouteOptimizationComponent_ng_container_7_div_21_Template_input_change_1_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r17);\n      const delivery_r15 = restoredCtx.$implicit;\n      const ctx_r16 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r16.onDeliverySelectionChange(delivery_r15.id, $event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\", 52);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const delivery_r15 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"id\", \"delivery-\" + delivery_r15.id)(\"value\", delivery_r15.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"for\", \"delivery-\" + delivery_r15.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", delivery_r15.customerName, \" - \", delivery_r15.address, \" \");\n  }\n}\nfunction RouteOptimizationComponent_ng_container_7_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵtext(1, \" Veuillez s\\u00E9lectionner au moins une livraison \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RouteOptimizationComponent_ng_container_7_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵtext(1, \" Aucune livraison en attente disponible \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RouteOptimizationComponent_ng_container_7_span_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 54);\n  }\n}\nfunction RouteOptimizationComponent_ng_container_7_div_54_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"div\", 62)(2, \"span\", 9);\n    i0.ɵɵtext(3, \"Chargement...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction RouteOptimizationComponent_ng_container_7_div_54_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"div\", 64);\n    i0.ɵɵelement(2, \"app-status-badge\", 65);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\")(4, \"p\", 66);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 66);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"number\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"status\", ctx_r19.getTrafficConditionText(ctx_r19.trafficData.condition))(\"variant\", ctx_r19.getTrafficConditionClass(ctx_r19.trafficData.condition));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Vitesse moyenne: \", i0.ɵɵpipeBind2(6, 4, ctx_r19.trafficData.averageSpeed, \"1.0-0\"), \" km/h\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Congestion: \", i0.ɵɵpipeBind2(9, 7, ctx_r19.trafficData.congestion * 100, \"1.0-0\"), \"%\");\n  }\n}\nfunction RouteOptimizationComponent_ng_container_7_div_54_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67);\n    i0.ɵɵtext(1, \" Donn\\u00E9es de trafic non disponibles \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RouteOptimizationComponent_ng_container_7_div_54_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"div\", 62)(2, \"span\", 9);\n    i0.ɵɵtext(3, \"Chargement...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction RouteOptimizationComponent_ng_container_7_div_54_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"div\", 64);\n    i0.ɵɵelement(2, \"app-status-badge\", 65);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\")(4, \"p\", 66);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 66);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"number\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"status\", ctx_r22.getWeatherConditionText(ctx_r22.weatherData.condition))(\"variant\", ctx_r22.getWeatherConditionClass(ctx_r22.weatherData.condition));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Temp\\u00E9rature: \", i0.ɵɵpipeBind2(6, 4, ctx_r22.weatherData.temperature, \"1.0-0\"), \"\\u00B0C\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Visibilit\\u00E9: \", i0.ɵɵpipeBind2(9, 7, ctx_r22.weatherData.visibility, \"1.0-0\"), \" km\");\n  }\n}\nfunction RouteOptimizationComponent_ng_container_7_div_54_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67);\n    i0.ɵɵtext(1, \" Donn\\u00E9es m\\u00E9t\\u00E9o non disponibles \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RouteOptimizationComponent_ng_container_7_div_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"div\", 15)(2, \"h5\", 16);\n    i0.ɵɵelement(3, \"i\", 56);\n    i0.ɵɵtext(4, \" Conditions externes \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 18)(6, \"div\", 32)(7, \"h6\", 57);\n    i0.ɵɵtext(8, \"Conditions de trafic\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, RouteOptimizationComponent_ng_container_7_div_54_div_9_Template, 4, 0, \"div\", 58);\n    i0.ɵɵtemplate(10, RouteOptimizationComponent_ng_container_7_div_54_div_10_Template, 10, 10, \"div\", 59);\n    i0.ɵɵtemplate(11, RouteOptimizationComponent_ng_container_7_div_54_div_11_Template, 2, 0, \"div\", 60);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\")(13, \"h6\", 57);\n    i0.ɵɵtext(14, \"Conditions m\\u00E9t\\u00E9o\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, RouteOptimizationComponent_ng_container_7_div_54_div_15_Template, 4, 0, \"div\", 58);\n    i0.ɵɵtemplate(16, RouteOptimizationComponent_ng_container_7_div_54_div_16_Template, 10, 10, \"div\", 59);\n    i0.ɵɵtemplate(17, RouteOptimizationComponent_ng_container_7_div_54_div_17_Template, 2, 0, \"div\", 60);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.trafficLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r9.trafficLoading && ctx_r9.trafficData);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r9.trafficLoading && !ctx_r9.trafficData);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.weatherLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r9.weatherLoading && ctx_r9.weatherData);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r9.weatherLoading && !ctx_r9.weatherData);\n  }\n}\nfunction RouteOptimizationComponent_ng_container_7_div_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 68);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 69);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"number\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r10.optimizedRoute.routes.length, \" arr\\u00EAts\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(5, 2, ctx_r10.optimizedRoute.optimizationScore, \"1.0-0\"), \"% d'efficacit\\u00E9\");\n  }\n}\nfunction RouteOptimizationComponent_ng_container_7_div_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"div\", 8)(2, \"span\", 9);\n    i0.ɵɵtext(3, \"Chargement...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 10);\n    i0.ɵɵtext(5, \"Optimisation de l'itin\\u00E9raire...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction RouteOptimizationComponent_ng_container_7_div_64_tr_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\")(8, \"span\", 80);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"td\");\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\");\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const stop_r25 = ctx.$implicit;\n    const ctx_r24 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stop_r25.order);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stop_r25.customerName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stop_r25.address);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"bg-danger\", stop_r25.priority === \"Urgent\")(\"bg-warning\", stop_r25.priority === \"High\")(\"bg-info\", stop_r25.priority === \"Medium\")(\"bg-secondary\", stop_r25.priority === \"Low\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", stop_r25.priority, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(12, 15, stop_r25.distance, \"1.1-1\"), \" km\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r24.formatTime(stop_r25.estimatedTime));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(17, 18, stop_r25.estimatedArrival, \"HH:mm\"));\n  }\n}\nfunction RouteOptimizationComponent_ng_container_7_div_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"app-map-view\", 70);\n    i0.ɵɵelementStart(2, \"div\", 71)(3, \"div\", 72)(4, \"div\", 73)(5, \"div\", 74)(6, \"div\", 75);\n    i0.ɵɵtext(7, \"Distance totale\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 76);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"number\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 73)(12, \"div\", 74)(13, \"div\", 75);\n    i0.ɵɵtext(14, \"Temps total\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 76);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 73)(18, \"div\", 74)(19, \"div\", 75);\n    i0.ɵɵtext(20, \"Efficacit\\u00E9 carburant\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 76);\n    i0.ɵɵtext(22);\n    i0.ɵɵpipe(23, \"number\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"div\", 73)(25, \"div\", 74)(26, \"div\", 75);\n    i0.ɵɵtext(27, \"M\\u00E9thode\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 76);\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(30, \"h6\", 20);\n    i0.ɵɵtext(31, \"Arr\\u00EAts planifi\\u00E9s\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 77)(33, \"table\", 78)(34, \"thead\")(35, \"tr\")(36, \"th\");\n    i0.ɵɵtext(37, \"#\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"th\");\n    i0.ɵɵtext(39, \"Client\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"th\");\n    i0.ɵɵtext(41, \"Adresse\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"th\");\n    i0.ɵɵtext(43, \"Priorit\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"th\");\n    i0.ɵɵtext(45, \"Distance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"th\");\n    i0.ɵɵtext(47, \"Temps estim\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"th\");\n    i0.ɵɵtext(49, \"Heure d'arriv\\u00E9e\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(50, \"tbody\");\n    i0.ɵɵtemplate(51, RouteOptimizationComponent_ng_container_7_div_64_tr_51_Template, 18, 21, \"tr\", 79);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"deliveries\", ctx_r12.getOptimizedDeliveries())(\"drivers\", ctx_r12.getSelectedDriverArray())(\"routes\", ctx_r12.getOptimizedRouteArray())(\"height\", 400);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(10, 9, ctx_r12.optimizedRoute.totalDistance, \"1.1-1\"), \" km\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r12.formatTime(ctx_r12.optimizedRoute.totalTime));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(23, 12, ctx_r12.optimizedRoute.fuelEfficiency, \"1.1-1\"), \"%\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r12.getOptimizationMethodText());\n    i0.ɵɵadvance(22);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r12.getSortedRouteStops());\n  }\n}\nfunction RouteOptimizationComponent_ng_container_7_div_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 81);\n    i0.ɵɵelement(1, \"i\", 82);\n    i0.ɵɵelementStart(2, \"h5\");\n    i0.ɵɵtext(3, \"Aucun itin\\u00E9raire optimis\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5, \"S\\u00E9lectionnez un livreur et des livraisons, puis cliquez sur \\\"Optimiser l'itin\\u00E9raire\\\"\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction RouteOptimizationComponent_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 12)(2, \"div\", 13)(3, \"div\", 14)(4, \"div\", 15)(5, \"h5\", 16);\n    i0.ɵɵelement(6, \"i\", 17);\n    i0.ɵɵtext(7, \" Param\\u00E8tres d'optimisation \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 18)(9, \"form\", 19);\n    i0.ɵɵlistener(\"ngSubmit\", function RouteOptimizationComponent_ng_container_7_Template_form_ngSubmit_9_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.optimizeRoute());\n    });\n    i0.ɵɵelementStart(10, \"div\", 20)(11, \"label\", 21);\n    i0.ɵɵtext(12, \"Livreur\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"select\", 22);\n    i0.ɵɵlistener(\"change\", function RouteOptimizationComponent_ng_container_7_Template_select_change_13_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r28 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r28.onDriverChange());\n    });\n    i0.ɵɵelementStart(14, \"option\", 23);\n    i0.ɵɵtext(15, \"S\\u00E9lectionnez un livreur\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, RouteOptimizationComponent_ng_container_7_option_16_Template, 2, 3, \"option\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(17, RouteOptimizationComponent_ng_container_7_div_17_Template, 2, 0, \"div\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 20)(19, \"label\", 26);\n    i0.ɵɵtext(20, \"Livraisons \\u00E0 optimiser\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(21, RouteOptimizationComponent_ng_container_7_div_21_Template, 4, 5, \"div\", 27);\n    i0.ɵɵtemplate(22, RouteOptimizationComponent_ng_container_7_div_22_Template, 2, 0, \"div\", 25);\n    i0.ɵɵtemplate(23, RouteOptimizationComponent_ng_container_7_div_23_Template, 2, 0, \"div\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 20)(25, \"label\", 29);\n    i0.ɵɵtext(26, \"Strat\\u00E9gie d'optimisation\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"select\", 30)(28, \"option\", 31);\n    i0.ɵɵtext(29, \"\\u00C9quilibr\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"option\", 31);\n    i0.ɵɵtext(31, \"Minimiser la distance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"option\", 31);\n    i0.ɵɵtext(33, \"Minimiser le temps\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"option\", 31);\n    i0.ɵɵtext(35, \"Maximiser les livraisons\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(36, \"div\", 32)(37, \"label\", 26);\n    i0.ɵɵtext(38, \"Options suppl\\u00E9mentaires\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"div\", 33);\n    i0.ɵɵelement(40, \"input\", 34);\n    i0.ɵɵelementStart(41, \"label\", 35);\n    i0.ɵɵtext(42, \" Prendre en compte le trafic \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(43, \"div\", 33);\n    i0.ɵɵelement(44, \"input\", 36);\n    i0.ɵɵelementStart(45, \"label\", 37);\n    i0.ɵɵtext(46, \" Prendre en compte la m\\u00E9t\\u00E9o \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(47, \"div\", 38);\n    i0.ɵɵelement(48, \"input\", 39);\n    i0.ɵɵelementStart(49, \"label\", 40);\n    i0.ɵɵtext(50, \" Prioriser les livraisons urgentes \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(51, \"button\", 41);\n    i0.ɵɵtemplate(52, RouteOptimizationComponent_ng_container_7_span_52_Template, 1, 0, \"span\", 42);\n    i0.ɵɵtext(53, \" Optimiser l'itin\\u00E9raire \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(54, RouteOptimizationComponent_ng_container_7_div_54_Template, 18, 6, \"div\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(55, \"div\", 44)(56, \"div\", 45)(57, \"div\", 46)(58, \"h5\", 16);\n    i0.ɵɵelement(59, \"i\", 47);\n    i0.ɵɵtext(60, \" Itin\\u00E9raire optimis\\u00E9 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(61, RouteOptimizationComponent_ng_container_7_div_61_Template, 6, 5, \"div\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(62, \"div\", 48);\n    i0.ɵɵtemplate(63, RouteOptimizationComponent_ng_container_7_div_63_Template, 6, 0, \"div\", 4);\n    i0.ɵɵtemplate(64, RouteOptimizationComponent_ng_container_7_div_64_Template, 52, 15, \"div\", 6);\n    i0.ɵɵtemplate(65, RouteOptimizationComponent_ng_container_7_div_65_Template, 6, 0, \"div\", 49);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    let tmp_2_0;\n    let tmp_4_0;\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"formGroup\", ctx_r2.optimizationForm);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.drivers);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx_r2.optimizationForm.get(\"driverId\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx_r2.optimizationForm.get(\"driverId\")) == null ? null : tmp_2_0.touched));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.pendingDeliveries);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx_r2.optimizationForm.get(\"deliveryIds\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx_r2.optimizationForm.get(\"deliveryIds\")) == null ? null : tmp_4_0.touched));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.pendingDeliveries.length === 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"value\", \"Balanced\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", \"MinimizeDistance\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", \"MinimizeTime\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", \"MaximizeDeliveries\");\n    i0.ɵɵadvance(17);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.optimizationForm.invalid || ctx_r2.optimizationLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.optimizationLoading);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedDriver);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.optimizedRoute);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.optimizationLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.optimizationLoading && ctx_r2.optimizedRoute);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.optimizationLoading && !ctx_r2.optimizedRoute);\n  }\n}\nexport let RouteOptimizationComponent = /*#__PURE__*/(() => {\n  class RouteOptimizationComponent {\n    constructor(formBuilder, optimizationService, deliveryService, driverService) {\n      this.formBuilder = formBuilder;\n      this.optimizationService = optimizationService;\n      this.deliveryService = deliveryService;\n      this.driverService = driverService;\n      this.drivers = [];\n      this.deliveries = [];\n      this.pendingDeliveries = [];\n      this.selectedDriver = null;\n      this.optimizedRoute = null;\n      this.trafficData = null;\n      this.weatherData = null;\n      this.loading = true;\n      this.driversLoading = false;\n      this.deliveriesLoading = false;\n      this.optimizationLoading = false;\n      this.trafficLoading = false;\n      this.weatherLoading = false;\n      this.error = '';\n      this.subscriptions = [];\n    }\n    ngOnInit() {\n      this.initForm();\n      this.loadData();\n    }\n    ngOnDestroy() {\n      this.subscriptions.forEach(sub => sub.unsubscribe());\n    }\n    initForm() {\n      this.optimizationForm = this.formBuilder.group({\n        driverId: ['', Validators.required],\n        deliveryIds: [[], Validators.required],\n        considerTraffic: [true],\n        considerWeather: [true],\n        prioritizeUrgent: [true],\n        strategy: [OptimizationObjective.BalanceWorkload]\n      });\n    }\n    loadData() {\n      this.loading = true;\n      this.driversLoading = true;\n      this.deliveriesLoading = true;\n      // Load drivers\n      this.subscriptions.push(this.driverService.getDrivers().subscribe({\n        next: drivers => {\n          this.drivers = drivers.filter(d => d.status === DriverStatus.Available);\n          this.driversLoading = false;\n          this.checkLoading();\n        },\n        error: err => {\n          console.error('Error loading drivers', err);\n          this.error = 'Erreur lors du chargement des livreurs';\n          this.driversLoading = false;\n          this.checkLoading();\n        }\n      }));\n      // Load deliveries\n      this.subscriptions.push(this.deliveryService.getDeliveries().subscribe({\n        next: deliveries => {\n          this.deliveries = deliveries;\n          this.pendingDeliveries = deliveries.filter(d => d.status === DeliveryStatus.Pending || d.status === DeliveryStatus.Delayed);\n          this.deliveriesLoading = false;\n          this.checkLoading();\n        },\n        error: err => {\n          console.error('Error loading deliveries', err);\n          this.error = 'Erreur lors du chargement des livraisons';\n          this.deliveriesLoading = false;\n          this.checkLoading();\n        }\n      }));\n    }\n    checkLoading() {\n      this.loading = this.driversLoading || this.deliveriesLoading;\n    }\n    onDriverChange() {\n      const driverId = this.optimizationForm.get('driverId')?.value;\n      if (!driverId) {\n        this.selectedDriver = null;\n        return;\n      }\n      this.selectedDriver = this.drivers.find(d => d.id === driverId) || null;\n      if (this.selectedDriver) {\n        // Load existing route for this driver\n        this.loadOptimizedRoute(driverId);\n        // Load traffic and weather data for driver's location\n        this.loadTrafficData(this.selectedDriver.currentLocation.latitude, this.selectedDriver.currentLocation.longitude);\n        this.loadWeatherData(this.selectedDriver.currentLocation.latitude, this.selectedDriver.currentLocation.longitude);\n      }\n    }\n    loadOptimizedRoute(driverId) {\n      this.optimizationLoading = true;\n      this.subscriptions.push(this.optimizationService.getOptimizedRoute(driverId).subscribe({\n        next: route => {\n          this.optimizedRoute = route;\n          this.optimizationLoading = false;\n        },\n        error: err => {\n          console.error('Error loading optimized route', err);\n          this.optimizedRoute = null;\n          this.optimizationLoading = false;\n        }\n      }));\n    }\n    loadTrafficData(latitude, longitude) {\n      this.trafficLoading = true;\n      this.subscriptions.push(this.optimizationService.getTrafficData(latitude, longitude).subscribe({\n        next: data => {\n          this.trafficData = data;\n          this.trafficLoading = false;\n        },\n        error: err => {\n          console.error('Error loading traffic data', err);\n          this.trafficData = null;\n          this.trafficLoading = false;\n        }\n      }));\n    }\n    loadWeatherData(latitude, longitude) {\n      this.weatherLoading = true;\n      this.subscriptions.push(this.optimizationService.getWeatherData(latitude, longitude).subscribe({\n        next: data => {\n          this.weatherData = data;\n          this.weatherLoading = false;\n        },\n        error: err => {\n          console.error('Error loading weather data', err);\n          this.weatherData = null;\n          this.weatherLoading = false;\n        }\n      }));\n    }\n    optimizeRoute() {\n      if (this.optimizationForm.invalid) {\n        return;\n      }\n      this.optimizationLoading = true;\n      const request = {\n        driverId: this.optimizationForm.get('driverId')?.value,\n        deliveryIds: this.optimizationForm.get('deliveryIds')?.value,\n        considerTraffic: this.optimizationForm.get('considerTraffic')?.value,\n        considerWeather: this.optimizationForm.get('considerWeather')?.value,\n        prioritizeUrgent: this.optimizationForm.get('prioritizeUrgent')?.value,\n        strategy: this.optimizationForm.get('strategy')?.value\n      };\n      this.subscriptions.push(this.optimizationService.optimizeRoute(request).subscribe({\n        next: route => {\n          this.optimizedRoute = route;\n          this.optimizationLoading = false;\n        },\n        error: err => {\n          console.error('Error optimizing route', err);\n          this.error = 'Erreur lors de l\\'optimisation de l\\'itinéraire';\n          this.optimizationLoading = false;\n        }\n      }));\n    }\n    getStrategyText(strategy) {\n      switch (strategy) {\n        case OptimizationObjective.MinimizeDistance:\n          return 'Minimiser la distance';\n        case OptimizationObjective.MinimizeTime:\n          return 'Minimiser le temps';\n        case OptimizationObjective.MaximizeDeliveries:\n          return 'Maximiser les livraisons';\n        case OptimizationObjective.BalanceWorkload:\n          return 'Équilibré';\n        default:\n          return strategy;\n      }\n    }\n    getTrafficConditionText(condition) {\n      switch (condition) {\n        case TrafficCondition.Light:\n          return 'Fluide';\n        case TrafficCondition.Moderate:\n          return 'Modéré';\n        case TrafficCondition.Heavy:\n          return 'Dense';\n        case TrafficCondition.Severe:\n          return 'Très dense';\n        default:\n          return condition.toString();\n      }\n    }\n    getWeatherConditionText(condition) {\n      switch (condition) {\n        case WeatherCondition.Clear:\n          return 'Dégagé';\n        case WeatherCondition.Cloudy:\n          return 'Nuageux';\n        case WeatherCondition.Rainy:\n          return 'Pluvieux';\n        case WeatherCondition.Snowy:\n          return 'Neigeux';\n        case WeatherCondition.Stormy:\n          return 'Orageux';\n        default:\n          return condition.toString();\n      }\n    }\n    getTrafficConditionClass(condition) {\n      switch (condition) {\n        case TrafficCondition.Light:\n          return 'success';\n        case TrafficCondition.Moderate:\n          return 'info';\n        case TrafficCondition.Heavy:\n          return 'warning';\n        case TrafficCondition.Severe:\n          return 'danger';\n        default:\n          return 'default';\n      }\n    }\n    getWeatherConditionClass(condition) {\n      switch (condition) {\n        case WeatherCondition.Clear:\n          return 'success';\n        case WeatherCondition.Cloudy:\n          return 'info';\n        case WeatherCondition.Rainy:\n          return 'warning';\n        case WeatherCondition.Snowy:\n          return 'warning';\n        case WeatherCondition.Stormy:\n          return 'danger';\n        default:\n          return 'default';\n      }\n    }\n    formatTime(minutes) {\n      const hours = Math.floor(minutes / 60);\n      const mins = Math.floor(minutes % 60);\n      if (hours > 0) {\n        return `${hours}h ${mins}min`;\n      } else {\n        return `${mins} min`;\n      }\n    }\n    onDeliverySelectionChange(deliveryId, event) {\n      const isChecked = event.target.checked;\n      const currentIds = this.optimizationForm.get('deliveryIds')?.value || [];\n      if (isChecked) {\n        if (!currentIds.includes(deliveryId)) {\n          this.optimizationForm.get('deliveryIds')?.setValue([...currentIds, deliveryId]);\n        }\n      } else {\n        this.optimizationForm.get('deliveryIds')?.setValue(currentIds.filter(id => id !== deliveryId));\n      }\n    }\n    getOptimizedDeliveries() {\n      if (!this.optimizedRoute) return [];\n      return this.deliveries.filter(d => this.optimizedRoute.routes.some(r => r.deliveryId === d.id));\n    }\n    getSelectedDriverArray() {\n      return this.selectedDriver ? [this.selectedDriver] : [];\n    }\n    getOptimizedRouteArray() {\n      return this.optimizedRoute ? [this.optimizedRoute] : [];\n    }\n    getSortedRouteStops() {\n      if (!this.optimizedRoute) return [];\n      return [...this.optimizedRoute.routes].sort((a, b) => a.order - b.order);\n    }\n    getOptimizationMethodText() {\n      if (!this.optimizedRoute) return '';\n      const method = parseInt(this.optimizedRoute.optimizationMethod, 10);\n      return this.getStrategyText(method);\n    }\n    static {\n      this.ɵfac = function RouteOptimizationComponent_Factory(t) {\n        return new (t || RouteOptimizationComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.OptimizationService), i0.ɵɵdirectiveInject(i3.DeliveryService), i0.ɵɵdirectiveInject(i4.DriverService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: RouteOptimizationComponent,\n        selectors: [[\"app-route-optimization\"]],\n        decls: 8,\n        vars: 3,\n        consts: [[1, \"optimization-container\"], [1, \"optimization-content\"], [\"title\", \"Optimisation des itin\\u00E9raires\", \"subtitle\", \"Optimisez les trajets de vos livreurs pour maximiser l'efficacit\\u00E9\"], [1, \"optimization-body\", \"p-4\"], [\"class\", \"text-center py-5\", 4, \"ngIf\"], [\"class\", \"alert alert-danger\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"text-center\", \"py-5\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"mt-2\"], [1, \"alert\", \"alert-danger\"], [1, \"row\", \"g-4\"], [1, \"col-lg-4\"], [1, \"card\"], [1, \"card-header\"], [1, \"card-title\", \"mb-0\"], [1, \"fa-solid\", \"fa-sliders\", \"me-2\", \"text-primary\"], [1, \"card-body\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"mb-3\"], [\"for\", \"driverSelect\", 1, \"form-label\"], [\"id\", \"driverSelect\", \"formControlName\", \"driverId\", 1, \"form-select\", 3, \"change\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-danger small mt-1\", 4, \"ngIf\"], [1, \"form-label\"], [\"class\", \"form-check\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-muted small mt-1\", 4, \"ngIf\"], [\"for\", \"strategySelect\", 1, \"form-label\"], [\"id\", \"strategySelect\", \"formControlName\", \"strategy\", 1, \"form-select\"], [3, \"value\"], [1, \"mb-4\"], [1, \"form-check\", \"mb-2\"], [\"type\", \"checkbox\", \"id\", \"considerTraffic\", \"formControlName\", \"considerTraffic\", 1, \"form-check-input\"], [\"for\", \"considerTraffic\", 1, \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"considerWeather\", \"formControlName\", \"considerWeather\", 1, \"form-check-input\"], [\"for\", \"considerWeather\", 1, \"form-check-label\"], [1, \"form-check\"], [\"type\", \"checkbox\", \"id\", \"prioritizeUrgent\", \"formControlName\", \"prioritizeUrgent\", 1, \"form-check-input\"], [\"for\", \"prioritizeUrgent\", 1, \"form-check-label\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"w-100\", 3, \"disabled\"], [\"class\", \"spinner-border spinner-border-sm me-2\", \"role\", \"status\", \"aria-hidden\", \"true\", 4, \"ngIf\"], [\"class\", \"card mt-4\", 4, \"ngIf\"], [1, \"col-lg-8\"], [1, \"card\", \"mb-4\"], [1, \"card-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"fa-solid\", \"fa-route\", \"me-2\", \"text-primary\"], [1, \"card-body\", \"p-0\"], [\"class\", \"text-center py-5 text-muted\", 4, \"ngIf\"], [1, \"text-danger\", \"small\", \"mt-1\"], [\"type\", \"checkbox\", 1, \"form-check-input\", 3, \"id\", \"value\", \"change\"], [1, \"form-check-label\", 3, \"for\"], [1, \"text-muted\", \"small\", \"mt-1\"], [\"role\", \"status\", \"aria-hidden\", \"true\", 1, \"spinner-border\", \"spinner-border-sm\", \"me-2\"], [1, \"card\", \"mt-4\"], [1, \"fa-solid\", \"fa-cloud\", \"me-2\", \"text-primary\"], [1, \"text-muted\", \"mb-2\"], [\"class\", \"text-center py-2\", 4, \"ngIf\"], [\"class\", \"d-flex align-items-center\", 4, \"ngIf\"], [\"class\", \"text-muted small\", 4, \"ngIf\"], [1, \"text-center\", \"py-2\"], [\"role\", \"status\", 1, \"spinner-border\", \"spinner-border-sm\", \"text-primary\"], [1, \"d-flex\", \"align-items-center\"], [1, \"me-3\"], [3, \"status\", \"variant\"], [1, \"mb-0\", \"small\"], [1, \"text-muted\", \"small\"], [1, \"badge\", \"bg-primary\", \"me-2\"], [1, \"badge\", \"bg-success\"], [3, \"deliveries\", \"drivers\", \"routes\", \"height\"], [1, \"p-4\"], [1, \"row\", \"g-3\", \"mb-4\"], [1, \"col-md-3\"], [1, \"metric-mini-card\"], [1, \"metric-mini-title\"], [1, \"metric-mini-value\"], [1, \"table-responsive\"], [1, \"table\", \"table-hover\"], [4, \"ngFor\", \"ngForOf\"], [1, \"badge\"], [1, \"text-center\", \"py-5\", \"text-muted\"], [1, \"fa-solid\", \"fa-route\", \"fa-3x\", \"mb-3\"]],\n        template: function RouteOptimizationComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0);\n            i0.ɵɵelement(1, \"app-sidebar\");\n            i0.ɵɵelementStart(2, \"div\", 1);\n            i0.ɵɵelement(3, \"app-header\", 2);\n            i0.ɵɵelementStart(4, \"div\", 3);\n            i0.ɵɵtemplate(5, RouteOptimizationComponent_div_5_Template, 6, 0, \"div\", 4);\n            i0.ɵɵtemplate(6, RouteOptimizationComponent_div_6_Template, 2, 1, \"div\", 5);\n            i0.ɵɵtemplate(7, RouteOptimizationComponent_ng_container_7_Template, 66, 17, \"ng-container\", 6);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.error);\n          }\n        },\n        dependencies: [i5.NgForOf, i5.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.CheckboxControlValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i6.SidebarComponent, i7.HeaderComponent, i8.StatusBadgeComponent, i9.MapViewComponent, i5.DecimalPipe, i5.DatePipe],\n        styles: [\".optimization-container[_ngcontent-%COMP%]{display:flex;height:100vh;overflow:hidden}.optimization-content[_ngcontent-%COMP%]{flex:1;display:flex;flex-direction:column;overflow:hidden}.optimization-body[_ngcontent-%COMP%]{flex:1;overflow-y:auto;background-color:var(--gray-50)}.metric-mini-card[_ngcontent-%COMP%]{background-color:#fff;border-radius:.5rem;padding:1rem;box-shadow:0 1px 2px #0000000d;border:1px solid var(--gray-200)}.metric-mini-title[_ngcontent-%COMP%]{font-size:.75rem;color:var(--gray-500);margin-bottom:.5rem}.metric-mini-value[_ngcontent-%COMP%]{font-size:1.25rem;font-weight:600;color:var(--gray-800)}\"]\n      });\n    }\n  }\n  return RouteOptimizationComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}