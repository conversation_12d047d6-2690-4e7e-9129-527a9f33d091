import { GeoCoordinates } from './delivery.model';

export interface Driver {
  id: string;
  name: string;
  email: string;
  phone: string;
  vehicleType: string;
  vehicleId?: string;
  rating: number;
  totalDeliveries: number;
  onTimeRate: number;
  status: DriverStatus;
  currentLocation: GeoCoordinates;
  todayDeliveries: number;
  avgDeliveryTime: number;
  lastActive: Date;
  profilePictureUrl?: string;
  hireDate: Date;
  licenseNumber?: string;
  licenseExpiryDate?: Date;
  isAvailableForUrgentDeliveries: boolean;
  preferredZones?: string;
  maxDeliveriesPerDay: number;
}

export enum DriverStatus {
  Available = 0,
  Busy = 1,
  OnBreak = 2,
  Offline = 3
}

export interface DriverPerformance {
  driverId: string;
  driverName: string;
  totalDeliveries: number;
  onTimeDeliveries: number;
  delayedDeliveries: number;
  onTimeRate: number;
  avgDeliveryTime: number;
  avgSpeed: number;
  customerSatisfaction: number;
  fuelEfficiency: number;
  totalDistance: number;
  totalWorkingHours: number;
  deliveriesPerHour: number;
  period: Date;
}
