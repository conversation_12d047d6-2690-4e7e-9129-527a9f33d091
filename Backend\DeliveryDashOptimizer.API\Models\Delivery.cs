using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DeliveryDashOptimizer.API.Models
{
    public class Delivery
    {
        [Key]
        public string Id { get; set; }

        [Required]
        public string OrderId { get; set; }

        [Required]
        public string CustomerId { get; set; }

        [Required]
        public string CustomerName { get; set; }

        [Required]
        public string Address { get; set; }

        [Required]
        public DeliveryStatus Status { get; set; }

        public string? DriverId { get; set; }

        public string? DriverName { get; set; }

        [Required]
        public DateTime EstimatedDeliveryTime { get; set; }

        public DateTime? ActualDeliveryTime { get; set; }

        [Required]
        public DeliveryPriority Priority { get; set; }

        [Required]
        public GeoCoordinates Coordinates { get; set; }

        [Required]
        public DateTime CreatedAt { get; set; }

        public DateTime? PickupTime { get; set; }

        public string? Notes { get; set; }

        public double? Distance { get; set; }

        public int? CustomerRating { get; set; }

        public string? CustomerFeedback { get; set; }

        public WeatherCondition? WeatherCondition { get; set; }

        public TrafficCondition? TrafficCondition { get; set; }
    }

    public enum DeliveryStatus
    {
        Pending,
        InTransit,
        Delivered,
        Delayed,
        Cancelled
    }

    public enum DeliveryPriority
    {
        Low,
        Medium,
        High,
        Urgent
    }

    public enum WeatherCondition
    {
        Clear,
        Cloudy,
        Rainy,
        Snowy,
        Stormy
    }

    public enum TrafficCondition
    {
        Light,
        Moderate,
        Heavy,
        Severe
    }

    public class GeoCoordinates
    {
        [Required]
        public double Latitude { get; set; }

        [Required]
        public double Longitude { get; set; }
    }
}
