{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/common\";\nfunction DriverFormComponent_option_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 39);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r2.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", option_r2.label, \" \");\n  }\n}\nfunction DriverFormComponent_option_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 39);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r3.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", option_r3.label, \" \");\n  }\n}\nexport class DriverFormComponent {\n  constructor(fb) {\n    this.fb = fb;\n    this.driver = null;\n    this.submitted = new EventEmitter();\n    this.cancelled = new EventEmitter();\n    // Vehicle type options\n    this.vehicleTypeOptions = [{\n      value: 'Voiture',\n      label: 'Voiture'\n    }, {\n      value: 'Moto',\n      label: 'Moto'\n    }, {\n      value: 'Vélo',\n      label: 'Vélo'\n    }, {\n      value: 'Camionnette',\n      label: 'Camionnette'\n    }, {\n      value: 'Camion',\n      label: 'Camion'\n    }];\n    // Status options (backend expects integer enum values)\n    this.statusOptions = [{\n      value: 0,\n      label: 'Disponible'\n    }, {\n      value: 1,\n      label: 'Occupé'\n    }, {\n      value: 2,\n      label: 'En pause'\n    }, {\n      value: 3,\n      label: 'Hors ligne'\n    }];\n    this.driverForm = this.fb.group({\n      name: ['', Validators.required],\n      email: ['', [Validators.required, Validators.email]],\n      phone: ['', Validators.required],\n      vehicleType: ['', Validators.required],\n      vehicleId: [''],\n      licenseNumber: [''],\n      licenseExpiryDate: [''],\n      status: [0, Validators.required],\n      isAvailableForUrgentDeliveries: [true],\n      maxDeliveriesPerDay: [20, [Validators.required, Validators.min(1), Validators.max(50)]],\n      preferredZones: [''],\n      latitude: [48.8566, [Validators.required, Validators.min(-90), Validators.max(90)]],\n      longitude: [2.3522, [Validators.required, Validators.min(-180), Validators.max(180)]]\n    });\n  }\n  ngOnInit() {\n    if (this.driver) {\n      this.driverForm.patchValue({\n        name: this.driver.name,\n        email: this.driver.email,\n        phone: this.driver.phone,\n        vehicleType: this.driver.vehicleType,\n        vehicleId: this.driver.vehicleId || '',\n        licenseNumber: this.driver.licenseNumber || '',\n        licenseExpiryDate: this.driver.licenseExpiryDate ? this.formatDateForInput(this.driver.licenseExpiryDate) : '',\n        status: this.driver.status,\n        isAvailableForUrgentDeliveries: this.driver.isAvailableForUrgentDeliveries,\n        maxDeliveriesPerDay: this.driver.maxDeliveriesPerDay,\n        preferredZones: this.driver.preferredZones || '',\n        latitude: this.driver.currentLocation?.latitude || 48.8566,\n        longitude: this.driver.currentLocation?.longitude || 2.3522\n      });\n    }\n  }\n  formatDateForInput(date) {\n    const d = new Date(date);\n    return d.toISOString().slice(0, 10); // Format for date input (YYYY-MM-DD)\n  }\n\n  onSubmit() {\n    if (this.driverForm.valid) {\n      const formValue = this.driverForm.value;\n      // Map form data to CreateDriverRequest format expected by backend\n      const driverData = {\n        name: formValue.name,\n        email: formValue.email,\n        phone: formValue.phone,\n        vehicleType: formValue.vehicleType,\n        vehicleId: formValue.vehicleId || null,\n        status: formValue.status,\n        currentLocation: {\n          latitude: parseFloat(formValue.latitude),\n          longitude: parseFloat(formValue.longitude)\n        },\n        profilePictureUrl: null,\n        licenseNumber: formValue.licenseNumber || null,\n        licenseExpiryDate: formValue.licenseExpiryDate ? formValue.licenseExpiryDate : null,\n        isAvailableForUrgentDeliveries: formValue.isAvailableForUrgentDeliveries,\n        preferredZones: formValue.preferredZones || null,\n        maxDeliveriesPerDay: formValue.maxDeliveriesPerDay\n      };\n      this.submitted.emit(driverData);\n    } else {\n      // Mark all fields as touched to show validation errors\n      this.markFormGroupTouched(this.driverForm);\n    }\n  }\n  markFormGroupTouched(formGroup) {\n    Object.keys(formGroup.controls).forEach(field => {\n      const control = formGroup.get(field);\n      control?.markAsTouched({\n        onlySelf: true\n      });\n    });\n  }\n  onCancel() {\n    this.cancelled.emit();\n  }\n  static {\n    this.ɵfac = function DriverFormComponent_Factory(t) {\n      return new (t || DriverFormComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DriverFormComponent,\n      selectors: [[\"app-driver-form\"]],\n      inputs: {\n        driver: \"driver\"\n      },\n      outputs: {\n        submitted: \"submitted\",\n        cancelled: \"cancelled\"\n      },\n      decls: 83,\n      vars: 19,\n      consts: [[3, \"formGroup\", \"ngSubmit\"], [1, \"row\"], [1, \"col-md-6\"], [1, \"mb-3\"], [\"for\", \"name\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"name\", \"formControlName\", \"name\", 1, \"form-control\"], [1, \"invalid-feedback\"], [\"for\", \"email\", 1, \"form-label\"], [\"type\", \"email\", \"id\", \"email\", \"formControlName\", \"email\", 1, \"form-control\"], [\"for\", \"phone\", 1, \"form-label\"], [\"type\", \"tel\", \"id\", \"phone\", \"formControlName\", \"phone\", 1, \"form-control\"], [\"for\", \"vehicleType\", 1, \"form-label\"], [\"id\", \"vehicleType\", \"formControlName\", \"vehicleType\", 1, \"form-select\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"vehicleId\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"vehicleId\", \"formControlName\", \"vehicleId\", \"placeholder\", \"Ex: AB-123-CD\", 1, \"form-control\"], [\"for\", \"status\", 1, \"form-label\"], [\"id\", \"status\", \"formControlName\", \"status\", 1, \"form-select\"], [\"for\", \"licenseNumber\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"licenseNumber\", \"formControlName\", \"licenseNumber\", \"placeholder\", \"Ex: 123456789\", 1, \"form-control\"], [\"for\", \"licenseExpiryDate\", 1, \"form-label\"], [\"type\", \"date\", \"id\", \"licenseExpiryDate\", \"formControlName\", \"licenseExpiryDate\", 1, \"form-control\"], [\"for\", \"maxDeliveriesPerDay\", 1, \"form-label\"], [\"type\", \"number\", \"id\", \"maxDeliveriesPerDay\", \"formControlName\", \"maxDeliveriesPerDay\", \"min\", \"1\", \"max\", \"50\", 1, \"form-control\"], [1, \"form-label\"], [1, \"col-6\"], [\"type\", \"number\", \"placeholder\", \"Latitude\", \"formControlName\", \"latitude\", \"step\", \"0.000001\", 1, \"form-control\"], [\"type\", \"number\", \"placeholder\", \"Longitude\", \"formControlName\", \"longitude\", \"step\", \"0.000001\", 1, \"form-control\"], [1, \"form-text\", \"text-muted\"], [\"for\", \"preferredZones\", 1, \"form-label\"], [\"id\", \"preferredZones\", \"formControlName\", \"preferredZones\", \"rows\", \"2\", \"placeholder\", \"Ex: Centre-ville, Banlieue nord...\", 1, \"form-control\"], [1, \"form-check\"], [\"type\", \"checkbox\", \"id\", \"isAvailableForUrgentDeliveries\", \"formControlName\", \"isAvailableForUrgentDeliveries\", 1, \"form-check-input\"], [\"for\", \"isAvailableForUrgentDeliveries\", 1, \"form-check-label\"], [1, \"d-flex\", \"gap-2\", \"justify-content-end\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"disabled\"], [1, \"fa-solid\", \"fa-save\", \"me-2\"], [3, \"value\"]],\n      template: function DriverFormComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"form\", 0);\n          i0.ɵɵlistener(\"ngSubmit\", function DriverFormComponent_Template_form_ngSubmit_0_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"label\", 4);\n          i0.ɵɵtext(5, \"Nom complet *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(6, \"input\", 5);\n          i0.ɵɵelementStart(7, \"div\", 6);\n          i0.ɵɵtext(8, \" Le nom est requis \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 3)(10, \"label\", 7);\n          i0.ɵɵtext(11, \"Email *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(12, \"input\", 8);\n          i0.ɵɵelementStart(13, \"div\", 6);\n          i0.ɵɵtext(14, \" Un email valide est requis \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(15, \"div\", 3)(16, \"label\", 9);\n          i0.ɵɵtext(17, \"T\\u00E9l\\u00E9phone *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(18, \"input\", 10);\n          i0.ɵɵelementStart(19, \"div\", 6);\n          i0.ɵɵtext(20, \" Le num\\u00E9ro de t\\u00E9l\\u00E9phone est requis \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(21, \"div\", 3)(22, \"label\", 11);\n          i0.ɵɵtext(23, \"Type de v\\u00E9hicule *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"select\", 12)(25, \"option\", 13);\n          i0.ɵɵtext(26, \"S\\u00E9lectionner un type de v\\u00E9hicule\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(27, DriverFormComponent_option_27_Template, 2, 2, \"option\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"div\", 6);\n          i0.ɵɵtext(29, \" Le type de v\\u00E9hicule est requis \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(30, \"div\", 3)(31, \"label\", 15);\n          i0.ɵɵtext(32, \"Immatriculation du v\\u00E9hicule\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(33, \"input\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"div\", 3)(35, \"label\", 17);\n          i0.ɵɵtext(36, \"Statut *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"select\", 18);\n          i0.ɵɵtemplate(38, DriverFormComponent_option_38_Template, 2, 2, \"option\", 14);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(39, \"div\", 2)(40, \"div\", 3)(41, \"label\", 19);\n          i0.ɵɵtext(42, \"Num\\u00E9ro de permis\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(43, \"input\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"div\", 3)(45, \"label\", 21);\n          i0.ɵɵtext(46, \"Date d'expiration du permis\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(47, \"input\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"div\", 3)(49, \"label\", 23);\n          i0.ɵɵtext(50, \"Livraisons max par jour *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(51, \"input\", 24);\n          i0.ɵɵelementStart(52, \"div\", 6);\n          i0.ɵɵtext(53, \" Nombre entre 1 et 50 requis \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(54, \"div\", 3)(55, \"label\", 25);\n          i0.ɵɵtext(56, \"Position actuelle\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"div\", 1)(58, \"div\", 26);\n          i0.ɵɵelement(59, \"input\", 27);\n          i0.ɵɵelementStart(60, \"div\", 6);\n          i0.ɵɵtext(61, \" Latitude invalide \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(62, \"div\", 26);\n          i0.ɵɵelement(63, \"input\", 28);\n          i0.ɵɵelementStart(64, \"div\", 6);\n          i0.ɵɵtext(65, \" Longitude invalide \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(66, \"small\", 29);\n          i0.ɵɵtext(67, \" Position par d\\u00E9faut: Paris (48.8566, 2.3522) \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(68, \"div\", 3)(69, \"label\", 30);\n          i0.ɵɵtext(70, \"Zones pr\\u00E9f\\u00E9r\\u00E9es\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(71, \"textarea\", 31);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(72, \"div\", 3)(73, \"div\", 32);\n          i0.ɵɵelement(74, \"input\", 33);\n          i0.ɵɵelementStart(75, \"label\", 34);\n          i0.ɵɵtext(76, \" Disponible pour les livraisons urgentes \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(77, \"div\", 35)(78, \"button\", 36);\n          i0.ɵɵlistener(\"click\", function DriverFormComponent_Template_button_click_78_listener() {\n            return ctx.onCancel();\n          });\n          i0.ɵɵtext(79, \"Annuler\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(80, \"button\", 37);\n          i0.ɵɵelement(81, \"i\", 38);\n          i0.ɵɵtext(82);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          let tmp_1_0;\n          let tmp_2_0;\n          let tmp_3_0;\n          let tmp_4_0;\n          let tmp_7_0;\n          let tmp_8_0;\n          let tmp_9_0;\n          i0.ɵɵproperty(\"formGroup\", ctx.driverForm);\n          i0.ɵɵadvance(6);\n          i0.ɵɵclassProp(\"is-invalid\", ((tmp_1_0 = ctx.driverForm.get(\"name\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx.driverForm.get(\"name\")) == null ? null : tmp_1_0.touched));\n          i0.ɵɵadvance(6);\n          i0.ɵɵclassProp(\"is-invalid\", ((tmp_2_0 = ctx.driverForm.get(\"email\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.driverForm.get(\"email\")) == null ? null : tmp_2_0.touched));\n          i0.ɵɵadvance(6);\n          i0.ɵɵclassProp(\"is-invalid\", ((tmp_3_0 = ctx.driverForm.get(\"phone\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx.driverForm.get(\"phone\")) == null ? null : tmp_3_0.touched));\n          i0.ɵɵadvance(6);\n          i0.ɵɵclassProp(\"is-invalid\", ((tmp_4_0 = ctx.driverForm.get(\"vehicleType\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx.driverForm.get(\"vehicleType\")) == null ? null : tmp_4_0.touched));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.vehicleTypeOptions);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngForOf\", ctx.statusOptions);\n          i0.ɵɵadvance(13);\n          i0.ɵɵclassProp(\"is-invalid\", ((tmp_7_0 = ctx.driverForm.get(\"maxDeliveriesPerDay\")) == null ? null : tmp_7_0.invalid) && ((tmp_7_0 = ctx.driverForm.get(\"maxDeliveriesPerDay\")) == null ? null : tmp_7_0.touched));\n          i0.ɵɵadvance(8);\n          i0.ɵɵclassProp(\"is-invalid\", ((tmp_8_0 = ctx.driverForm.get(\"latitude\")) == null ? null : tmp_8_0.invalid) && ((tmp_8_0 = ctx.driverForm.get(\"latitude\")) == null ? null : tmp_8_0.touched));\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"is-invalid\", ((tmp_9_0 = ctx.driverForm.get(\"longitude\")) == null ? null : tmp_9_0.invalid) && ((tmp_9_0 = ctx.driverForm.get(\"longitude\")) == null ? null : tmp_9_0.touched));\n          i0.ɵɵadvance(17);\n          i0.ɵɵproperty(\"disabled\", !ctx.driverForm.valid);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.driver ? \"Mettre \\u00E0 jour\" : \"Cr\\u00E9er le livreur\", \" \");\n        }\n      },\n      dependencies: [i2.NgForOf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.CheckboxControlValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MinValidator, i1.MaxValidator, i1.FormGroupDirective, i1.FormControlName],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "option_r2", "value", "ɵɵadvance", "ɵɵtextInterpolate1", "label", "option_r3", "DriverFormComponent", "constructor", "fb", "driver", "submitted", "cancelled", "vehicleTypeOptions", "statusOptions", "driverForm", "group", "name", "required", "email", "phone", "vehicleType", "vehicleId", "licenseNumber", "licenseExpiryDate", "status", "isAvailableForUrgentDeliveries", "maxDeliveriesPerDay", "min", "max", "preferredZones", "latitude", "longitude", "ngOnInit", "patchValue", "formatDateForInput", "currentLocation", "date", "d", "Date", "toISOString", "slice", "onSubmit", "valid", "formValue", "driverData", "parseFloat", "profilePictureUrl", "emit", "markFormGroupTouched", "formGroup", "Object", "keys", "controls", "for<PERSON>ach", "field", "control", "get", "<PERSON><PERSON><PERSON><PERSON>ched", "onlySelf", "onCancel", "ɵɵdirectiveInject", "i1", "FormBuilder", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "DriverFormComponent_Template", "rf", "ctx", "ɵɵlistener", "DriverFormComponent_Template_form_ngSubmit_0_listener", "ɵɵelement", "ɵɵtemplate", "DriverFormComponent_option_27_Template", "DriverFormComponent_option_38_Template", "DriverFormComponent_Template_button_click_78_listener", "ɵɵclassProp", "tmp_1_0", "invalid", "touched", "tmp_2_0", "tmp_3_0", "tmp_4_0", "tmp_7_0", "tmp_8_0", "tmp_9_0"], "sources": ["C:\\Users\\<USER>\\delivery-dash-optimiser\\frontend\\src\\app\\shared\\components\\driver-form\\driver-form.component.ts", "C:\\Users\\<USER>\\delivery-dash-optimiser\\frontend\\src\\app\\shared\\components\\driver-form\\driver-form.component.html"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Driver, DriverStatus } from '@core/models/driver.model';\n\n@Component({\n  selector: 'app-driver-form',\n  templateUrl: './driver-form.component.html',\n  styleUrls: ['./driver-form.component.scss']\n})\nexport class DriverFormComponent implements OnInit {\n  @Input() driver: Driver | null = null;\n  @Output() submitted = new EventEmitter<any>();\n  @Output() cancelled = new EventEmitter<void>();\n\n  driverForm: FormGroup;\n\n  // Vehicle type options\n  vehicleTypeOptions = [\n    { value: 'Voiture', label: 'Voiture' },\n    { value: 'Moto', label: 'Mo<PERSON>' },\n    { value: 'Vélo', label: 'Vélo' },\n    { value: '<PERSON>ionnette', label: '<PERSON>ionnette' },\n    { value: 'Camion', label: 'Camion' }\n  ];\n\n  // Status options (backend expects integer enum values)\n  statusOptions = [\n    { value: 0, label: 'Disponible' },\n    { value: 1, label: 'Occupé' },\n    { value: 2, label: 'En pause' },\n    { value: 3, label: 'Hors ligne' }\n  ];\n\n  constructor(private fb: FormBuilder) {\n    this.driverForm = this.fb.group({\n      name: ['', Validators.required],\n      email: ['', [Validators.required, Validators.email]],\n      phone: ['', Validators.required],\n      vehicleType: ['', Validators.required],\n      vehicleId: [''],\n      licenseNumber: [''],\n      licenseExpiryDate: [''],\n      status: [0, Validators.required], // 0 = Available\n      isAvailableForUrgentDeliveries: [true],\n      maxDeliveriesPerDay: [20, [Validators.required, Validators.min(1), Validators.max(50)]],\n      preferredZones: [''],\n      latitude: [48.8566, [Validators.required, Validators.min(-90), Validators.max(90)]],\n      longitude: [2.3522, [Validators.required, Validators.min(-180), Validators.max(180)]]\n    });\n  }\n\n  ngOnInit() {\n    if (this.driver) {\n      this.driverForm.patchValue({\n        name: this.driver.name,\n        email: this.driver.email,\n        phone: this.driver.phone,\n        vehicleType: this.driver.vehicleType,\n        vehicleId: this.driver.vehicleId || '',\n        licenseNumber: this.driver.licenseNumber || '',\n        licenseExpiryDate: this.driver.licenseExpiryDate ? this.formatDateForInput(this.driver.licenseExpiryDate) : '',\n        status: this.driver.status,\n        isAvailableForUrgentDeliveries: this.driver.isAvailableForUrgentDeliveries,\n        maxDeliveriesPerDay: this.driver.maxDeliveriesPerDay,\n        preferredZones: this.driver.preferredZones || '',\n        latitude: this.driver.currentLocation?.latitude || 48.8566,\n        longitude: this.driver.currentLocation?.longitude || 2.3522\n      });\n    }\n  }\n\n  private formatDateForInput(date: Date | string): string {\n    const d = new Date(date);\n    return d.toISOString().slice(0, 10); // Format for date input (YYYY-MM-DD)\n  }\n\n  onSubmit() {\n    if (this.driverForm.valid) {\n      const formValue = this.driverForm.value;\n\n      // Map form data to CreateDriverRequest format expected by backend\n      const driverData = {\n        name: formValue.name,\n        email: formValue.email,\n        phone: formValue.phone,\n        vehicleType: formValue.vehicleType,\n        vehicleId: formValue.vehicleId || null,\n        status: formValue.status,\n        currentLocation: {\n          latitude: parseFloat(formValue.latitude),\n          longitude: parseFloat(formValue.longitude)\n        },\n        profilePictureUrl: null,\n        licenseNumber: formValue.licenseNumber || null,\n        licenseExpiryDate: formValue.licenseExpiryDate ? formValue.licenseExpiryDate : null,\n        isAvailableForUrgentDeliveries: formValue.isAvailableForUrgentDeliveries,\n        preferredZones: formValue.preferredZones || null,\n        maxDeliveriesPerDay: formValue.maxDeliveriesPerDay\n      };\n\n      this.submitted.emit(driverData);\n    } else {\n      // Mark all fields as touched to show validation errors\n      this.markFormGroupTouched(this.driverForm);\n    }\n  }\n\n  private markFormGroupTouched(formGroup: any) {\n    Object.keys(formGroup.controls).forEach(field => {\n      const control = formGroup.get(field);\n      control?.markAsTouched({ onlySelf: true });\n    });\n  }\n\n  onCancel() {\n    this.cancelled.emit();\n  }\n}\n", "<form [formGroup]=\"driverForm\" (ngSubmit)=\"onSubmit()\">\n  <div class=\"row\">\n    <!-- Left Column -->\n    <div class=\"col-md-6\">\n      <div class=\"mb-3\">\n        <label for=\"name\" class=\"form-label\">Nom complet *</label>\n        <input type=\"text\" class=\"form-control\" id=\"name\" formControlName=\"name\"\n               [class.is-invalid]=\"driverForm.get('name')?.invalid && driverForm.get('name')?.touched\">\n        <div class=\"invalid-feedback\">\n          Le nom est requis\n        </div>\n      </div>\n\n      <div class=\"mb-3\">\n        <label for=\"email\" class=\"form-label\">Email *</label>\n        <input type=\"email\" class=\"form-control\" id=\"email\" formControlName=\"email\"\n               [class.is-invalid]=\"driverForm.get('email')?.invalid && driverForm.get('email')?.touched\">\n        <div class=\"invalid-feedback\">\n          Un email valide est requis\n        </div>\n      </div>\n\n      <div class=\"mb-3\">\n        <label for=\"phone\" class=\"form-label\">Téléphone *</label>\n        <input type=\"tel\" class=\"form-control\" id=\"phone\" formControlName=\"phone\"\n               [class.is-invalid]=\"driverForm.get('phone')?.invalid && driverForm.get('phone')?.touched\">\n        <div class=\"invalid-feedback\">\n          Le numéro de téléphone est requis\n        </div>\n      </div>\n\n      <div class=\"mb-3\">\n        <label for=\"vehicleType\" class=\"form-label\">Type de véhicule *</label>\n        <select class=\"form-select\" id=\"vehicleType\" formControlName=\"vehicleType\"\n                [class.is-invalid]=\"driverForm.get('vehicleType')?.invalid && driverForm.get('vehicleType')?.touched\">\n          <option value=\"\">Sélectionner un type de véhicule</option>\n          <option *ngFor=\"let option of vehicleTypeOptions\" [value]=\"option.value\">\n            {{ option.label }}\n          </option>\n        </select>\n        <div class=\"invalid-feedback\">\n          Le type de véhicule est requis\n        </div>\n      </div>\n\n      <div class=\"mb-3\">\n        <label for=\"vehicleId\" class=\"form-label\">Immatriculation du véhicule</label>\n        <input type=\"text\" class=\"form-control\" id=\"vehicleId\" formControlName=\"vehicleId\"\n               placeholder=\"Ex: AB-123-CD\">\n      </div>\n\n      <div class=\"mb-3\">\n        <label for=\"status\" class=\"form-label\">Statut *</label>\n        <select class=\"form-select\" id=\"status\" formControlName=\"status\">\n          <option *ngFor=\"let option of statusOptions\" [value]=\"option.value\">\n            {{ option.label }}\n          </option>\n        </select>\n      </div>\n    </div>\n\n    <!-- Right Column -->\n    <div class=\"col-md-6\">\n      <div class=\"mb-3\">\n        <label for=\"licenseNumber\" class=\"form-label\">Numéro de permis</label>\n        <input type=\"text\" class=\"form-control\" id=\"licenseNumber\" formControlName=\"licenseNumber\"\n               placeholder=\"Ex: 123456789\">\n      </div>\n\n      <div class=\"mb-3\">\n        <label for=\"licenseExpiryDate\" class=\"form-label\">Date d'expiration du permis</label>\n        <input type=\"date\" class=\"form-control\" id=\"licenseExpiryDate\" formControlName=\"licenseExpiryDate\">\n      </div>\n\n      <div class=\"mb-3\">\n        <label for=\"maxDeliveriesPerDay\" class=\"form-label\">Livraisons max par jour *</label>\n        <input type=\"number\" class=\"form-control\" id=\"maxDeliveriesPerDay\" formControlName=\"maxDeliveriesPerDay\"\n               min=\"1\" max=\"50\" [class.is-invalid]=\"driverForm.get('maxDeliveriesPerDay')?.invalid && driverForm.get('maxDeliveriesPerDay')?.touched\">\n        <div class=\"invalid-feedback\">\n          Nombre entre 1 et 50 requis\n        </div>\n      </div>\n\n      <div class=\"mb-3\">\n        <label class=\"form-label\">Position actuelle</label>\n        <div class=\"row\">\n          <div class=\"col-6\">\n            <input type=\"number\" class=\"form-control\" placeholder=\"Latitude\" formControlName=\"latitude\"\n                   step=\"0.000001\" [class.is-invalid]=\"driverForm.get('latitude')?.invalid && driverForm.get('latitude')?.touched\">\n            <div class=\"invalid-feedback\">\n              Latitude invalide\n            </div>\n          </div>\n          <div class=\"col-6\">\n            <input type=\"number\" class=\"form-control\" placeholder=\"Longitude\" formControlName=\"longitude\"\n                   step=\"0.000001\" [class.is-invalid]=\"driverForm.get('longitude')?.invalid && driverForm.get('longitude')?.touched\">\n            <div class=\"invalid-feedback\">\n              Longitude invalide\n            </div>\n          </div>\n        </div>\n        <small class=\"form-text text-muted\">\n          Position par défaut: Paris (48.8566, 2.3522)\n        </small>\n      </div>\n\n      <div class=\"mb-3\">\n        <label for=\"preferredZones\" class=\"form-label\">Zones préférées</label>\n        <textarea class=\"form-control\" id=\"preferredZones\" formControlName=\"preferredZones\" rows=\"2\"\n                  placeholder=\"Ex: Centre-ville, Banlieue nord...\"></textarea>\n      </div>\n\n      <div class=\"mb-3\">\n        <div class=\"form-check\">\n          <input class=\"form-check-input\" type=\"checkbox\" id=\"isAvailableForUrgentDeliveries\"\n                 formControlName=\"isAvailableForUrgentDeliveries\">\n          <label class=\"form-check-label\" for=\"isAvailableForUrgentDeliveries\">\n            Disponible pour les livraisons urgentes\n          </label>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <div class=\"d-flex gap-2 justify-content-end\">\n    <button type=\"button\" class=\"btn btn-secondary\" (click)=\"onCancel()\">Annuler</button>\n    <button type=\"submit\" class=\"btn btn-primary\" [disabled]=\"!driverForm.valid\">\n      <i class=\"fa-solid fa-save me-2\"></i>\n      {{ driver ? 'Mettre à jour' : 'Créer le livreur' }}\n    </button>\n  </div>\n</form>\n"], "mappings": "AAAA,SAAmCA,YAAY,QAAgB,eAAe;AAC9E,SAAiCC,UAAU,QAAQ,gBAAgB;;;;;;ICmCzDC,EAAA,CAAAC,cAAA,iBAAyE;IACvED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFyCH,EAAA,CAAAI,UAAA,UAAAC,SAAA,CAAAC,KAAA,CAAsB;IACtEN,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,SAAA,CAAAI,KAAA,MACF;;;;;IAgBAT,EAAA,CAAAC,cAAA,iBAAoE;IAClED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFoCH,EAAA,CAAAI,UAAA,UAAAM,SAAA,CAAAJ,KAAA,CAAsB;IACjEN,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAE,SAAA,CAAAD,KAAA,MACF;;;AD/CV,OAAM,MAAOE,mBAAmB;EAwB9BC,YAAoBC,EAAe;IAAf,KAAAA,EAAE,GAAFA,EAAE;IAvBb,KAAAC,MAAM,GAAkB,IAAI;IAC3B,KAAAC,SAAS,GAAG,IAAIjB,YAAY,EAAO;IACnC,KAAAkB,SAAS,GAAG,IAAIlB,YAAY,EAAQ;IAI9C;IACA,KAAAmB,kBAAkB,GAAG,CACnB;MAAEX,KAAK,EAAE,SAAS;MAAEG,KAAK,EAAE;IAAS,CAAE,EACtC;MAAEH,KAAK,EAAE,MAAM;MAAEG,KAAK,EAAE;IAAM,CAAE,EAChC;MAAEH,KAAK,EAAE,MAAM;MAAEG,KAAK,EAAE;IAAM,CAAE,EAChC;MAAEH,KAAK,EAAE,aAAa;MAAEG,KAAK,EAAE;IAAa,CAAE,EAC9C;MAAEH,KAAK,EAAE,QAAQ;MAAEG,KAAK,EAAE;IAAQ,CAAE,CACrC;IAED;IACA,KAAAS,aAAa,GAAG,CACd;MAAEZ,KAAK,EAAE,CAAC;MAAEG,KAAK,EAAE;IAAY,CAAE,EACjC;MAAEH,KAAK,EAAE,CAAC;MAAEG,KAAK,EAAE;IAAQ,CAAE,EAC7B;MAAEH,KAAK,EAAE,CAAC;MAAEG,KAAK,EAAE;IAAU,CAAE,EAC/B;MAAEH,KAAK,EAAE,CAAC;MAAEG,KAAK,EAAE;IAAY,CAAE,CAClC;IAGC,IAAI,CAACU,UAAU,GAAG,IAAI,CAACN,EAAE,CAACO,KAAK,CAAC;MAC9BC,IAAI,EAAE,CAAC,EAAE,EAAEtB,UAAU,CAACuB,QAAQ,CAAC;MAC/BC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACxB,UAAU,CAACuB,QAAQ,EAAEvB,UAAU,CAACwB,KAAK,CAAC,CAAC;MACpDC,KAAK,EAAE,CAAC,EAAE,EAAEzB,UAAU,CAACuB,QAAQ,CAAC;MAChCG,WAAW,EAAE,CAAC,EAAE,EAAE1B,UAAU,CAACuB,QAAQ,CAAC;MACtCI,SAAS,EAAE,CAAC,EAAE,CAAC;MACfC,aAAa,EAAE,CAAC,EAAE,CAAC;MACnBC,iBAAiB,EAAE,CAAC,EAAE,CAAC;MACvBC,MAAM,EAAE,CAAC,CAAC,EAAE9B,UAAU,CAACuB,QAAQ,CAAC;MAChCQ,8BAA8B,EAAE,CAAC,IAAI,CAAC;MACtCC,mBAAmB,EAAE,CAAC,EAAE,EAAE,CAAChC,UAAU,CAACuB,QAAQ,EAAEvB,UAAU,CAACiC,GAAG,CAAC,CAAC,CAAC,EAAEjC,UAAU,CAACkC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;MACvFC,cAAc,EAAE,CAAC,EAAE,CAAC;MACpBC,QAAQ,EAAE,CAAC,OAAO,EAAE,CAACpC,UAAU,CAACuB,QAAQ,EAAEvB,UAAU,CAACiC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAEjC,UAAU,CAACkC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;MACnFG,SAAS,EAAE,CAAC,MAAM,EAAE,CAACrC,UAAU,CAACuB,QAAQ,EAAEvB,UAAU,CAACiC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAEjC,UAAU,CAACkC,GAAG,CAAC,GAAG,CAAC,CAAC;KACrF,CAAC;EACJ;EAEAI,QAAQA,CAAA;IACN,IAAI,IAAI,CAACvB,MAAM,EAAE;MACf,IAAI,CAACK,UAAU,CAACmB,UAAU,CAAC;QACzBjB,IAAI,EAAE,IAAI,CAACP,MAAM,CAACO,IAAI;QACtBE,KAAK,EAAE,IAAI,CAACT,MAAM,CAACS,KAAK;QACxBC,KAAK,EAAE,IAAI,CAACV,MAAM,CAACU,KAAK;QACxBC,WAAW,EAAE,IAAI,CAACX,MAAM,CAACW,WAAW;QACpCC,SAAS,EAAE,IAAI,CAACZ,MAAM,CAACY,SAAS,IAAI,EAAE;QACtCC,aAAa,EAAE,IAAI,CAACb,MAAM,CAACa,aAAa,IAAI,EAAE;QAC9CC,iBAAiB,EAAE,IAAI,CAACd,MAAM,CAACc,iBAAiB,GAAG,IAAI,CAACW,kBAAkB,CAAC,IAAI,CAACzB,MAAM,CAACc,iBAAiB,CAAC,GAAG,EAAE;QAC9GC,MAAM,EAAE,IAAI,CAACf,MAAM,CAACe,MAAM;QAC1BC,8BAA8B,EAAE,IAAI,CAAChB,MAAM,CAACgB,8BAA8B;QAC1EC,mBAAmB,EAAE,IAAI,CAACjB,MAAM,CAACiB,mBAAmB;QACpDG,cAAc,EAAE,IAAI,CAACpB,MAAM,CAACoB,cAAc,IAAI,EAAE;QAChDC,QAAQ,EAAE,IAAI,CAACrB,MAAM,CAAC0B,eAAe,EAAEL,QAAQ,IAAI,OAAO;QAC1DC,SAAS,EAAE,IAAI,CAACtB,MAAM,CAAC0B,eAAe,EAAEJ,SAAS,IAAI;OACtD,CAAC;;EAEN;EAEQG,kBAAkBA,CAACE,IAAmB;IAC5C,MAAMC,CAAC,GAAG,IAAIC,IAAI,CAACF,IAAI,CAAC;IACxB,OAAOC,CAAC,CAACE,WAAW,EAAE,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;EACvC;;EAEAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAAC3B,UAAU,CAAC4B,KAAK,EAAE;MACzB,MAAMC,SAAS,GAAG,IAAI,CAAC7B,UAAU,CAACb,KAAK;MAEvC;MACA,MAAM2C,UAAU,GAAG;QACjB5B,IAAI,EAAE2B,SAAS,CAAC3B,IAAI;QACpBE,KAAK,EAAEyB,SAAS,CAACzB,KAAK;QACtBC,KAAK,EAAEwB,SAAS,CAACxB,KAAK;QACtBC,WAAW,EAAEuB,SAAS,CAACvB,WAAW;QAClCC,SAAS,EAAEsB,SAAS,CAACtB,SAAS,IAAI,IAAI;QACtCG,MAAM,EAAEmB,SAAS,CAACnB,MAAM;QACxBW,eAAe,EAAE;UACfL,QAAQ,EAAEe,UAAU,CAACF,SAAS,CAACb,QAAQ,CAAC;UACxCC,SAAS,EAAEc,UAAU,CAACF,SAAS,CAACZ,SAAS;SAC1C;QACDe,iBAAiB,EAAE,IAAI;QACvBxB,aAAa,EAAEqB,SAAS,CAACrB,aAAa,IAAI,IAAI;QAC9CC,iBAAiB,EAAEoB,SAAS,CAACpB,iBAAiB,GAAGoB,SAAS,CAACpB,iBAAiB,GAAG,IAAI;QACnFE,8BAA8B,EAAEkB,SAAS,CAAClB,8BAA8B;QACxEI,cAAc,EAAEc,SAAS,CAACd,cAAc,IAAI,IAAI;QAChDH,mBAAmB,EAAEiB,SAAS,CAACjB;OAChC;MAED,IAAI,CAAChB,SAAS,CAACqC,IAAI,CAACH,UAAU,CAAC;KAChC,MAAM;MACL;MACA,IAAI,CAACI,oBAAoB,CAAC,IAAI,CAAClC,UAAU,CAAC;;EAE9C;EAEQkC,oBAAoBA,CAACC,SAAc;IACzCC,MAAM,CAACC,IAAI,CAACF,SAAS,CAACG,QAAQ,CAAC,CAACC,OAAO,CAACC,KAAK,IAAG;MAC9C,MAAMC,OAAO,GAAGN,SAAS,CAACO,GAAG,CAACF,KAAK,CAAC;MACpCC,OAAO,EAAEE,aAAa,CAAC;QAAEC,QAAQ,EAAE;MAAI,CAAE,CAAC;IAC5C,CAAC,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAAChD,SAAS,CAACoC,IAAI,EAAE;EACvB;;;uBA3GWzC,mBAAmB,EAAAX,EAAA,CAAAiE,iBAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAnBxD,mBAAmB;MAAAyD,SAAA;MAAAC,MAAA;QAAAvD,MAAA;MAAA;MAAAwD,OAAA;QAAAvD,SAAA;QAAAC,SAAA;MAAA;MAAAuD,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCThC5E,EAAA,CAAAC,cAAA,cAAuD;UAAxBD,EAAA,CAAA8E,UAAA,sBAAAC,sDAAA;YAAA,OAAYF,GAAA,CAAA/B,QAAA,EAAU;UAAA,EAAC;UACpD9C,EAAA,CAAAC,cAAA,aAAiB;UAI0BD,EAAA,CAAAE,MAAA,oBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC1DH,EAAA,CAAAgF,SAAA,eAC+F;UAC/FhF,EAAA,CAAAC,cAAA,aAA8B;UAC5BD,EAAA,CAAAE,MAAA,0BACF;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAGRH,EAAA,CAAAC,cAAA,aAAkB;UACsBD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACrDH,EAAA,CAAAgF,SAAA,gBACiG;UACjGhF,EAAA,CAAAC,cAAA,cAA8B;UAC5BD,EAAA,CAAAE,MAAA,oCACF;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAGRH,EAAA,CAAAC,cAAA,cAAkB;UACsBD,EAAA,CAAAE,MAAA,6BAAW;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACzDH,EAAA,CAAAgF,SAAA,iBACiG;UACjGhF,EAAA,CAAAC,cAAA,cAA8B;UAC5BD,EAAA,CAAAE,MAAA,0DACF;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAGRH,EAAA,CAAAC,cAAA,cAAkB;UAC4BD,EAAA,CAAAE,MAAA,+BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtEH,EAAA,CAAAC,cAAA,kBAC8G;UAC3FD,EAAA,CAAAE,MAAA,kDAAgC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC1DH,EAAA,CAAAiF,UAAA,KAAAC,sCAAA,qBAES;UACXlF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,cAA8B;UAC5BD,EAAA,CAAAE,MAAA,6CACF;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAGRH,EAAA,CAAAC,cAAA,cAAkB;UAC0BD,EAAA,CAAAE,MAAA,wCAA2B;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC7EH,EAAA,CAAAgF,SAAA,iBACmC;UACrChF,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,cAAkB;UACuBD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvDH,EAAA,CAAAC,cAAA,kBAAiE;UAC/DD,EAAA,CAAAiF,UAAA,KAAAE,sCAAA,qBAES;UACXnF,EAAA,CAAAG,YAAA,EAAS;UAKbH,EAAA,CAAAC,cAAA,cAAsB;UAE4BD,EAAA,CAAAE,MAAA,6BAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtEH,EAAA,CAAAgF,SAAA,iBACmC;UACrChF,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,cAAkB;UACkCD,EAAA,CAAAE,MAAA,mCAA2B;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACrFH,EAAA,CAAAgF,SAAA,iBAAmG;UACrGhF,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,cAAkB;UACoCD,EAAA,CAAAE,MAAA,iCAAyB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACrFH,EAAA,CAAAgF,SAAA,iBAC8I;UAC9IhF,EAAA,CAAAC,cAAA,cAA8B;UAC5BD,EAAA,CAAAE,MAAA,qCACF;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAGRH,EAAA,CAAAC,cAAA,cAAkB;UACUD,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACnDH,EAAA,CAAAC,cAAA,cAAiB;UAEbD,EAAA,CAAAgF,SAAA,iBACuH;UACvHhF,EAAA,CAAAC,cAAA,cAA8B;UAC5BD,EAAA,CAAAE,MAAA,2BACF;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAERH,EAAA,CAAAC,cAAA,eAAmB;UACjBD,EAAA,CAAAgF,SAAA,iBACyH;UACzHhF,EAAA,CAAAC,cAAA,cAA8B;UAC5BD,EAAA,CAAAE,MAAA,4BACF;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAGVH,EAAA,CAAAC,cAAA,iBAAoC;UAClCD,EAAA,CAAAE,MAAA,2DACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAGVH,EAAA,CAAAC,cAAA,cAAkB;UAC+BD,EAAA,CAAAE,MAAA,sCAAe;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtEH,EAAA,CAAAgF,SAAA,oBACsE;UACxEhF,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,cAAkB;UAEdD,EAAA,CAAAgF,SAAA,iBACwD;UACxDhF,EAAA,CAAAC,cAAA,iBAAqE;UACnED,EAAA,CAAAE,MAAA,iDACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAMhBH,EAAA,CAAAC,cAAA,eAA8C;UACID,EAAA,CAAA8E,UAAA,mBAAAM,sDAAA;YAAA,OAASP,GAAA,CAAAb,QAAA,EAAU;UAAA,EAAC;UAAChE,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACrFH,EAAA,CAAAC,cAAA,kBAA6E;UAC3ED,EAAA,CAAAgF,SAAA,aAAqC;UACrChF,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;;;;;UAjIPH,EAAA,CAAAI,UAAA,cAAAyE,GAAA,CAAA1D,UAAA,CAAwB;UAOfnB,EAAA,CAAAO,SAAA,GAAuF;UAAvFP,EAAA,CAAAqF,WAAA,iBAAAC,OAAA,GAAAT,GAAA,CAAA1D,UAAA,CAAA0C,GAAA,2BAAAyB,OAAA,CAAAC,OAAA,OAAAD,OAAA,GAAAT,GAAA,CAAA1D,UAAA,CAAA0C,GAAA,2BAAAyB,OAAA,CAAAE,OAAA,EAAuF;UASvFxF,EAAA,CAAAO,SAAA,GAAyF;UAAzFP,EAAA,CAAAqF,WAAA,iBAAAI,OAAA,GAAAZ,GAAA,CAAA1D,UAAA,CAAA0C,GAAA,4BAAA4B,OAAA,CAAAF,OAAA,OAAAE,OAAA,GAAAZ,GAAA,CAAA1D,UAAA,CAAA0C,GAAA,4BAAA4B,OAAA,CAAAD,OAAA,EAAyF;UASzFxF,EAAA,CAAAO,SAAA,GAAyF;UAAzFP,EAAA,CAAAqF,WAAA,iBAAAK,OAAA,GAAAb,GAAA,CAAA1D,UAAA,CAAA0C,GAAA,4BAAA6B,OAAA,CAAAH,OAAA,OAAAG,OAAA,GAAAb,GAAA,CAAA1D,UAAA,CAAA0C,GAAA,4BAAA6B,OAAA,CAAAF,OAAA,EAAyF;UASxFxF,EAAA,CAAAO,SAAA,GAAqG;UAArGP,EAAA,CAAAqF,WAAA,iBAAAM,OAAA,GAAAd,GAAA,CAAA1D,UAAA,CAAA0C,GAAA,kCAAA8B,OAAA,CAAAJ,OAAA,OAAAI,OAAA,GAAAd,GAAA,CAAA1D,UAAA,CAAA0C,GAAA,kCAAA8B,OAAA,CAAAH,OAAA,EAAqG;UAEhFxF,EAAA,CAAAO,SAAA,GAAqB;UAArBP,EAAA,CAAAI,UAAA,YAAAyE,GAAA,CAAA5D,kBAAA,CAAqB;UAkBrBjB,EAAA,CAAAO,SAAA,IAAgB;UAAhBP,EAAA,CAAAI,UAAA,YAAAyE,GAAA,CAAA3D,aAAA,CAAgB;UAuBrBlB,EAAA,CAAAO,SAAA,IAAqH;UAArHP,EAAA,CAAAqF,WAAA,iBAAAO,OAAA,GAAAf,GAAA,CAAA1D,UAAA,CAAA0C,GAAA,0CAAA+B,OAAA,CAAAL,OAAA,OAAAK,OAAA,GAAAf,GAAA,CAAA1D,UAAA,CAAA0C,GAAA,0CAAA+B,OAAA,CAAAJ,OAAA,EAAqH;UAWlHxF,EAAA,CAAAO,SAAA,GAA+F;UAA/FP,EAAA,CAAAqF,WAAA,iBAAAQ,OAAA,GAAAhB,GAAA,CAAA1D,UAAA,CAAA0C,GAAA,+BAAAgC,OAAA,CAAAN,OAAA,OAAAM,OAAA,GAAAhB,GAAA,CAAA1D,UAAA,CAAA0C,GAAA,+BAAAgC,OAAA,CAAAL,OAAA,EAA+F;UAO/FxF,EAAA,CAAAO,SAAA,GAAiG;UAAjGP,EAAA,CAAAqF,WAAA,iBAAAS,OAAA,GAAAjB,GAAA,CAAA1D,UAAA,CAAA0C,GAAA,gCAAAiC,OAAA,CAAAP,OAAA,OAAAO,OAAA,GAAAjB,GAAA,CAAA1D,UAAA,CAAA0C,GAAA,gCAAAiC,OAAA,CAAAN,OAAA,EAAiG;UA+BlFxF,EAAA,CAAAO,SAAA,IAA8B;UAA9BP,EAAA,CAAAI,UAAA,cAAAyE,GAAA,CAAA1D,UAAA,CAAA4B,KAAA,CAA8B;UAE1E/C,EAAA,CAAAO,SAAA,GACF;UADEP,EAAA,CAAAQ,kBAAA,MAAAqE,GAAA,CAAA/D,MAAA,uDACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}