{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/common\";\nfunction DriverFormComponent_div_1_li_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1, \" Le nom est requis \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DriverFormComponent_div_1_li_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1, \" Un email valide est requis \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DriverFormComponent_div_1_li_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1, \" Le num\\u00E9ro de t\\u00E9l\\u00E9phone est requis \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DriverFormComponent_div_1_li_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1, \" Le type de v\\u00E9hicule est requis \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DriverFormComponent_div_1_li_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1, \" Le nombre de livraisons par jour doit \\u00EAtre entre 1 et 50 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DriverFormComponent_div_1_li_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1, \" La latitude doit \\u00EAtre entre -90 et 90 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DriverFormComponent_div_1_li_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1, \" La longitude doit \\u00EAtre entre -180 et 180 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DriverFormComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"h6\", 41);\n    i0.ɵɵelement(2, \"i\", 42);\n    i0.ɵɵtext(3, \" Veuillez corriger les erreurs suivantes: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"ul\", 43);\n    i0.ɵɵtemplate(5, DriverFormComponent_div_1_li_5_Template, 2, 0, \"li\", 44);\n    i0.ɵɵtemplate(6, DriverFormComponent_div_1_li_6_Template, 2, 0, \"li\", 44);\n    i0.ɵɵtemplate(7, DriverFormComponent_div_1_li_7_Template, 2, 0, \"li\", 44);\n    i0.ɵɵtemplate(8, DriverFormComponent_div_1_li_8_Template, 2, 0, \"li\", 44);\n    i0.ɵɵtemplate(9, DriverFormComponent_div_1_li_9_Template, 2, 0, \"li\", 44);\n    i0.ɵɵtemplate(10, DriverFormComponent_div_1_li_10_Template, 2, 0, \"li\", 44);\n    i0.ɵɵtemplate(11, DriverFormComponent_div_1_li_11_Template, 2, 0, \"li\", 44);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    let tmp_2_0;\n    let tmp_3_0;\n    let tmp_4_0;\n    let tmp_5_0;\n    let tmp_6_0;\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_0_0 = ctx_r0.driverForm.get(\"name\")) == null ? null : tmp_0_0.invalid) && ((tmp_0_0 = ctx_r0.driverForm.get(\"name\")) == null ? null : tmp_0_0.touched));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx_r0.driverForm.get(\"email\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx_r0.driverForm.get(\"email\")) == null ? null : tmp_1_0.touched));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx_r0.driverForm.get(\"phone\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx_r0.driverForm.get(\"phone\")) == null ? null : tmp_2_0.touched));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx_r0.driverForm.get(\"vehicleType\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx_r0.driverForm.get(\"vehicleType\")) == null ? null : tmp_3_0.touched));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx_r0.driverForm.get(\"maxDeliveriesPerDay\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx_r0.driverForm.get(\"maxDeliveriesPerDay\")) == null ? null : tmp_4_0.touched));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx_r0.driverForm.get(\"latitude\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx_r0.driverForm.get(\"latitude\")) == null ? null : tmp_5_0.touched));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = ctx_r0.driverForm.get(\"longitude\")) == null ? null : tmp_6_0.invalid) && ((tmp_6_0 = ctx_r0.driverForm.get(\"longitude\")) == null ? null : tmp_6_0.touched));\n  }\n}\nfunction DriverFormComponent_option_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r10 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r10.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", option_r10.label, \" \");\n  }\n}\nfunction DriverFormComponent_option_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 45);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r11 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r11.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", option_r11.label, \" \");\n  }\n}\nexport let DriverFormComponent = /*#__PURE__*/(() => {\n  class DriverFormComponent {\n    constructor(fb) {\n      this.fb = fb;\n      this.driver = null;\n      this.submitted = new EventEmitter();\n      this.cancelled = new EventEmitter();\n      // Vehicle type options\n      this.vehicleTypeOptions = [{\n        value: 'Voiture',\n        label: 'Voiture'\n      }, {\n        value: 'Moto',\n        label: 'Moto'\n      }, {\n        value: 'Vélo',\n        label: 'Vélo'\n      }, {\n        value: 'Camionnette',\n        label: 'Camionnette'\n      }, {\n        value: 'Camion',\n        label: 'Camion'\n      }];\n      // Status options (backend expects integer enum values)\n      this.statusOptions = [{\n        value: 0,\n        label: 'Disponible'\n      }, {\n        value: 1,\n        label: 'Occupé'\n      }, {\n        value: 2,\n        label: 'En pause'\n      }, {\n        value: 3,\n        label: 'Hors ligne'\n      }];\n      this.driverForm = this.fb.group({\n        name: ['', Validators.required],\n        email: ['', [Validators.required, Validators.email]],\n        phone: ['', Validators.required],\n        vehicleType: ['', Validators.required],\n        vehicleId: [''],\n        licenseNumber: [''],\n        licenseExpiryDate: [''],\n        status: [0, Validators.required],\n        isAvailableForUrgentDeliveries: [true],\n        maxDeliveriesPerDay: [20, [Validators.required, Validators.min(1), Validators.max(50)]],\n        preferredZones: [''],\n        latitude: [48.8566, [Validators.required, Validators.min(-90), Validators.max(90)]],\n        longitude: [2.3522, [Validators.required, Validators.min(-180), Validators.max(180)]]\n      });\n    }\n    ngOnInit() {\n      if (this.driver) {\n        this.driverForm.patchValue({\n          name: this.driver.name,\n          email: this.driver.email,\n          phone: this.driver.phone,\n          vehicleType: this.driver.vehicleType,\n          vehicleId: this.driver.vehicleId || '',\n          licenseNumber: this.driver.licenseNumber || '',\n          licenseExpiryDate: this.driver.licenseExpiryDate ? this.formatDateForInput(this.driver.licenseExpiryDate) : '',\n          status: this.driver.status,\n          isAvailableForUrgentDeliveries: this.driver.isAvailableForUrgentDeliveries,\n          maxDeliveriesPerDay: this.driver.maxDeliveriesPerDay,\n          preferredZones: this.driver.preferredZones || '',\n          latitude: this.driver.currentLocation?.latitude || 48.8566,\n          longitude: this.driver.currentLocation?.longitude || 2.3522\n        });\n      }\n    }\n    formatDateForInput(date) {\n      const d = new Date(date);\n      return d.toISOString().slice(0, 10); // Format for date input (YYYY-MM-DD)\n    }\n\n    onSubmit() {\n      if (this.driverForm.valid) {\n        const formValue = this.driverForm.value;\n        // Map form data to CreateDriverRequest format expected by backend\n        const driverData = {\n          name: formValue.name,\n          email: formValue.email,\n          phone: formValue.phone,\n          vehicleType: formValue.vehicleType,\n          vehicleId: formValue.vehicleId || null,\n          status: formValue.status,\n          currentLocation: {\n            latitude: parseFloat(formValue.latitude),\n            longitude: parseFloat(formValue.longitude)\n          },\n          profilePictureUrl: null,\n          licenseNumber: formValue.licenseNumber || null,\n          licenseExpiryDate: formValue.licenseExpiryDate ? formValue.licenseExpiryDate : null,\n          isAvailableForUrgentDeliveries: formValue.isAvailableForUrgentDeliveries,\n          preferredZones: formValue.preferredZones || null,\n          maxDeliveriesPerDay: formValue.maxDeliveriesPerDay\n        };\n        this.submitted.emit(driverData);\n      } else {\n        // Mark all fields as touched to show validation errors\n        this.markFormGroupTouched(this.driverForm);\n      }\n    }\n    markFormGroupTouched(formGroup) {\n      Object.keys(formGroup.controls).forEach(field => {\n        const control = formGroup.get(field);\n        control?.markAsTouched({\n          onlySelf: true\n        });\n      });\n    }\n    onCancel() {\n      this.cancelled.emit();\n    }\n    static {\n      this.ɵfac = function DriverFormComponent_Factory(t) {\n        return new (t || DriverFormComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: DriverFormComponent,\n        selectors: [[\"app-driver-form\"]],\n        inputs: {\n          driver: \"driver\"\n        },\n        outputs: {\n          submitted: \"submitted\",\n          cancelled: \"cancelled\"\n        },\n        decls: 84,\n        vars: 20,\n        consts: [[3, \"formGroup\", \"ngSubmit\"], [\"class\", \"alert alert-danger mb-3\", 4, \"ngIf\"], [1, \"row\"], [1, \"col-md-6\"], [1, \"mb-3\"], [\"for\", \"name\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"name\", \"formControlName\", \"name\", 1, \"form-control\"], [1, \"invalid-feedback\"], [\"for\", \"email\", 1, \"form-label\"], [\"type\", \"email\", \"id\", \"email\", \"formControlName\", \"email\", 1, \"form-control\"], [\"for\", \"phone\", 1, \"form-label\"], [\"type\", \"tel\", \"id\", \"phone\", \"formControlName\", \"phone\", 1, \"form-control\"], [\"for\", \"vehicleType\", 1, \"form-label\"], [\"id\", \"vehicleType\", \"formControlName\", \"vehicleType\", 1, \"form-select\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"vehicleId\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"vehicleId\", \"formControlName\", \"vehicleId\", \"placeholder\", \"Ex: AB-123-CD\", 1, \"form-control\"], [\"for\", \"status\", 1, \"form-label\"], [\"id\", \"status\", \"formControlName\", \"status\", 1, \"form-select\"], [\"for\", \"licenseNumber\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"licenseNumber\", \"formControlName\", \"licenseNumber\", \"placeholder\", \"Ex: 123456789\", 1, \"form-control\"], [\"for\", \"licenseExpiryDate\", 1, \"form-label\"], [\"type\", \"date\", \"id\", \"licenseExpiryDate\", \"formControlName\", \"licenseExpiryDate\", 1, \"form-control\"], [\"for\", \"maxDeliveriesPerDay\", 1, \"form-label\"], [\"type\", \"number\", \"id\", \"maxDeliveriesPerDay\", \"formControlName\", \"maxDeliveriesPerDay\", \"min\", \"1\", \"max\", \"50\", 1, \"form-control\"], [1, \"form-label\"], [1, \"col-6\"], [\"type\", \"number\", \"placeholder\", \"Latitude\", \"formControlName\", \"latitude\", \"step\", \"0.000001\", 1, \"form-control\"], [\"type\", \"number\", \"placeholder\", \"Longitude\", \"formControlName\", \"longitude\", \"step\", \"0.000001\", 1, \"form-control\"], [1, \"form-text\", \"text-muted\"], [\"for\", \"preferredZones\", 1, \"form-label\"], [\"id\", \"preferredZones\", \"formControlName\", \"preferredZones\", \"rows\", \"2\", \"placeholder\", \"Ex: Centre-ville, Banlieue nord...\", 1, \"form-control\"], [1, \"form-check\"], [\"type\", \"checkbox\", \"id\", \"isAvailableForUrgentDeliveries\", \"formControlName\", \"isAvailableForUrgentDeliveries\", 1, \"form-check-input\"], [\"for\", \"isAvailableForUrgentDeliveries\", 1, \"form-check-label\"], [1, \"d-flex\", \"gap-2\", \"justify-content-end\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"disabled\"], [1, \"fa-solid\", \"fa-save\", \"me-2\"], [1, \"alert\", \"alert-danger\", \"mb-3\"], [1, \"alert-heading\"], [1, \"fa-solid\", \"fa-exclamation-triangle\", \"me-2\"], [1, \"mb-0\"], [4, \"ngIf\"], [3, \"value\"]],\n        template: function DriverFormComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"form\", 0);\n            i0.ɵɵlistener(\"ngSubmit\", function DriverFormComponent_Template_form_ngSubmit_0_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵtemplate(1, DriverFormComponent_div_1_Template, 12, 7, \"div\", 1);\n            i0.ɵɵelementStart(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"label\", 5);\n            i0.ɵɵtext(6, \"Nom complet *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(7, \"input\", 6);\n            i0.ɵɵelementStart(8, \"div\", 7);\n            i0.ɵɵtext(9, \" Le nom est requis \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(10, \"div\", 4)(11, \"label\", 8);\n            i0.ɵɵtext(12, \"Email *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(13, \"input\", 9);\n            i0.ɵɵelementStart(14, \"div\", 7);\n            i0.ɵɵtext(15, \" Un email valide est requis \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(16, \"div\", 4)(17, \"label\", 10);\n            i0.ɵɵtext(18, \"T\\u00E9l\\u00E9phone *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(19, \"input\", 11);\n            i0.ɵɵelementStart(20, \"div\", 7);\n            i0.ɵɵtext(21, \" Le num\\u00E9ro de t\\u00E9l\\u00E9phone est requis \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(22, \"div\", 4)(23, \"label\", 12);\n            i0.ɵɵtext(24, \"Type de v\\u00E9hicule *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(25, \"select\", 13)(26, \"option\", 14);\n            i0.ɵɵtext(27, \"S\\u00E9lectionner un type de v\\u00E9hicule\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(28, DriverFormComponent_option_28_Template, 2, 2, \"option\", 15);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(29, \"div\", 7);\n            i0.ɵɵtext(30, \" Le type de v\\u00E9hicule est requis \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(31, \"div\", 4)(32, \"label\", 16);\n            i0.ɵɵtext(33, \"Immatriculation du v\\u00E9hicule\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(34, \"input\", 17);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(35, \"div\", 4)(36, \"label\", 18);\n            i0.ɵɵtext(37, \"Statut *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(38, \"select\", 19);\n            i0.ɵɵtemplate(39, DriverFormComponent_option_39_Template, 2, 2, \"option\", 15);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(40, \"div\", 3)(41, \"div\", 4)(42, \"label\", 20);\n            i0.ɵɵtext(43, \"Num\\u00E9ro de permis\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(44, \"input\", 21);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(45, \"div\", 4)(46, \"label\", 22);\n            i0.ɵɵtext(47, \"Date d'expiration du permis\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(48, \"input\", 23);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(49, \"div\", 4)(50, \"label\", 24);\n            i0.ɵɵtext(51, \"Livraisons max par jour *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(52, \"input\", 25);\n            i0.ɵɵelementStart(53, \"div\", 7);\n            i0.ɵɵtext(54, \" Nombre entre 1 et 50 requis \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(55, \"div\", 4)(56, \"label\", 26);\n            i0.ɵɵtext(57, \"Position actuelle\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(58, \"div\", 2)(59, \"div\", 27);\n            i0.ɵɵelement(60, \"input\", 28);\n            i0.ɵɵelementStart(61, \"div\", 7);\n            i0.ɵɵtext(62, \" Latitude invalide \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(63, \"div\", 27);\n            i0.ɵɵelement(64, \"input\", 29);\n            i0.ɵɵelementStart(65, \"div\", 7);\n            i0.ɵɵtext(66, \" Longitude invalide \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(67, \"small\", 30);\n            i0.ɵɵtext(68, \" Position par d\\u00E9faut: Paris (48.8566, 2.3522) \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(69, \"div\", 4)(70, \"label\", 31);\n            i0.ɵɵtext(71, \"Zones pr\\u00E9f\\u00E9r\\u00E9es\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(72, \"textarea\", 32);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(73, \"div\", 4)(74, \"div\", 33);\n            i0.ɵɵelement(75, \"input\", 34);\n            i0.ɵɵelementStart(76, \"label\", 35);\n            i0.ɵɵtext(77, \" Disponible pour les livraisons urgentes \");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(78, \"div\", 36)(79, \"button\", 37);\n            i0.ɵɵlistener(\"click\", function DriverFormComponent_Template_button_click_79_listener() {\n              return ctx.onCancel();\n            });\n            i0.ɵɵtext(80, \"Annuler\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(81, \"button\", 38);\n            i0.ɵɵelement(82, \"i\", 39);\n            i0.ɵɵtext(83);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            let tmp_2_0;\n            let tmp_3_0;\n            let tmp_4_0;\n            let tmp_5_0;\n            let tmp_8_0;\n            let tmp_9_0;\n            let tmp_10_0;\n            i0.ɵɵproperty(\"formGroup\", ctx.driverForm);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.driverForm.invalid && ctx.driverForm.touched);\n            i0.ɵɵadvance(6);\n            i0.ɵɵclassProp(\"is-invalid\", ((tmp_2_0 = ctx.driverForm.get(\"name\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.driverForm.get(\"name\")) == null ? null : tmp_2_0.touched));\n            i0.ɵɵadvance(6);\n            i0.ɵɵclassProp(\"is-invalid\", ((tmp_3_0 = ctx.driverForm.get(\"email\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx.driverForm.get(\"email\")) == null ? null : tmp_3_0.touched));\n            i0.ɵɵadvance(6);\n            i0.ɵɵclassProp(\"is-invalid\", ((tmp_4_0 = ctx.driverForm.get(\"phone\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx.driverForm.get(\"phone\")) == null ? null : tmp_4_0.touched));\n            i0.ɵɵadvance(6);\n            i0.ɵɵclassProp(\"is-invalid\", ((tmp_5_0 = ctx.driverForm.get(\"vehicleType\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx.driverForm.get(\"vehicleType\")) == null ? null : tmp_5_0.touched));\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngForOf\", ctx.vehicleTypeOptions);\n            i0.ɵɵadvance(11);\n            i0.ɵɵproperty(\"ngForOf\", ctx.statusOptions);\n            i0.ɵɵadvance(13);\n            i0.ɵɵclassProp(\"is-invalid\", ((tmp_8_0 = ctx.driverForm.get(\"maxDeliveriesPerDay\")) == null ? null : tmp_8_0.invalid) && ((tmp_8_0 = ctx.driverForm.get(\"maxDeliveriesPerDay\")) == null ? null : tmp_8_0.touched));\n            i0.ɵɵadvance(8);\n            i0.ɵɵclassProp(\"is-invalid\", ((tmp_9_0 = ctx.driverForm.get(\"latitude\")) == null ? null : tmp_9_0.invalid) && ((tmp_9_0 = ctx.driverForm.get(\"latitude\")) == null ? null : tmp_9_0.touched));\n            i0.ɵɵadvance(4);\n            i0.ɵɵclassProp(\"is-invalid\", ((tmp_10_0 = ctx.driverForm.get(\"longitude\")) == null ? null : tmp_10_0.invalid) && ((tmp_10_0 = ctx.driverForm.get(\"longitude\")) == null ? null : tmp_10_0.touched));\n            i0.ɵɵadvance(17);\n            i0.ɵɵproperty(\"disabled\", !ctx.driverForm.valid);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate1(\" \", ctx.driver ? \"Mettre \\u00E0 jour\" : \"Cr\\u00E9er le livreur\", \" \");\n          }\n        },\n        dependencies: [i2.NgForOf, i2.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.CheckboxControlValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MinValidator, i1.MaxValidator, i1.FormGroupDirective, i1.FormControlName]\n      });\n    }\n  }\n  return DriverFormComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}