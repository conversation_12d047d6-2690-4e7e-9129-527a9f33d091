{"ast": null, "code": "import { DeliveryStatus } from '../../core/models/delivery.model';\nimport { UserRole } from '../../core/models/user.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../core/services/delivery.service\";\nimport * as i2 from \"../../core/services/driver.service\";\nimport * as i3 from \"../../core/services/real-time.service\";\nimport * as i4 from \"../../core/services/auth.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"../../shared/components/sidebar/sidebar.component\";\nimport * as i8 from \"../../shared/components/header/header.component\";\nimport * as i9 from \"../../shared/components/status-badge/status-badge.component\";\nimport * as i10 from \"../../shared/components/map-view/map-view.component\";\nimport * as i11 from \"../../shared/components/delivery-form/delivery-form.component\";\nfunction DeliveryTrackingComponent_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function DeliveryTrackingComponent_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onNewDelivery());\n    });\n    i0.ɵɵelement(1, \"i\", 33);\n    i0.ɵɵtext(2, \" Nouvelle livraison \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DeliveryTrackingComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35)(2, \"span\", 36);\n    i0.ɵɵtext(3, \"Chargement...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 37);\n    i0.ɵɵtext(5, \"Chargement des donn\\u00E9es...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DeliveryTrackingComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.error, \" \");\n  }\n}\nfunction DeliveryTrackingComponent_ng_container_12_button_37_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function DeliveryTrackingComponent_ng_container_12_button_37_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r12);\n      const delivery_r10 = restoredCtx.$implicit;\n      const ctx_r11 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r11.selectDelivery(delivery_r10));\n    });\n    i0.ɵɵelementStart(1, \"div\", 70)(2, \"div\")(3, \"h6\", 71);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 72);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 73);\n    i0.ɵɵelement(8, \"app-status-badge\", 74);\n    i0.ɵɵelementStart(9, \"p\", 75);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"date\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const delivery_r10 = ctx.$implicit;\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", (ctx_r6.selectedDelivery == null ? null : ctx_r6.selectedDelivery.id) === delivery_r10.id);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(delivery_r10.customerName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(delivery_r10.address);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"status\", ctx_r6.getStatusText(delivery_r10.status))(\"variant\", ctx_r6.getStatusClass(delivery_r10.status));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(11, 7, delivery_r10.estimatedDeliveryTime, \"HH:mm\"), \" \");\n  }\n}\nfunction DeliveryTrackingComponent_ng_container_12_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76);\n    i0.ɵɵelement(1, \"i\", 77);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Aucune livraison trouv\\u00E9e\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DeliveryTrackingComponent_ng_container_12_div_43_div_71_i_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 93);\n  }\n  if (rf & 2) {\n    const star_r17 = ctx.$implicit;\n    const ctx_r16 = i0.ɵɵnextContext(4);\n    i0.ɵɵclassProp(\"text-warning\", star_r17 <= ctx_r16.selectedDelivery.customerRating)(\"text-muted\", star_r17 > ctx_r16.selectedDelivery.customerRating);\n  }\n}\nconst _c0 = function () {\n  return [1, 2, 3, 4, 5];\n};\nfunction DeliveryTrackingComponent_ng_container_12_div_43_div_71_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88)(1, \"div\", 89)(2, \"span\", 90);\n    i0.ɵɵtext(3, \"Note:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 91);\n    i0.ɵɵtemplate(5, DeliveryTrackingComponent_ng_container_12_div_43_div_71_i_5_Template, 1, 4, \"i\", 92);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction DeliveryTrackingComponent_ng_container_12_div_43_div_74_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 94)(1, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function DeliveryTrackingComponent_ng_container_12_div_43_div_74_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r18 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r18.onEditDelivery(ctx_r18.selectedDelivery));\n    });\n    i0.ɵɵelement(2, \"i\", 95);\n    i0.ɵɵtext(3, \" Modifier \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 96);\n    i0.ɵɵlistener(\"click\", function DeliveryTrackingComponent_ng_container_12_div_43_div_74_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r20 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r20.onDeleteDelivery(ctx_r20.selectedDelivery));\n    });\n    i0.ɵɵelement(5, \"i\", 97);\n    i0.ɵɵtext(6, \" Supprimer \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DeliveryTrackingComponent_ng_container_12_div_43_div_75_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 94)(1, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function DeliveryTrackingComponent_ng_container_12_div_43_div_75_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r21 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r21.onUpdateDeliveryStatus(ctx_r21.selectedDelivery));\n    });\n    i0.ɵɵelement(2, \"i\", 98);\n    i0.ɵɵtext(3, \" Mettre \\u00E0 jour le statut \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DeliveryTrackingComponent_ng_container_12_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"div\", 78)(2, \"h5\", 60);\n    i0.ɵɵtext(3, \"D\\u00E9tails de la livraison\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 42)(5, \"div\", 79)(6, \"div\", 44)(7, \"h6\", 80);\n    i0.ɵɵtext(8, \"Informations g\\u00E9n\\u00E9rales\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"dl\", 81)(10, \"dt\", 82);\n    i0.ɵɵtext(11, \"ID Commande\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"dd\", 83);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"dt\", 82);\n    i0.ɵɵtext(15, \"Client\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"dd\", 83);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"dt\", 82);\n    i0.ɵɵtext(19, \"Adresse\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"dd\", 83);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"dt\", 82);\n    i0.ɵɵtext(23, \"Statut\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"dd\", 83);\n    i0.ɵɵelement(25, \"app-status-badge\", 74);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"dt\", 82);\n    i0.ɵɵtext(27, \"Priorit\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"dd\", 83);\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"dt\", 82);\n    i0.ɵɵtext(31, \"Distance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"dd\", 83);\n    i0.ɵɵtext(33);\n    i0.ɵɵpipe(34, \"number\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(35, \"div\", 44)(36, \"h6\", 80);\n    i0.ɵɵtext(37, \"Informations de livraison\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"dl\", 81)(39, \"dt\", 82);\n    i0.ɵɵtext(40, \"Livreur\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"dd\", 83);\n    i0.ɵɵtext(42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"dt\", 82);\n    i0.ɵɵtext(44, \"Heure estim\\u00E9e\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"dd\", 83);\n    i0.ɵɵtext(46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"dt\", 82);\n    i0.ɵɵtext(48, \"Heure r\\u00E9elle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"dd\", 83);\n    i0.ɵɵtext(50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"dt\", 82);\n    i0.ɵɵtext(52, \"Heure de prise\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(53, \"dd\", 83);\n    i0.ɵɵtext(54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(55, \"dt\", 82);\n    i0.ɵɵtext(56, \"Trafic\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(57, \"dd\", 83);\n    i0.ɵɵtext(58);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(59, \"dt\", 82);\n    i0.ɵɵtext(60, \"M\\u00E9t\\u00E9o\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(61, \"dd\", 83);\n    i0.ɵɵtext(62);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(63, \"div\", 84)(64, \"h6\", 80);\n    i0.ɵɵtext(65, \"Notes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(66, \"p\", 85);\n    i0.ɵɵtext(67);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(68, \"div\", 84)(69, \"h6\", 80);\n    i0.ɵɵtext(70, \"Feedback client\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(71, DeliveryTrackingComponent_ng_container_12_div_43_div_71_Template, 6, 2, \"div\", 86);\n    i0.ɵɵelementStart(72, \"p\", 85);\n    i0.ɵɵtext(73);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(74, DeliveryTrackingComponent_ng_container_12_div_43_div_74_Template, 7, 0, \"div\", 87);\n    i0.ɵɵtemplate(75, DeliveryTrackingComponent_ng_container_12_div_43_div_75_Template, 4, 0, \"div\", 87);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(13);\n    i0.ɵɵtextInterpolate(ctx_r8.selectedDelivery.orderId);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r8.selectedDelivery.customerName);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r8.selectedDelivery.address);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"status\", ctx_r8.getStatusText(ctx_r8.selectedDelivery.status))(\"variant\", ctx_r8.getStatusClass(ctx_r8.selectedDelivery.status));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r8.getPriorityText(ctx_r8.selectedDelivery.priority));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r8.selectedDelivery.distance ? i0.ɵɵpipeBind2(34, 18, ctx_r8.selectedDelivery.distance, \"1.1-1\") + \" km\" : \"N/A\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r8.selectedDelivery.driverName || \"Non assign\\u00E9\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r8.formatDate(ctx_r8.selectedDelivery.estimatedDeliveryTime));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r8.selectedDelivery.actualDeliveryTime ? ctx_r8.formatDate(ctx_r8.selectedDelivery.actualDeliveryTime) : \"N/A\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r8.selectedDelivery.pickupTime ? ctx_r8.formatDate(ctx_r8.selectedDelivery.pickupTime) : \"N/A\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r8.selectedDelivery.trafficCondition || \"N/A\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r8.selectedDelivery.weatherCondition || \"N/A\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r8.selectedDelivery.notes || \"Aucune note\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.selectedDelivery.customerRating);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r8.selectedDelivery.customerFeedback || \"Aucun feedback\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r8.isDriverView);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.isDriverView);\n  }\n}\nfunction DeliveryTrackingComponent_ng_container_12_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"div\", 99);\n    i0.ɵɵelement(2, \"i\", 100);\n    i0.ɵɵelementStart(3, \"h5\");\n    i0.ɵɵtext(4, \"Aucune livraison s\\u00E9lectionn\\u00E9e\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 101);\n    i0.ɵɵtext(6, \"S\\u00E9lectionnez une livraison dans la liste pour voir les d\\u00E9tails\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nconst _c1 = function (a0) {\n  return [a0];\n};\nconst _c2 = function () {\n  return [];\n};\nfunction DeliveryTrackingComponent_ng_container_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 39)(2, \"div\", 40)(3, \"div\", 41)(4, \"div\", 42)(5, \"div\", 43)(6, \"div\", 44)(7, \"label\", 45);\n    i0.ɵɵtext(8, \"Statut\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"select\", 46);\n    i0.ɵɵlistener(\"ngModelChange\", function DeliveryTrackingComponent_ng_container_12_Template_select_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.statusFilter = $event);\n    })(\"change\", function DeliveryTrackingComponent_ng_container_12_Template_select_change_9_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r25 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r25.applyFilters());\n    });\n    i0.ɵɵelementStart(10, \"option\", 47);\n    i0.ɵɵtext(11, \"Tous les statuts\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"option\", 48);\n    i0.ɵɵtext(13, \"En attente\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"option\", 49);\n    i0.ɵɵtext(15, \"En cours\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"option\", 50);\n    i0.ɵɵtext(17, \"Livr\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"option\", 51);\n    i0.ɵɵtext(19, \"Retard\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"option\", 52);\n    i0.ɵɵtext(21, \"Annul\\u00E9\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"div\", 44)(23, \"label\", 53);\n    i0.ɵɵtext(24, \"Recherche\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 54)(26, \"input\", 55);\n    i0.ɵɵlistener(\"ngModelChange\", function DeliveryTrackingComponent_ng_container_12_Template_input_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.searchTerm = $event);\n    })(\"input\", function DeliveryTrackingComponent_ng_container_12_Template_input_input_26_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r27 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r27.applyFilters());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"span\", 56);\n    i0.ɵɵelement(28, \"i\", 57);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(29, \"div\", 58)(30, \"div\", 59)(31, \"h5\", 60);\n    i0.ɵɵtext(32, \"Livraisons\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"span\", 61);\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"div\", 62)(36, \"div\", 63);\n    i0.ɵɵtemplate(37, DeliveryTrackingComponent_ng_container_12_button_37_Template, 12, 10, \"button\", 64);\n    i0.ɵɵtemplate(38, DeliveryTrackingComponent_ng_container_12_div_38_Template, 4, 0, \"div\", 65);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(39, \"div\", 66)(40, \"div\", 41)(41, \"div\", 62);\n    i0.ɵɵelement(42, \"app-map-view\", 67);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(43, DeliveryTrackingComponent_ng_container_12_div_43_Template, 76, 21, \"div\", 68);\n    i0.ɵɵtemplate(44, DeliveryTrackingComponent_ng_container_12_div_44_Template, 7, 0, \"div\", 68);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.statusFilter);\n    i0.ɵɵadvance(17);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.searchTerm);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r3.filteredDeliveries.length);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.filteredDeliveries);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.filteredDeliveries.length === 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"deliveries\", ctx_r3.selectedDelivery ? i0.ɵɵpureFunction1(12, _c1, ctx_r3.selectedDelivery) : i0.ɵɵpureFunction0(14, _c2))(\"drivers\", ctx_r3.getSelectedDrivers())(\"height\", 300)(\"center\", ctx_r3.getMapCenter())(\"zoom\", 14);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.selectedDelivery);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.selectedDelivery);\n  }\n}\nexport let DeliveryTrackingComponent = /*#__PURE__*/(() => {\n  class DeliveryTrackingComponent {\n    constructor(deliveryService, driverService, realTimeService, authService) {\n      this.deliveryService = deliveryService;\n      this.driverService = driverService;\n      this.realTimeService = realTimeService;\n      this.authService = authService;\n      this.deliveries = [];\n      this.drivers = [];\n      this.filteredDeliveries = [];\n      this.selectedDelivery = null;\n      // User context\n      this.currentUser = null;\n      this.isDriverView = false;\n      this.statusFilter = 'all';\n      this.searchTerm = '';\n      this.loading = true;\n      this.error = '';\n      // Modal states\n      this.showDeliveryForm = false;\n      this.showDeleteConfirm = false;\n      this.showStatusUpdate = false;\n      this.editingDelivery = null;\n      this.deletingDelivery = null;\n      this.newStatus = '';\n      this.subscriptions = [];\n    }\n    ngOnInit() {\n      // Check user role to determine view type\n      this.currentUser = this.authService.getCurrentUser();\n      this.isDriverView = this.currentUser?.role === UserRole.Driver;\n      this.loadData();\n      this.setupRealTimeUpdates();\n    }\n    ngOnDestroy() {\n      this.subscriptions.forEach(sub => sub.unsubscribe());\n    }\n    loadData() {\n      this.loading = true;\n      if (this.isDriverView) {\n        // Load driver's deliveries\n        this.subscriptions.push(this.driverService.getCurrentDriverDeliveries().subscribe({\n          next: deliveries => {\n            this.deliveries = deliveries;\n            this.applyFilters();\n            this.loading = false;\n          },\n          error: err => {\n            console.error('Error loading driver deliveries', err);\n            this.error = 'Erreur lors du chargement de vos livraisons';\n            this.loading = false;\n          }\n        }));\n      } else {\n        // Load all deliveries for admin/manager\n        this.subscriptions.push(this.deliveryService.getDeliveries().subscribe({\n          next: deliveries => {\n            this.deliveries = deliveries;\n            this.applyFilters();\n            this.loading = false;\n          },\n          error: err => {\n            console.error('Error loading deliveries', err);\n            this.error = 'Erreur lors du chargement des livraisons';\n            this.loading = false;\n          }\n        }));\n      }\n      // Load drivers\n      this.subscriptions.push(this.driverService.getDrivers().subscribe({\n        next: drivers => {\n          this.drivers = drivers;\n        },\n        error: err => {\n          console.error('Error loading drivers', err);\n        }\n      }));\n    }\n    setupRealTimeUpdates() {\n      // Start SignalR connection\n      this.realTimeService.startConnection().then(() => {\n        console.log('Connected to real-time hub');\n        this.realTimeService.joinAdminGroup();\n        // Subscribe to delivery updates\n        this.subscriptions.push(this.realTimeService.deliveryUpdates$.subscribe(delivery => {\n          if (delivery) {\n            this.updateDelivery(delivery);\n          }\n        }));\n      }).catch(err => {\n        console.error('Error connecting to real-time hub', err);\n      });\n    }\n    updateDelivery(updatedDelivery) {\n      const index = this.deliveries.findIndex(d => d.id === updatedDelivery.id);\n      if (index !== -1) {\n        this.deliveries[index] = updatedDelivery;\n      } else {\n        this.deliveries.push(updatedDelivery);\n      }\n      this.applyFilters();\n      // Update selected delivery if it's the one that was updated\n      if (this.selectedDelivery && this.selectedDelivery.id === updatedDelivery.id) {\n        this.selectedDelivery = updatedDelivery;\n      }\n    }\n    applyFilters() {\n      let filtered = [...this.deliveries];\n      // Apply status filter\n      if (this.statusFilter !== 'all') {\n        filtered = filtered.filter(d => d.status === this.statusFilter);\n      }\n      // Apply search filter\n      if (this.searchTerm.trim() !== '') {\n        const search = this.searchTerm.toLowerCase();\n        filtered = filtered.filter(d => d.customerName.toLowerCase().includes(search) || d.address.toLowerCase().includes(search) || d.orderId.toLowerCase().includes(search) || d.driverName?.toLowerCase().includes(search));\n      }\n      this.filteredDeliveries = filtered;\n    }\n    selectDelivery(delivery) {\n      this.selectedDelivery = delivery;\n    }\n    // CRUD Operations\n    onNewDelivery() {\n      this.editingDelivery = null;\n      this.showDeliveryForm = true;\n    }\n    onEditDelivery(delivery) {\n      this.editingDelivery = {\n        ...delivery\n      };\n      this.showDeliveryForm = true;\n    }\n    onDeleteDelivery(delivery) {\n      this.deletingDelivery = delivery;\n      this.showDeleteConfirm = true;\n    }\n    onUpdateDeliveryStatus(delivery) {\n      this.editingDelivery = delivery;\n      this.newStatus = delivery.status.toString();\n      this.showStatusUpdate = true;\n    }\n    // Form handlers\n    onDeliveryFormSubmit(deliveryData) {\n      if (this.editingDelivery) {\n        // Update existing delivery\n        const updatedDelivery = {\n          ...this.editingDelivery,\n          ...deliveryData\n        };\n        this.subscriptions.push(this.deliveryService.updateDelivery(updatedDelivery).subscribe({\n          next: () => {\n            this.updateDelivery(updatedDelivery);\n            this.showDeliveryForm = false;\n            this.editingDelivery = null;\n          },\n          error: err => {\n            console.error('Error updating delivery', err);\n            this.error = 'Erreur lors de la mise à jour de la livraison';\n          }\n        }));\n      } else {\n        // Create new delivery\n        this.subscriptions.push(this.deliveryService.createDelivery(deliveryData).subscribe({\n          next: newDelivery => {\n            this.deliveries.push(newDelivery);\n            this.applyFilters();\n            this.showDeliveryForm = false;\n          },\n          error: err => {\n            console.error('Error creating delivery', err);\n            this.error = 'Erreur lors de la création de la livraison';\n          }\n        }));\n      }\n    }\n    onDeliveryFormCancel() {\n      this.showDeliveryForm = false;\n      this.editingDelivery = null;\n    }\n    // Delete handlers\n    onDeleteConfirm() {\n      if (this.deletingDelivery) {\n        this.subscriptions.push(this.deliveryService.deleteDelivery(this.deletingDelivery.id).subscribe({\n          next: () => {\n            this.deliveries = this.deliveries.filter(d => d.id !== this.deletingDelivery.id);\n            this.applyFilters();\n            if (this.selectedDelivery?.id === this.deletingDelivery.id) {\n              this.selectedDelivery = null;\n            }\n            this.showDeleteConfirm = false;\n            this.deletingDelivery = null;\n          },\n          error: err => {\n            console.error('Error deleting delivery', err);\n            this.error = 'Erreur lors de la suppression de la livraison';\n          }\n        }));\n      }\n    }\n    onDeleteCancel() {\n      this.showDeleteConfirm = false;\n      this.deletingDelivery = null;\n    }\n    // Status update handlers\n    onStatusUpdateConfirm() {\n      if (this.editingDelivery && this.newStatus) {\n        const statusValue = parseInt(this.newStatus, 10);\n        this.subscriptions.push(this.deliveryService.updateDeliveryStatus(this.editingDelivery.id, statusValue).subscribe({\n          next: updatedDelivery => {\n            this.updateDelivery(updatedDelivery);\n            this.showStatusUpdate = false;\n            this.editingDelivery = null;\n            this.newStatus = '';\n          },\n          error: err => {\n            console.error('Error updating delivery status', err);\n            this.error = 'Erreur lors de la mise à jour du statut';\n          }\n        }));\n      }\n    }\n    onStatusUpdateCancel() {\n      this.showStatusUpdate = false;\n      this.editingDelivery = null;\n      this.newStatus = '';\n    }\n    // Export functionality\n    onExport() {\n      const csvData = this.convertToCSV(this.filteredDeliveries);\n      const blob = new Blob([csvData], {\n        type: 'text/csv;charset=utf-8;'\n      });\n      const link = document.createElement('a');\n      const url = URL.createObjectURL(blob);\n      link.setAttribute('href', url);\n      link.setAttribute('download', `livraisons_${new Date().toISOString().split('T')[0]}.csv`);\n      link.style.visibility = 'hidden';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n    }\n    convertToCSV(deliveries) {\n      const headers = ['ID Commande', 'Client', 'Adresse', 'Statut', 'Livreur', 'Date création', 'Heure estimée', 'Heure réelle'];\n      const csvContent = [headers.join(','), ...deliveries.map(d => [d.orderId, `\"${d.customerName}\"`, `\"${d.address}\"`, this.getStatusText(d.status), `\"${d.driverName || ''}\"`, new Date(d.createdAt).toLocaleDateString(), d.estimatedDeliveryTime ? new Date(d.estimatedDeliveryTime).toLocaleString() : '', d.actualDeliveryTime ? new Date(d.actualDeliveryTime).toLocaleString() : ''].join(','))].join('\\n');\n      return csvContent;\n    }\n    getStatusClass(status) {\n      switch (status) {\n        case DeliveryStatus.Delivered:\n          return 'success';\n        case DeliveryStatus.InTransit:\n          return 'info';\n        case DeliveryStatus.Delayed:\n          return 'danger';\n        case DeliveryStatus.Pending:\n          return 'warning';\n        case DeliveryStatus.Cancelled:\n          return 'default';\n        default:\n          return 'default';\n      }\n    }\n    getStatusText(status) {\n      switch (status) {\n        case DeliveryStatus.Delivered:\n          return 'Livré';\n        case DeliveryStatus.InTransit:\n          return 'En cours';\n        case DeliveryStatus.Delayed:\n          return 'Retardé';\n        case DeliveryStatus.Pending:\n          return 'En attente';\n        case DeliveryStatus.Cancelled:\n          return 'Annulé';\n        default:\n          return status;\n      }\n    }\n    getPriorityText(priority) {\n      const priorityValue = typeof priority === 'string' ? parseInt(priority, 10) : priority;\n      switch (priorityValue) {\n        case 0:\n          // Low\n          return 'Basse';\n        case 1:\n          // Medium\n          return 'Moyenne';\n        case 2:\n          // High\n          return 'Haute';\n        case 3:\n          // Urgent\n          return 'Urgente';\n        default:\n          return priority;\n      }\n    }\n    formatDate(date) {\n      return new Date(date).toLocaleString();\n    }\n    getSelectedDrivers() {\n      if (!this.selectedDelivery) return [];\n      return this.drivers.filter(d => d.id === this.selectedDelivery?.driverId);\n    }\n    getMapCenter() {\n      if (!this.selectedDelivery) return undefined;\n      return [this.selectedDelivery.coordinates.latitude, this.selectedDelivery.coordinates.longitude];\n    }\n    static {\n      this.ɵfac = function DeliveryTrackingComponent_Factory(t) {\n        return new (t || DeliveryTrackingComponent)(i0.ɵɵdirectiveInject(i1.DeliveryService), i0.ɵɵdirectiveInject(i2.DriverService), i0.ɵɵdirectiveInject(i3.RealTimeService), i0.ɵɵdirectiveInject(i4.AuthService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: DeliveryTrackingComponent,\n        selectors: [[\"app-delivery-tracking\"]],\n        decls: 67,\n        vars: 21,\n        consts: [[1, \"tracking-container\"], [1, \"tracking-content\"], [\"title\", \"Suivi des livraisons\", \"subtitle\", \"Suivez en temps r\\u00E9el l'\\u00E9tat de toutes les livraisons\"], [1, \"d-flex\", \"gap-2\"], [\"class\", \"btn btn-primary\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-secondary\", 3, \"click\"], [1, \"fa-solid\", \"fa-file-export\", \"me-2\"], [1, \"tracking-body\", \"p-4\"], [\"class\", \"text-center py-5\", 4, \"ngIf\"], [\"class\", \"alert alert-danger\", 4, \"ngIf\"], [4, \"ngIf\"], [\"tabindex\", \"-1\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\", \"modal-lg\"], [1, \"modal-content\"], [1, \"modal-header\"], [1, \"modal-title\"], [\"type\", \"button\", 1, \"btn-close\", 3, \"click\"], [1, \"modal-body\"], [3, \"delivery\", \"submitted\", \"cancelled\"], [1, \"modal-dialog\"], [1, \"modal-footer\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-danger\", 3, \"click\"], [1, \"mb-3\"], [\"for\", \"newStatus\", 1, \"form-label\"], [\"id\", \"newStatus\", 1, \"form-select\", 3, \"ngModel\", \"ngModelChange\"], [\"value\", \"0\"], [\"value\", \"1\"], [\"value\", \"2\"], [\"value\", \"3\"], [\"value\", \"4\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"fa-solid\", \"fa-plus\", \"me-2\"], [1, \"text-center\", \"py-5\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"mt-2\"], [1, \"alert\", \"alert-danger\"], [1, \"row\", \"g-4\"], [1, \"col-lg-5\"], [1, \"card\", \"mb-4\"], [1, \"card-body\"], [1, \"row\", \"g-3\"], [1, \"col-md-6\"], [\"for\", \"statusFilter\", 1, \"form-label\"], [\"id\", \"statusFilter\", 1, \"form-select\", 3, \"ngModel\", \"ngModelChange\", \"change\"], [\"value\", \"all\"], [\"value\", \"Pending\"], [\"value\", \"InTransit\"], [\"value\", \"Delivered\"], [\"value\", \"Delayed\"], [\"value\", \"Cancelled\"], [\"for\", \"searchFilter\", 1, \"form-label\"], [1, \"input-group\"], [\"type\", \"text\", \"id\", \"searchFilter\", \"placeholder\", \"Rechercher...\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\", \"input\"], [1, \"input-group-text\"], [1, \"fa-solid\", \"fa-search\"], [1, \"card\"], [1, \"card-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"card-title\", \"mb-0\"], [1, \"badge\", \"bg-primary\"], [1, \"card-body\", \"p-0\"], [1, \"list-group\", \"list-group-flush\", \"delivery-list\"], [\"class\", \"list-group-item list-group-item-action p-3\", 3, \"active\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"p-4 text-center text-muted\", 4, \"ngIf\"], [1, \"col-lg-7\"], [3, \"deliveries\", \"drivers\", \"height\", \"center\", \"zoom\"], [\"class\", \"card\", 4, \"ngIf\"], [1, \"list-group-item\", \"list-group-item-action\", \"p-3\", 3, \"click\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"mb-1\"], [1, \"text-muted\", \"small\", \"mb-0\"], [1, \"text-end\"], [3, \"status\", \"variant\"], [1, \"text-muted\", \"small\", \"mt-1\"], [1, \"p-4\", \"text-center\", \"text-muted\"], [1, \"fa-solid\", \"fa-box-open\", \"fa-2x\", \"mb-3\"], [1, \"card-header\"], [1, \"row\"], [1, \"text-muted\", \"mb-3\"], [1, \"row\", \"mb-0\"], [1, \"col-sm-5\"], [1, \"col-sm-7\"], [1, \"mt-4\"], [1, \"mb-0\"], [\"class\", \"mb-2\", 4, \"ngIf\"], [\"class\", \"mt-4 d-flex gap-2\", 4, \"ngIf\"], [1, \"mb-2\"], [1, \"d-flex\", \"align-items-center\"], [1, \"me-2\"], [1, \"rating\"], [\"class\", \"fa-solid fa-star\", 3, \"text-warning\", \"text-muted\", 4, \"ngFor\", \"ngForOf\"], [1, \"fa-solid\", \"fa-star\"], [1, \"mt-4\", \"d-flex\", \"gap-2\"], [1, \"fa-solid\", \"fa-pen-to-square\", \"me-2\"], [1, \"btn\", \"btn-outline-danger\", 3, \"click\"], [1, \"fa-solid\", \"fa-trash\", \"me-2\"], [1, \"fa-solid\", \"fa-sync\", \"me-2\"], [1, \"card-body\", \"text-center\", \"p-5\"], [1, \"fa-solid\", \"fa-box\", \"fa-3x\", \"text-muted\", \"mb-3\"], [1, \"text-muted\"]],\n        template: function DeliveryTrackingComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0);\n            i0.ɵɵelement(1, \"app-sidebar\");\n            i0.ɵɵelementStart(2, \"div\", 1)(3, \"app-header\", 2)(4, \"div\", 3);\n            i0.ɵɵtemplate(5, DeliveryTrackingComponent_button_5_Template, 3, 0, \"button\", 4);\n            i0.ɵɵelementStart(6, \"button\", 5);\n            i0.ɵɵlistener(\"click\", function DeliveryTrackingComponent_Template_button_click_6_listener() {\n              return ctx.onExport();\n            });\n            i0.ɵɵelement(7, \"i\", 6);\n            i0.ɵɵtext(8, \" Exporter \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(9, \"div\", 7);\n            i0.ɵɵtemplate(10, DeliveryTrackingComponent_div_10_Template, 6, 0, \"div\", 8);\n            i0.ɵɵtemplate(11, DeliveryTrackingComponent_div_11_Template, 2, 1, \"div\", 9);\n            i0.ɵɵtemplate(12, DeliveryTrackingComponent_ng_container_12_Template, 45, 15, \"ng-container\", 10);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(13, \"div\", 11)(14, \"div\", 12)(15, \"div\", 13)(16, \"div\", 14)(17, \"h5\", 15);\n            i0.ɵɵtext(18);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(19, \"button\", 16);\n            i0.ɵɵlistener(\"click\", function DeliveryTrackingComponent_Template_button_click_19_listener() {\n              return ctx.onDeliveryFormCancel();\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(20, \"div\", 17)(21, \"app-delivery-form\", 18);\n            i0.ɵɵlistener(\"submitted\", function DeliveryTrackingComponent_Template_app_delivery_form_submitted_21_listener($event) {\n              return ctx.onDeliveryFormSubmit($event);\n            })(\"cancelled\", function DeliveryTrackingComponent_Template_app_delivery_form_cancelled_21_listener() {\n              return ctx.onDeliveryFormCancel();\n            });\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(22, \"div\", 11)(23, \"div\", 19)(24, \"div\", 13)(25, \"div\", 14)(26, \"h5\", 15);\n            i0.ɵɵtext(27, \"Confirmer la suppression\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(28, \"button\", 16);\n            i0.ɵɵlistener(\"click\", function DeliveryTrackingComponent_Template_button_click_28_listener() {\n              return ctx.onDeleteCancel();\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(29, \"div\", 17)(30, \"p\");\n            i0.ɵɵtext(31, \"\\u00CAtes-vous s\\u00FBr de vouloir supprimer cette livraison ?\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(32, \"p\")(33, \"strong\");\n            i0.ɵɵtext(34);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(35, \"div\", 20)(36, \"button\", 21);\n            i0.ɵɵlistener(\"click\", function DeliveryTrackingComponent_Template_button_click_36_listener() {\n              return ctx.onDeleteCancel();\n            });\n            i0.ɵɵtext(37, \"Annuler\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(38, \"button\", 22);\n            i0.ɵɵlistener(\"click\", function DeliveryTrackingComponent_Template_button_click_38_listener() {\n              return ctx.onDeleteConfirm();\n            });\n            i0.ɵɵtext(39, \"Supprimer\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(40, \"div\", 11)(41, \"div\", 19)(42, \"div\", 13)(43, \"div\", 14)(44, \"h5\", 15);\n            i0.ɵɵtext(45, \"Mettre \\u00E0 jour le statut\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(46, \"button\", 16);\n            i0.ɵɵlistener(\"click\", function DeliveryTrackingComponent_Template_button_click_46_listener() {\n              return ctx.onStatusUpdateCancel();\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(47, \"div\", 17)(48, \"div\", 23)(49, \"label\", 24);\n            i0.ɵɵtext(50, \"Nouveau statut\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(51, \"select\", 25);\n            i0.ɵɵlistener(\"ngModelChange\", function DeliveryTrackingComponent_Template_select_ngModelChange_51_listener($event) {\n              return ctx.newStatus = $event;\n            });\n            i0.ɵɵelementStart(52, \"option\", 26);\n            i0.ɵɵtext(53, \"En attente\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(54, \"option\", 27);\n            i0.ɵɵtext(55, \"En cours\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(56, \"option\", 28);\n            i0.ɵɵtext(57, \"Livr\\u00E9\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(58, \"option\", 29);\n            i0.ɵɵtext(59, \"Retard\\u00E9\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(60, \"option\", 30);\n            i0.ɵɵtext(61, \"Annul\\u00E9\");\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵelementStart(62, \"div\", 20)(63, \"button\", 21);\n            i0.ɵɵlistener(\"click\", function DeliveryTrackingComponent_Template_button_click_63_listener() {\n              return ctx.onStatusUpdateCancel();\n            });\n            i0.ɵɵtext(64, \"Annuler\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(65, \"button\", 31);\n            i0.ɵɵlistener(\"click\", function DeliveryTrackingComponent_Template_button_click_65_listener() {\n              return ctx.onStatusUpdateConfirm();\n            });\n            i0.ɵɵtext(66, \"Mettre \\u00E0 jour\");\n            i0.ɵɵelementEnd()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isDriverView);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵstyleProp(\"display\", ctx.showDeliveryForm ? \"block\" : \"none\");\n            i0.ɵɵclassProp(\"show\", ctx.showDeliveryForm);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate1(\" \", ctx.editingDelivery ? \"Modifier la livraison\" : \"Nouvelle livraison\", \" \");\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"delivery\", ctx.editingDelivery);\n            i0.ɵɵadvance(1);\n            i0.ɵɵstyleProp(\"display\", ctx.showDeleteConfirm ? \"block\" : \"none\");\n            i0.ɵɵclassProp(\"show\", ctx.showDeleteConfirm);\n            i0.ɵɵadvance(12);\n            i0.ɵɵtextInterpolate2(\"\", ctx.deletingDelivery == null ? null : ctx.deletingDelivery.customerName, \" - \", ctx.deletingDelivery == null ? null : ctx.deletingDelivery.orderId, \"\");\n            i0.ɵɵadvance(6);\n            i0.ɵɵstyleProp(\"display\", ctx.showStatusUpdate ? \"block\" : \"none\");\n            i0.ɵɵclassProp(\"show\", ctx.showStatusUpdate);\n            i0.ɵɵadvance(11);\n            i0.ɵɵproperty(\"ngModel\", ctx.newStatus);\n          }\n        },\n        dependencies: [i5.NgForOf, i5.NgIf, i6.NgSelectOption, i6.ɵNgSelectMultipleOption, i6.DefaultValueAccessor, i6.SelectControlValueAccessor, i6.NgControlStatus, i6.NgModel, i7.SidebarComponent, i8.HeaderComponent, i9.StatusBadgeComponent, i10.MapViewComponent, i11.DeliveryFormComponent, i5.DecimalPipe, i5.DatePipe],\n        styles: [\".tracking-container[_ngcontent-%COMP%]{display:flex;height:100vh;overflow:hidden}.tracking-content[_ngcontent-%COMP%]{flex:1;display:flex;flex-direction:column;overflow:hidden}.tracking-body[_ngcontent-%COMP%]{flex:1;overflow-y:auto;background-color:var(--gray-50)}.delivery-list[_ngcontent-%COMP%]{max-height:600px;overflow-y:auto}.list-group-item.active[_ngcontent-%COMP%]{background-color:var(--primary-light);color:var(--primary-dark);border-color:var(--primary-light)}.list-group-item.active[_ngcontent-%COMP%]   .text-muted[_ngcontent-%COMP%]{color:var(--primary-dark)!important;opacity:.8}.rating[_ngcontent-%COMP%]{font-size:1.25rem}\"]\n      });\n    }\n  }\n  return DeliveryTrackingComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}