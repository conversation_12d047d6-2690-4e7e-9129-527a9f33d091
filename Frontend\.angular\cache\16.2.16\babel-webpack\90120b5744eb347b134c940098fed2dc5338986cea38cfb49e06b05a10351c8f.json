{"ast": null, "code": "export var DriverStatus;\n(function (DriverStatus) {\n  DriverStatus[DriverStatus[\"Available\"] = 0] = \"Available\";\n  DriverStatus[DriverStatus[\"Busy\"] = 1] = \"Busy\";\n  DriverStatus[DriverStatus[\"OnBreak\"] = 2] = \"OnBreak\";\n  DriverStatus[DriverStatus[\"Offline\"] = 3] = \"Offline\";\n})(DriverStatus || (DriverStatus = {}));", "map": {"version": 3, "names": ["DriverStatus"], "sources": ["C:\\Users\\<USER>\\delivery-dash-optimiser\\frontend\\src\\app\\core\\models\\driver.model.ts"], "sourcesContent": ["import { GeoCoordinates } from './delivery.model';\n\nexport interface Driver {\n  id: string;\n  name: string;\n  email: string;\n  phone: string;\n  vehicleType: string;\n  vehicleId?: string;\n  rating: number;\n  totalDeliveries: number;\n  onTimeRate: number;\n  status: DriverStatus;\n  currentLocation: GeoCoordinates;\n  todayDeliveries: number;\n  avgDeliveryTime: number;\n  lastActive: Date;\n  profilePictureUrl?: string;\n  hireDate: Date;\n  licenseNumber?: string;\n  licenseExpiryDate?: Date;\n  isAvailableForUrgentDeliveries: boolean;\n  preferredZones?: string;\n  maxDeliveriesPerDay: number;\n}\n\nexport enum DriverStatus {\n  Available = 0,\n  Busy = 1,\n  OnBreak = 2,\n  Offline = 3\n}\n\nexport interface DriverPerformance {\n  driverId: string;\n  driverName: string;\n  totalDeliveries: number;\n  onTimeDeliveries: number;\n  delayedDeliveries: number;\n  onTimeRate: number;\n  avgDeliveryTime: number;\n  avgSpeed: number;\n  customerSatisfaction: number;\n  fuelEfficiency: number;\n  totalDistance: number;\n  totalWorkingHours: number;\n  deliveriesPerHour: number;\n  period: Date;\n}\n"], "mappings": "AA0BA,WAAYA,YAKX;AALD,WAAYA,YAAY;EACtBA,YAAA,CAAAA,YAAA,gCAAa;EACbA,YAAA,CAAAA,YAAA,sBAAQ;EACRA,YAAA,CAAAA,YAAA,4BAAW;EACXA,YAAA,CAAAA,YAAA,4BAAW;AACb,CAAC,EALWA,YAAY,KAAZA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}