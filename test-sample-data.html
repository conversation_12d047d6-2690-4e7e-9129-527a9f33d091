<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Sample Data</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin-bottom: 30px; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .result { margin-top: 10px; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        button { padding: 10px 20px; margin: 5px; background-color: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Create Sample Data for Testing</h1>

        <div class="section">
            <h2>1. Create Sample Driver</h2>
            <p>This will create a test driver for route optimization.</p>
            <button onclick="createSampleDriver()">Create Sample Driver</button>
            <div id="driverResult"></div>
        </div>

        <div class="section">
            <h2>2. Create Sample Deliveries</h2>
            <p>This will create test deliveries for route optimization.</p>
            <button onclick="createSampleDeliveries()">Create Sample Deliveries</button>
            <div id="deliveriesResult"></div>
        </div>

        <div class="section">
            <h2>3. Test Route Optimization</h2>
            <p>After creating drivers and deliveries, test the route optimization.</p>
            <button onclick="window.open('http://localhost:4200/route-optimization', '_blank')">Open Route Optimization</button>
        </div>
    </div>

    <script>
        async function getAuthToken() {
            try {
                const response = await fetch('http://localhost:5001/api/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        username: 'admin123',
                        password: 'Admin@123'
                    })
                });

                if (!response.ok) {
                    throw new Error('Login failed');
                }

                const data = await response.json();
                return data.token;
            } catch (error) {
                console.error('Auth error:', error);
                throw error;
            }
        }

        async function createSampleDriver() {
            const resultDiv = document.getElementById('driverResult');
            resultDiv.innerHTML = '<p>Creating driver...</p>';

            try {
                const token = await getAuthToken();

                const driverData = {
                    name: 'Jean Dupont',
                    email: '<EMAIL>',
                    phone: '+33123456789',
                    vehicleType: 'Voiture',
                    vehicleId: 'VH001',
                    status: 0, // Available
                    currentLocation: {
                        Latitude: 48.8566,
                        Longitude: 2.3522
                    },
                    profilePictureUrl: null,
                    licenseNumber: 'FR123456789',
                    licenseExpiryDate: '2025-12-31',
                    isAvailableForUrgentDeliveries: true,
                    preferredZones: 'Centre-ville, Zone Nord',
                    maxDeliveriesPerDay: 20
                };

                const response = await fetch('http://localhost:5001/api/drivers', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify(driverData)
                });

                const responseData = await response.text();
                let parsedData;
                try {
                    parsedData = JSON.parse(responseData);
                } catch {
                    parsedData = responseData;
                }

                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h4>✅ Driver Created Successfully!</h4>
                            <p><strong>Driver ID:</strong> ${parsedData.id}</p>
                            <p><strong>Name:</strong> ${parsedData.name}</p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h4>❌ Error Creating Driver</h4>
                            <p><strong>Status:</strong> ${response.status} ${response.statusText}</p>
                            <pre>${JSON.stringify(parsedData, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h4>❌ Network Error</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        async function createSampleDeliveries() {
            const resultDiv = document.getElementById('deliveriesResult');
            resultDiv.innerHTML = '<p>Creating deliveries...</p>';

            try {
                const token = await getAuthToken();

                const deliveries = [
                    {
                        orderId: 'ORD001',
                        customerId: 'CUST001',
                        customerName: 'Marie Martin',
                        address: '123 Rue de Rivoli, 75001 Paris',
                        status: 0, // Pending
                        driverId: '', // Empty string for unassigned
                        driverName: '',
                        estimatedDeliveryTime: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(), // 2 hours from now
                        priority: 1, // Medium
                        coordinates: {
                            Latitude: 48.8606,
                            Longitude: 2.3376
                        },
                        createdAt: new Date().toISOString(),
                        notes: 'Livraison en main propre',
                        id: '', // Will be generated by backend
                        actualDeliveryTime: null,
                        pickupTime: null,
                        distance: null,
                        customerRating: null,
                        customerFeedback: null,
                        weatherCondition: null,
                        trafficCondition: null
                    },
                    {
                        orderId: 'ORD002',
                        customerId: 'CUST002',
                        customerName: 'Pierre Durand',
                        address: '45 Avenue des Champs-Élysées, 75008 Paris',
                        status: 0, // Pending
                        driverId: '', // Empty string for unassigned
                        driverName: '',
                        estimatedDeliveryTime: new Date(Date.now() + 3 * 60 * 60 * 1000).toISOString(), // 3 hours from now
                        priority: 3, // Urgent
                        coordinates: {
                            Latitude: 48.8698,
                            Longitude: 2.3075
                        },
                        createdAt: new Date().toISOString(),
                        notes: 'Livraison urgente',
                        id: '', // Will be generated by backend
                        actualDeliveryTime: null,
                        pickupTime: null,
                        distance: null,
                        customerRating: null,
                        customerFeedback: null,
                        weatherCondition: null,
                        trafficCondition: null
                    },
                    {
                        orderId: 'ORD003',
                        customerId: 'CUST003',
                        customerName: 'Sophie Leblanc',
                        address: '78 Boulevard Saint-Germain, 75005 Paris',
                        status: 0, // Pending
                        driverId: '', // Empty string for unassigned
                        driverName: '',
                        estimatedDeliveryTime: new Date(Date.now() + 4 * 60 * 60 * 1000).toISOString(), // 4 hours from now
                        priority: 0, // Low
                        coordinates: {
                            Latitude: 48.8534,
                            Longitude: 2.3488
                        },
                        createdAt: new Date().toISOString(),
                        notes: 'Pas de contrainte horaire',
                        id: '', // Will be generated by backend
                        actualDeliveryTime: null,
                        pickupTime: null,
                        distance: null,
                        customerRating: null,
                        customerFeedback: null,
                        weatherCondition: null,
                        trafficCondition: null
                    }
                ];

                let successCount = 0;
                let errors = [];

                for (let i = 0; i < deliveries.length; i++) {
                    try {
                        const response = await fetch('http://localhost:5001/api/deliveries', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': `Bearer ${token}`
                            },
                            body: JSON.stringify(deliveries[i])
                        });

                        if (response.ok) {
                            successCount++;
                        } else {
                            const errorData = await response.text();
                            errors.push(`Delivery ${i + 1}: ${response.status} - ${errorData}`);
                        }
                    } catch (error) {
                        errors.push(`Delivery ${i + 1}: ${error.message}`);
                    }
                }

                if (successCount === deliveries.length) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h4>✅ All Deliveries Created Successfully!</h4>
                            <p><strong>Created:</strong> ${successCount} deliveries</p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h4>⚠️ Partial Success</h4>
                            <p><strong>Created:</strong> ${successCount} out of ${deliveries.length} deliveries</p>
                            <p><strong>Errors:</strong></p>
                            <ul>${errors.map(err => `<li>${err}</li>`).join('')}</ul>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h4>❌ Network Error</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
