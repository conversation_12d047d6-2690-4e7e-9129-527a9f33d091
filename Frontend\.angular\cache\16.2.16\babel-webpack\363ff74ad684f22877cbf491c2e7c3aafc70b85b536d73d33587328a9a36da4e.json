{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/delivery-dash-optimiser/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport { DeliveryStatus } from '../../core/models/delivery.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"../../core/services/delivery.service\";\nimport * as i4 from \"../../core/services/real-time.service\";\nimport * as i5 from \"../../core/services/auth.service\";\nimport * as i6 from \"@angular/common\";\nfunction CustomerPortalComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵelement(1, \"i\", 27);\n    i0.ɵɵelementStart(2, \"span\", 28);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r0.currentUser.firstName, \" \", ctx_r0.currentUser.lastName, \"\");\n  }\n}\nfunction CustomerPortalComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"div\", 30)(2, \"span\", 31);\n    i0.ɵɵtext(3, \"Chargement...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 32);\n    i0.ɵɵtext(5, \"Recherche de votre livraison...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CustomerPortalComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵelement(1, \"i\", 34);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.error, \" \");\n  }\n}\nfunction CustomerPortalComponent_div_33_div_1_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"div\", 79)(2, \"div\", 80);\n    i0.ɵɵelement(3, \"i\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h6\", 81);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const step_r13 = ctx.$implicit;\n    const i_r14 = ctx.index;\n    const ctx_r8 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"completed\", ctx_r8.isStatusCompleted(i_r14))(\"active\", ctx_r8.isStatusActive(i_r14));\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(step_r13.icon);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"text-primary\", ctx_r8.isStatusActive(i_r14))(\"text-success\", ctx_r8.isStatusCompleted(i_r14) && !ctx_r8.isStatusActive(i_r14));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", step_r13.label, \" \");\n  }\n}\nfunction CustomerPortalComponent_div_33_div_1_div_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵelement(1, \"div\", 82);\n    i0.ɵɵelementStart(2, \"div\", 61)(3, \"h6\", 62);\n    i0.ɵɵtext(4, \"Prise en charge\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 63);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r9.formatDate(ctx_r9.delivery.pickupTime));\n  }\n}\nfunction CustomerPortalComponent_div_33_div_1_div_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵelement(1, \"div\", 60);\n    i0.ɵɵelementStart(2, \"div\", 61)(3, \"h6\", 62);\n    i0.ɵɵtext(4, \"Livraison effectu\\u00E9e\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 63);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r10.formatDate(ctx_r10.delivery.actualDeliveryTime));\n  }\n}\nfunction CustomerPortalComponent_div_33_div_1_div_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 83)(1, \"h6\", 84);\n    i0.ɵɵtext(2, \"Instructions sp\\u00E9ciales\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 85);\n    i0.ɵɵelement(4, \"i\", 86);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r11.delivery.notes, \" \");\n  }\n}\nfunction CustomerPortalComponent_div_33_div_1_div_92_div_6_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 92);\n  }\n  if (rf & 2) {\n    const star_r19 = ctx.$implicit;\n    const ctx_r18 = i0.ɵɵnextContext(5);\n    i0.ɵɵclassProp(\"text-warning\", star_r19 <= ctx_r18.delivery.customerRating)(\"text-muted\", star_r19 > ctx_r18.delivery.customerRating);\n  }\n}\nconst _c0 = function () {\n  return [1, 2, 3, 4, 5];\n};\nfunction CustomerPortalComponent_div_33_div_1_div_92_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 89)(1, \"p\", 73);\n    i0.ɵɵtext(2, \"Note donn\\u00E9e:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 90);\n    i0.ɵɵtemplate(4, CustomerPortalComponent_div_33_div_1_div_92_div_6_i_4_Template, 1, 4, \"i\", 91);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction CustomerPortalComponent_div_33_div_1_div_92_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 89)(1, \"p\", 73);\n    i0.ɵɵtext(2, \"Commentaire:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 93);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r16.delivery.customerFeedback);\n  }\n}\nfunction CustomerPortalComponent_div_33_div_1_div_92_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"p\", 93);\n    i0.ɵɵtext(2, \"Merci de nous faire part de votre exp\\u00E9rience!\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 94);\n    i0.ɵɵelement(4, \"i\", 87);\n    i0.ɵɵtext(5, \" Donner mon avis \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CustomerPortalComponent_div_33_div_1_div_92_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"div\", 71)(2, \"h6\", 47);\n    i0.ɵɵelement(3, \"i\", 87);\n    i0.ɵɵtext(4, \" Votre avis \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 36);\n    i0.ɵɵtemplate(6, CustomerPortalComponent_div_33_div_1_div_92_div_6_Template, 5, 2, \"div\", 88);\n    i0.ɵɵtemplate(7, CustomerPortalComponent_div_33_div_1_div_92_div_7_Template, 5, 1, \"div\", 88);\n    i0.ɵɵtemplate(8, CustomerPortalComponent_div_33_div_1_div_92_div_8_Template, 6, 0, \"div\", 25);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r12.delivery.customerRating);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r12.delivery.customerFeedback);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r12.delivery.customerRating);\n  }\n}\nfunction CustomerPortalComponent_div_33_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 35)(2, \"div\", 36)(3, \"h5\", 37);\n    i0.ɵɵelement(4, \"i\", 38);\n    i0.ɵɵtext(5, \" \\u00C9tat de votre livraison \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 39);\n    i0.ɵɵelement(7, \"div\", 40);\n    i0.ɵɵelementStart(8, \"div\", 41);\n    i0.ɵɵtemplate(9, CustomerPortalComponent_div_33_div_1_div_9_Template, 6, 11, \"div\", 42);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(10, \"div\", 43)(11, \"div\", 44)(12, \"div\", 45)(13, \"div\", 46)(14, \"h5\", 47);\n    i0.ɵɵelement(15, \"i\", 48);\n    i0.ɵɵtext(16, \" D\\u00E9tails de la livraison \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\", 49);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 36)(20, \"div\", 50)(21, \"div\", 51)(22, \"label\", 52);\n    i0.ɵɵtext(23, \"Num\\u00E9ro de commande\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"p\", 53);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 51)(27, \"label\", 52);\n    i0.ɵɵtext(28, \"Client\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"p\", 53);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 54)(32, \"label\", 52);\n    i0.ɵɵtext(33, \"Adresse de livraison\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"p\", 53);\n    i0.ɵɵelement(35, \"i\", 55);\n    i0.ɵɵtext(36);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(37, \"div\", 51)(38, \"label\", 52);\n    i0.ɵɵtext(39, \"Livreur\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"p\", 53);\n    i0.ɵɵelement(41, \"i\", 56);\n    i0.ɵɵtext(42);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(43, \"div\", 51)(44, \"label\", 52);\n    i0.ɵɵtext(45, \"Priorit\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"span\", 49);\n    i0.ɵɵtext(47);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(48, \"hr\");\n    i0.ɵɵelementStart(49, \"h6\", 57);\n    i0.ɵɵtext(50, \"Chronologie\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"div\", 58)(52, \"div\", 59);\n    i0.ɵɵelement(53, \"div\", 60);\n    i0.ɵɵelementStart(54, \"div\", 61)(55, \"h6\", 62);\n    i0.ɵɵtext(56, \"Commande cr\\u00E9\\u00E9e\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(57, \"p\", 63);\n    i0.ɵɵtext(58);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(59, CustomerPortalComponent_div_33_div_1_div_59_Template, 7, 1, \"div\", 64);\n    i0.ɵɵtemplate(60, CustomerPortalComponent_div_33_div_1_div_60_Template, 7, 1, \"div\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(61, CustomerPortalComponent_div_33_div_1_div_61_Template, 6, 1, \"div\", 65);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(62, \"div\", 66)(63, \"div\", 35)(64, \"div\", 67);\n    i0.ɵɵelement(65, \"i\", 68);\n    i0.ɵɵelementStart(66, \"h5\", 69);\n    i0.ɵɵtext(67, \"Temps estim\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(68, \"p\", 70);\n    i0.ɵɵtext(69);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(70, \"p\", 63);\n    i0.ɵɵtext(71);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(72, \"div\", 35)(73, \"div\", 71)(74, \"h6\", 47);\n    i0.ɵɵelement(75, \"i\", 72);\n    i0.ɵɵtext(76, \" Contact \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(77, \"div\", 36)(78, \"p\", 73)(79, \"strong\");\n    i0.ɵɵtext(80, \"Service client:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(81, \"br\");\n    i0.ɵɵelementStart(82, \"a\", 74);\n    i0.ɵɵelement(83, \"i\", 72);\n    i0.ɵɵtext(84, \" 01 23 45 67 89 \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(85, \"p\", 75)(86, \"strong\");\n    i0.ɵɵtext(87, \"Email:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(88, \"br\");\n    i0.ɵɵelementStart(89, \"a\", 76);\n    i0.ɵɵelement(90, \"i\", 77);\n    i0.ɵɵtext(91, \" <EMAIL> \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(92, CustomerPortalComponent_div_33_div_1_div_92_Template, 9, 3, \"div\", 78);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.statusSteps);\n    i0.ɵɵadvance(8);\n    i0.ɵɵclassMap(\"bg-\" + ctx_r6.getStatusClass(ctx_r6.delivery.status));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.getStatusText(ctx_r6.delivery.status), \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r6.delivery.orderId);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r6.delivery.customerName);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.delivery.address, \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.delivery.driverName, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassMap(ctx_r6.delivery.priority === 3 ? \"bg-danger\" : ctx_r6.delivery.priority === 2 ? \"bg-warning\" : ctx_r6.delivery.priority === 1 ? \"bg-info\" : \"bg-secondary\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.delivery.priority, \" \");\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate(ctx_r6.formatDate(ctx_r6.delivery.createdAt));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.delivery.pickupTime);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.delivery.actualDeliveryTime);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.delivery.notes);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r6.getEstimatedTimeRemaining());\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" Livraison pr\\u00E9vue le \", ctx_r6.formatDate(ctx_r6.delivery.estimatedDeliveryTime), \" \");\n    i0.ɵɵadvance(21);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.delivery.status === \"Delivered\");\n  }\n}\nfunction CustomerPortalComponent_div_33_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵelement(1, \"i\", 95);\n    i0.ɵɵelementStart(2, \"h4\", 93);\n    i0.ɵɵtext(3, \"Entrez votre num\\u00E9ro de suivi\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 93);\n    i0.ɵɵtext(5, \"Saisissez votre num\\u00E9ro de commande ou de suivi dans le champ ci-dessus\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CustomerPortalComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, CustomerPortalComponent_div_33_div_1_Template, 93, 18, \"div\", 25);\n    i0.ɵɵtemplate(2, CustomerPortalComponent_div_33_div_2_Template, 6, 0, \"div\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.delivery && !ctx_r3.loading && !ctx_r3.error);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.trackingId && !ctx_r3.loading);\n  }\n}\nfunction CustomerPortalComponent_div_34_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 108)(1, \"div\", 30)(2, \"span\", 31);\n    i0.ɵɵtext(3, \"Chargement...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction CustomerPortalComponent_div_34_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 108);\n    i0.ɵɵelement(1, \"i\", 109);\n    i0.ɵɵelementStart(2, \"h5\", 93);\n    i0.ɵɵtext(3, \"Aucune commande trouv\\u00E9e\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 93);\n    i0.ɵɵtext(5, \"Vous n'avez pas encore pass\\u00E9 de commande.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CustomerPortalComponent_div_34_div_26_tr_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"strong\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"br\");\n    i0.ɵɵelementStart(5, \"small\", 93);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵelement(10, \"i\", 113);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\")(13, \"span\", 49);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"td\")(16, \"button\", 114);\n    i0.ɵɵlistener(\"click\", function CustomerPortalComponent_div_34_div_26_tr_15_Template_button_click_16_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r26);\n      const order_r24 = restoredCtx.$implicit;\n      const ctx_r25 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r25.viewOrderDetails(order_r24));\n    });\n    i0.ɵɵelement(17, \"i\", 115);\n    i0.ɵɵtext(18, \" Voir \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"button\", 116);\n    i0.ɵɵlistener(\"click\", function CustomerPortalComponent_div_34_div_26_tr_15_Template_button_click_19_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r26);\n      const order_r24 = restoredCtx.$implicit;\n      const ctx_r27 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r27.trackOrder(order_r24.orderId));\n    });\n    i0.ɵɵelement(20, \"i\", 18);\n    i0.ɵɵtext(21, \" Suivre \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const order_r24 = ctx.$implicit;\n    const ctx_r23 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(order_r24.orderId);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(order_r24.customerName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r23.formatDate(order_r24.createdAt));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", order_r24.address, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(\"bg-\" + ctx_r23.getStatusClass(order_r24.status));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r23.getStatusText(order_r24.status), \" \");\n  }\n}\nfunction CustomerPortalComponent_div_34_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 110)(1, \"table\", 111)(2, \"thead\")(3, \"tr\")(4, \"th\");\n    i0.ɵɵtext(5, \"Commande\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7, \"Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"Adresse\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Statut\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\");\n    i0.ɵɵtext(13, \"Actions\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"tbody\");\n    i0.ɵɵtemplate(15, CustomerPortalComponent_div_34_div_26_tr_15_Template, 22, 7, \"tr\", 112);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r22.filteredOrders);\n  }\n}\nfunction CustomerPortalComponent_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 41)(2, \"div\", 54)(3, \"div\", 45)(4, \"div\", 46)(5, \"h5\", 47);\n    i0.ɵɵelement(6, \"i\", 96);\n    i0.ɵɵtext(7, \" Mes Commandes \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 97)(9, \"select\", 98);\n    i0.ɵɵlistener(\"ngModelChange\", function CustomerPortalComponent_div_34_Template_select_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r28 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r28.orderFilter = $event);\n    })(\"change\", function CustomerPortalComponent_div_34_Template_select_change_9_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r30 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r30.filterOrders());\n    });\n    i0.ɵɵelementStart(10, \"option\", 99);\n    i0.ɵɵtext(11, \"Toutes les commandes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"option\", 100);\n    i0.ɵɵtext(13, \"En attente\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"option\", 101);\n    i0.ɵɵtext(15, \"En cours\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"option\", 102);\n    i0.ɵɵtext(17, \"Livr\\u00E9es\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"option\", 103);\n    i0.ɵɵtext(19, \"Annul\\u00E9es\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"button\", 104);\n    i0.ɵɵlistener(\"click\", function CustomerPortalComponent_div_34_Template_button_click_20_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r31 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r31.loadOrders());\n    });\n    i0.ɵɵelement(21, \"i\", 105);\n    i0.ɵɵtext(22, \" Actualiser \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(23, \"div\", 36);\n    i0.ɵɵtemplate(24, CustomerPortalComponent_div_34_div_24_Template, 4, 0, \"div\", 106);\n    i0.ɵɵtemplate(25, CustomerPortalComponent_div_34_div_25_Template, 6, 0, \"div\", 106);\n    i0.ɵɵtemplate(26, CustomerPortalComponent_div_34_div_26_Template, 16, 1, \"div\", 107);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngModel\", ctx_r4.orderFilter);\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.loadingOrders);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.loadingOrders && ctx_r4.orders.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.loadingOrders && ctx_r4.orders.length > 0);\n  }\n}\nfunction CustomerPortalComponent_div_35_span_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Sauvegarder\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerPortalComponent_div_35_span_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Sauvegarde...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CustomerPortalComponent_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r35 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 43)(2, \"div\", 44)(3, \"div\", 45)(4, \"div\", 71)(5, \"h5\", 47);\n    i0.ɵɵelement(6, \"i\", 117);\n    i0.ɵɵtext(7, \" Informations Personnelles \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 36)(9, \"form\", 118);\n    i0.ɵɵlistener(\"ngSubmit\", function CustomerPortalComponent_div_35_Template_form_ngSubmit_9_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r34 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r34.updateProfile());\n    });\n    i0.ɵɵelementStart(10, \"div\", 50)(11, \"div\", 51)(12, \"label\", 119);\n    i0.ɵɵtext(13, \"Pr\\u00E9nom\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(14, \"input\", 120);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 51)(16, \"label\", 119);\n    i0.ɵɵtext(17, \"Nom\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(18, \"input\", 121);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 54)(20, \"label\", 119);\n    i0.ɵɵtext(21, \"Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"input\", 122);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 51)(24, \"label\", 119);\n    i0.ɵɵtext(25, \"T\\u00E9l\\u00E9phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(26, \"input\", 123);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 51)(28, \"label\", 119);\n    i0.ɵɵtext(29, \"Date de naissance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(30, \"input\", 124);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\", 54)(32, \"label\", 119);\n    i0.ɵɵtext(33, \"Adresse\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(34, \"textarea\", 125);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(35, \"hr\");\n    i0.ɵɵelementStart(36, \"div\", 126)(37, \"button\", 127);\n    i0.ɵɵlistener(\"click\", function CustomerPortalComponent_div_35_Template_button_click_37_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r36 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r36.resetProfile());\n    });\n    i0.ɵɵelement(38, \"i\", 128);\n    i0.ɵɵtext(39, \" Annuler \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"button\", 129);\n    i0.ɵɵelement(41, \"i\", 130);\n    i0.ɵɵtemplate(42, CustomerPortalComponent_div_35_span_42_Template, 2, 0, \"span\", 25);\n    i0.ɵɵtemplate(43, CustomerPortalComponent_div_35_span_43_Template, 2, 0, \"span\", 25);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(44, \"div\", 66)(45, \"div\", 35)(46, \"div\", 71)(47, \"h6\", 47);\n    i0.ɵɵelement(48, \"i\", 131);\n    i0.ɵɵtext(49, \" Param\\u00E8tres du Compte \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(50, \"div\", 36)(51, \"div\", 132)(52, \"button\", 133);\n    i0.ɵɵlistener(\"click\", function CustomerPortalComponent_div_35_Template_button_click_52_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r37 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r37.changePassword());\n    });\n    i0.ɵɵelement(53, \"i\", 134);\n    i0.ɵɵtext(54, \" Changer le mot de passe \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(55, \"button\", 116);\n    i0.ɵɵlistener(\"click\", function CustomerPortalComponent_div_35_Template_button_click_55_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r38 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r38.manageNotifications());\n    });\n    i0.ɵɵelement(56, \"i\", 135);\n    i0.ɵɵtext(57, \" Notifications \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"button\", 136);\n    i0.ɵɵlistener(\"click\", function CustomerPortalComponent_div_35_Template_button_click_58_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r39 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r39.downloadData());\n    });\n    i0.ɵɵelement(59, \"i\", 137);\n    i0.ɵɵtext(60, \" T\\u00E9l\\u00E9charger mes donn\\u00E9es \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(61, \"div\", 45)(62, \"div\", 71)(63, \"h6\", 47);\n    i0.ɵɵelement(64, \"i\", 138);\n    i0.ɵɵtext(65, \" Statistiques \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(66, \"div\", 36)(67, \"div\", 139)(68, \"div\", 140)(69, \"div\", 141)(70, \"h4\", 142);\n    i0.ɵɵtext(71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(72, \"small\", 93);\n    i0.ɵɵtext(73, \"Commandes\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(74, \"div\", 140)(75, \"div\", 141)(76, \"h4\", 143);\n    i0.ɵɵtext(77);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(78, \"small\", 93);\n    i0.ɵɵtext(79, \"Livr\\u00E9es\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(80, \"div\", 54)(81, \"div\", 141)(82, \"h5\", 144);\n    i0.ɵɵtext(83);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(84, \"small\", 93);\n    i0.ɵɵtext(85, \"Note moyenne donn\\u00E9e\");\n    i0.ɵɵelementEnd()()()()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"formGroup\", ctx_r5.profileForm);\n    i0.ɵɵadvance(31);\n    i0.ɵɵproperty(\"disabled\", !ctx_r5.profileForm.valid || ctx_r5.updatingProfile);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r5.updatingProfile);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.updatingProfile);\n    i0.ɵɵadvance(28);\n    i0.ɵɵtextInterpolate(ctx_r5.userStats.totalOrders);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r5.userStats.deliveredOrders);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r5.userStats.averageRating, \"/5\");\n  }\n}\nexport class CustomerPortalComponent {\n  constructor(route, router, formBuilder, deliveryService, realTimeService, authService) {\n    this.route = route;\n    this.router = router;\n    this.formBuilder = formBuilder;\n    this.deliveryService = deliveryService;\n    this.realTimeService = realTimeService;\n    this.authService = authService;\n    // Tab management\n    this.activeTab = 'tracking';\n    // User data\n    this.currentUser = null;\n    // Tracking tab\n    this.delivery = null;\n    this.loading = true;\n    this.error = '';\n    this.trackingId = '';\n    // Orders tab\n    this.orders = [];\n    this.filteredOrders = [];\n    this.loadingOrders = false;\n    this.orderFilter = 'all';\n    this.updatingProfile = false;\n    this.userStats = {\n      totalOrders: 0,\n      deliveredOrders: 0,\n      averageRating: 0\n    };\n    // Real-time updates\n    this.subscriptions = [];\n    // Status tracking\n    this.statusSteps = [{\n      key: DeliveryStatus.Pending,\n      label: 'Commande confirmée',\n      icon: 'fa-check-circle'\n    }, {\n      key: DeliveryStatus.InTransit,\n      label: 'En cours de livraison',\n      icon: 'fa-truck'\n    }, {\n      key: DeliveryStatus.Delivered,\n      label: 'Livré',\n      icon: 'fa-box-check'\n    }];\n    this.profileForm = this.formBuilder.group({\n      firstName: ['', Validators.required],\n      lastName: ['', Validators.required],\n      email: ['', [Validators.required, Validators.email]],\n      phone: [''],\n      dateOfBirth: [''],\n      address: ['']\n    });\n  }\n  ngOnInit() {\n    // Load current user\n    this.currentUser = this.authService.getCurrentUser();\n    if (this.currentUser) {\n      this.initializeProfileForm();\n      this.loadUserStats();\n    }\n    this.route.params.subscribe(params => {\n      this.trackingId = params['trackingId'] || '';\n      if (this.trackingId) {\n        this.setActiveTab('tracking');\n        this.loadDelivery();\n        this.setupRealTimeUpdates();\n      } else {\n        // No tracking ID provided, stop loading and show search form\n        this.loading = false;\n      }\n    });\n    // Load orders if on orders tab\n    if (this.activeTab === 'orders') {\n      this.loadOrders();\n    }\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.realTimeService.stopConnection();\n  }\n  loadDelivery() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.loading = true;\n        _this.error = '';\n        // Try to find delivery by order ID or delivery ID\n        const deliveryByOrderId = yield _this.deliveryService.getDeliveryByOrderId(_this.trackingId).toPromise();\n        _this.delivery = deliveryByOrderId || null;\n        if (!_this.delivery) {\n          const deliveryById = yield _this.deliveryService.getDelivery(_this.trackingId).toPromise();\n          _this.delivery = deliveryById || null;\n        }\n        if (!_this.delivery) {\n          _this.error = 'Livraison non trouvée. Vérifiez votre numéro de suivi.';\n        }\n      } catch (error) {\n        console.error('Error loading delivery:', error);\n        _this.error = 'Erreur lors du chargement des informations de livraison.';\n      } finally {\n        _this.loading = false;\n      }\n    })();\n  }\n  setupRealTimeUpdates() {\n    this.realTimeService.startConnection().then(() => {\n      console.log('Connected to real-time tracking');\n      // Subscribe to delivery updates\n      this.subscriptions.push(this.realTimeService.deliveryUpdates$.subscribe(updatedDelivery => {\n        if (updatedDelivery && this.delivery && updatedDelivery.id === this.delivery.id) {\n          this.delivery = {\n            ...this.delivery,\n            ...updatedDelivery\n          };\n        }\n      }));\n    }).catch(err => {\n      console.error('Error connecting to real-time tracking', err);\n    });\n  }\n  trackDelivery() {\n    if (this.trackingId.trim()) {\n      this.error = ''; // Clear any previous errors\n      this.loadDelivery();\n      this.setupRealTimeUpdates();\n    }\n  }\n  getStatusIndex(status) {\n    return this.statusSteps.findIndex(step => step.key === status);\n  }\n  isStatusCompleted(stepIndex) {\n    if (!this.delivery) return false;\n    const currentIndex = this.getStatusIndex(this.delivery.status);\n    return stepIndex <= currentIndex;\n  }\n  isStatusActive(stepIndex) {\n    if (!this.delivery) return false;\n    const currentIndex = this.getStatusIndex(this.delivery.status);\n    return stepIndex === currentIndex;\n  }\n  getStatusText(status) {\n    switch (status) {\n      case DeliveryStatus.Pending:\n        return 'En attente';\n      case DeliveryStatus.InTransit:\n        return 'En cours';\n      case DeliveryStatus.Delivered:\n        return 'Livré';\n      case DeliveryStatus.Delayed:\n        return 'Retardé';\n      case DeliveryStatus.Cancelled:\n        return 'Annulé';\n      default:\n        return 'Inconnu';\n    }\n  }\n  getStatusClass(status) {\n    switch (status) {\n      case DeliveryStatus.Pending:\n        return 'warning';\n      case DeliveryStatus.InTransit:\n        return 'info';\n      case DeliveryStatus.Delivered:\n        return 'success';\n      case DeliveryStatus.Delayed:\n        return 'danger';\n      case DeliveryStatus.Cancelled:\n        return 'secondary';\n      default:\n        return 'secondary';\n    }\n  }\n  formatDate(date) {\n    if (!date) return 'Non défini';\n    const d = new Date(date);\n    return d.toLocaleDateString('fr-FR', {\n      day: '2-digit',\n      month: '2-digit',\n      year: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n  getEstimatedTimeRemaining() {\n    if (!this.delivery || !this.delivery.estimatedDeliveryTime) {\n      return 'Non disponible';\n    }\n    const now = new Date();\n    const estimated = new Date(this.delivery.estimatedDeliveryTime);\n    const diffMs = estimated.getTime() - now.getTime();\n    if (diffMs <= 0) {\n      return 'Livraison prévue';\n    }\n    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));\n    const diffMinutes = Math.floor(diffMs % (1000 * 60 * 60) / (1000 * 60));\n    if (diffHours > 0) {\n      return `${diffHours}h ${diffMinutes}min`;\n    } else {\n      return `${diffMinutes}min`;\n    }\n  }\n  // Tab management\n  setActiveTab(tab) {\n    this.activeTab = tab;\n    if (tab === 'orders' && this.orders.length === 0) {\n      this.loadOrders();\n    }\n  }\n  // Logout functionality\n  logout() {\n    this.authService.logout().subscribe({\n      next: () => {\n        // Small delay to ensure authentication state is cleared\n        setTimeout(() => {\n          this.router.navigate(['/login']);\n        }, 100);\n      },\n      error: error => {\n        console.error('Logout error:', error);\n        // Navigate anyway since local storage is already cleared\n        setTimeout(() => {\n          this.router.navigate(['/login']);\n        }, 100);\n      }\n    });\n  }\n  // Orders management\n  loadOrders() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this2.currentUser) return;\n      try {\n        _this2.loadingOrders = true;\n        // Get all deliveries for the current user\n        _this2.orders = (yield _this2.deliveryService.getDeliveriesByCustomer(_this2.currentUser.id).toPromise()) || [];\n        _this2.filterOrders();\n      } catch (error) {\n        console.error('Error loading orders:', error);\n      } finally {\n        _this2.loadingOrders = false;\n      }\n    })();\n  }\n  filterOrders() {\n    if (this.orderFilter === 'all') {\n      this.filteredOrders = [...this.orders];\n    } else {\n      this.filteredOrders = this.orders.filter(order => {\n        switch (this.orderFilter) {\n          case 'pending':\n            return order.status === DeliveryStatus.Pending;\n          case 'in-transit':\n            return order.status === DeliveryStatus.InTransit;\n          case 'delivered':\n            return order.status === DeliveryStatus.Delivered;\n          case 'cancelled':\n            return order.status === DeliveryStatus.Cancelled;\n          default:\n            return true;\n        }\n      });\n    }\n  }\n  viewOrderDetails(order) {\n    // Navigate to tracking with this order\n    this.trackingId = order.orderId;\n    this.setActiveTab('tracking');\n    this.trackDelivery();\n  }\n  trackOrder(orderId) {\n    this.trackingId = orderId;\n    this.setActiveTab('tracking');\n    this.trackDelivery();\n  }\n  // Profile management\n  initializeProfileForm() {\n    if (this.currentUser) {\n      this.profileForm.patchValue({\n        firstName: this.currentUser.firstName,\n        lastName: this.currentUser.lastName,\n        email: this.currentUser.email,\n        phone: this.currentUser.phone || '',\n        dateOfBirth: this.currentUser.dateOfBirth || '',\n        address: this.currentUser.address || ''\n      });\n    }\n  }\n  updateProfile() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this3.profileForm.valid || !_this3.currentUser) return;\n      try {\n        _this3.updatingProfile = true;\n        const formData = _this3.profileForm.value;\n        // Update user profile\n        yield _this3.authService.updateProfile({\n          ..._this3.currentUser,\n          ...formData\n        }).toPromise();\n        // Update local user data\n        _this3.currentUser = {\n          ..._this3.currentUser,\n          ...formData\n        };\n        // Show success message (you can implement a toast service)\n        console.log('Profile updated successfully');\n      } catch (error) {\n        console.error('Error updating profile:', error);\n      } finally {\n        _this3.updatingProfile = false;\n      }\n    })();\n  }\n  resetProfile() {\n    this.initializeProfileForm();\n  }\n  // Account settings\n  changePassword() {\n    // Implement password change modal/dialog\n    console.log('Change password clicked');\n  }\n  manageNotifications() {\n    // Implement notification settings\n    console.log('Manage notifications clicked');\n  }\n  downloadData() {\n    // Implement data download\n    console.log('Download data clicked');\n  }\n  // User statistics\n  loadUserStats() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this4.currentUser) return;\n      try {\n        // Load user statistics\n        const orders = (yield _this4.deliveryService.getDeliveriesByCustomer(_this4.currentUser.id).toPromise()) || [];\n        _this4.userStats = {\n          totalOrders: orders.length,\n          deliveredOrders: orders.filter(o => o.status === DeliveryStatus.Delivered).length,\n          averageRating: _this4.calculateAverageRating(orders)\n        };\n      } catch (error) {\n        console.error('Error loading user stats:', error);\n      }\n    })();\n  }\n  calculateAverageRating(orders) {\n    const ratedOrders = orders.filter(o => o.customerRating && o.customerRating > 0);\n    if (ratedOrders.length === 0) return 0;\n    const totalRating = ratedOrders.reduce((sum, order) => sum + (order.customerRating || 0), 0);\n    return Math.round(totalRating / ratedOrders.length * 10) / 10;\n  }\n  static {\n    this.ɵfac = function CustomerPortalComponent_Factory(t) {\n      return new (t || CustomerPortalComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.DeliveryService), i0.ɵɵdirectiveInject(i4.RealTimeService), i0.ɵɵdirectiveInject(i5.AuthService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CustomerPortalComponent,\n      selectors: [[\"app-customer-portal\"]],\n      decls: 36,\n      vars: 13,\n      consts: [[1, \"customer-portal\"], [1, \"container-fluid\"], [1, \"portal-header\", \"bg-primary\", \"text-white\", \"py-3\", \"mb-4\"], [1, \"container\"], [1, \"row\", \"align-items-center\"], [1, \"col-md-4\"], [1, \"h4\", \"mb-0\"], [1, \"fa-solid\", \"fa-truck\", \"me-2\"], [1, \"col-md-4\", \"text-center\"], [1, \"input-group\", 2, \"max-width\", \"300px\", \"margin\", \"0 auto\"], [\"type\", \"text\", \"placeholder\", \"Num\\u00E9ro de suivi\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\", \"keyup.enter\"], [\"type\", \"button\", 1, \"btn\", \"btn-light\", 3, \"click\"], [1, \"fa-solid\", \"fa-search\"], [1, \"col-md-4\", \"text-md-end\"], [1, \"d-flex\", \"align-items-center\", \"justify-content-md-end\", \"gap-3\"], [\"class\", \"d-flex align-items-center\", 4, \"ngIf\"], [\"role\", \"group\", 1, \"btn-group\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-light\", \"btn-sm\", 3, \"click\"], [1, \"fa-solid\", \"fa-search\", \"me-1\"], [1, \"fa-solid\", \"fa-list\", \"me-1\"], [1, \"fa-solid\", \"fa-user\", \"me-1\"], [\"title\", \"Se d\\u00E9connecter\", 1, \"btn\", \"btn-outline-light\", \"btn-sm\", 3, \"click\"], [1, \"fa-solid\", \"fa-sign-out-alt\", \"me-1\"], [\"class\", \"text-center py-5\", 4, \"ngIf\"], [\"class\", \"alert alert-danger\", \"role\", \"alert\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"d-flex\", \"align-items-center\"], [1, \"fa-solid\", \"fa-user-circle\", \"fa-lg\", \"me-2\"], [1, \"small\"], [1, \"text-center\", \"py-5\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"mt-3\", \"text-muted\"], [\"role\", \"alert\", 1, \"alert\", \"alert-danger\"], [1, \"fa-solid\", \"fa-exclamation-triangle\", \"me-2\"], [1, \"card\", \"mb-4\"], [1, \"card-body\"], [1, \"card-title\", \"mb-4\"], [1, \"fa-solid\", \"fa-route\", \"me-2\"], [1, \"progress-tracker\"], [1, \"progress-line\"], [1, \"row\"], [\"class\", \"col-md-4\", 4, \"ngFor\", \"ngForOf\"], [1, \"row\", \"g-4\"], [1, \"col-lg-8\"], [1, \"card\"], [1, \"card-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"card-title\", \"mb-0\"], [1, \"fa-solid\", \"fa-info-circle\", \"me-2\"], [1, \"badge\"], [1, \"row\", \"g-3\"], [1, \"col-md-6\"], [1, \"form-label\", \"text-muted\"], [1, \"fw-bold\"], [1, \"col-12\"], [1, \"fa-solid\", \"fa-location-dot\", \"me-2\", \"text-primary\"], [1, \"fa-solid\", \"fa-user\", \"me-2\", \"text-info\"], [1, \"text-muted\", \"mb-3\"], [1, \"timeline\"], [1, \"timeline-item\"], [1, \"timeline-marker\", \"bg-success\"], [1, \"timeline-content\"], [1, \"mb-1\"], [1, \"text-muted\", \"small\", \"mb-0\"], [\"class\", \"timeline-item\", 4, \"ngIf\"], [\"class\", \"mt-4\", 4, \"ngIf\"], [1, \"col-lg-4\"], [1, \"card-body\", \"text-center\"], [1, \"fa-solid\", \"fa-clock\", \"fa-2x\", \"text-primary\", \"mb-3\"], [1, \"card-title\"], [1, \"h4\", \"text-primary\", \"mb-2\"], [1, \"card-header\"], [1, \"fa-solid\", \"fa-phone\", \"me-2\"], [1, \"mb-2\"], [\"href\", \"tel:+33123456789\", 1, \"text-decoration-none\"], [1, \"mb-0\"], [\"href\", \"mailto:<EMAIL>\", 1, \"text-decoration-none\"], [1, \"fa-solid\", \"fa-envelope\", \"me-2\"], [\"class\", \"card\", 4, \"ngIf\"], [1, \"progress-step\", \"text-center\"], [1, \"step-icon\"], [1, \"step-title\", \"mt-2\"], [1, \"timeline-marker\", \"bg-info\"], [1, \"mt-4\"], [1, \"text-muted\", \"mb-2\"], [1, \"alert\", \"alert-info\"], [1, \"fa-solid\", \"fa-note-sticky\", \"me-2\"], [1, \"fa-solid\", \"fa-star\", \"me-2\"], [\"class\", \"mb-3\", 4, \"ngIf\"], [1, \"mb-3\"], [1, \"rating\"], [\"class\", \"fa-solid fa-star\", 3, \"text-warning\", \"text-muted\", 4, \"ngFor\", \"ngForOf\"], [1, \"fa-solid\", \"fa-star\"], [1, \"text-muted\"], [1, \"btn\", \"btn-primary\", \"btn-sm\"], [1, \"fa-solid\", \"fa-search\", \"fa-3x\", \"text-muted\", \"mb-3\"], [1, \"fa-solid\", \"fa-list\", \"me-2\"], [1, \"d-flex\", \"gap-2\"], [1, \"form-select\", \"form-select-sm\", 3, \"ngModel\", \"ngModelChange\", \"change\"], [\"value\", \"all\"], [\"value\", \"pending\"], [\"value\", \"in-transit\"], [\"value\", \"delivered\"], [\"value\", \"cancelled\"], [1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\"], [1, \"fa-solid\", \"fa-refresh\", \"me-1\"], [\"class\", \"text-center py-4\", 4, \"ngIf\"], [\"class\", \"table-responsive\", 4, \"ngIf\"], [1, \"text-center\", \"py-4\"], [1, \"fa-solid\", \"fa-box-open\", \"fa-3x\", \"text-muted\", \"mb-3\"], [1, \"table-responsive\"], [1, \"table\", \"table-hover\"], [4, \"ngFor\", \"ngForOf\"], [1, \"fa-solid\", \"fa-location-dot\", \"me-1\", \"text-primary\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fa-solid\", \"fa-eye\", \"me-1\"], [1, \"btn\", \"btn-outline-info\", \"btn-sm\", 3, \"click\"], [1, \"fa-solid\", \"fa-user\", \"me-2\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"form-label\"], [\"type\", \"text\", \"formControlName\", \"firstName\", 1, \"form-control\"], [\"type\", \"text\", \"formControlName\", \"lastName\", 1, \"form-control\"], [\"type\", \"email\", \"formControlName\", \"email\", 1, \"form-control\"], [\"type\", \"tel\", \"formControlName\", \"phone\", 1, \"form-control\"], [\"type\", \"date\", \"formControlName\", \"dateOfBirth\", 1, \"form-control\"], [\"rows\", \"3\", \"formControlName\", \"address\", 1, \"form-control\"], [1, \"d-flex\", \"justify-content-between\"], [\"type\", \"button\", 1, \"btn\", \"btn-outline-secondary\", 3, \"click\"], [1, \"fa-solid\", \"fa-undo\", \"me-1\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"disabled\"], [1, \"fa-solid\", \"fa-save\", \"me-1\"], [1, \"fa-solid\", \"fa-cog\", \"me-2\"], [1, \"d-grid\", \"gap-2\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", 3, \"click\"], [1, \"fa-solid\", \"fa-key\", \"me-1\"], [1, \"fa-solid\", \"fa-bell\", \"me-1\"], [1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", 3, \"click\"], [1, \"fa-solid\", \"fa-download\", \"me-1\"], [1, \"fa-solid\", \"fa-chart-bar\", \"me-2\"], [1, \"row\", \"g-3\", \"text-center\"], [1, \"col-6\"], [1, \"border\", \"rounded\", \"p-2\"], [1, \"text-primary\", \"mb-1\"], [1, \"text-success\", \"mb-1\"], [1, \"text-info\", \"mb-1\"]],\n      template: function CustomerPortalComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"h1\", 6);\n          i0.ɵɵelement(7, \"i\", 7);\n          i0.ɵɵtext(8, \" Espace Client \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 8)(10, \"div\", 9)(11, \"input\", 10);\n          i0.ɵɵlistener(\"ngModelChange\", function CustomerPortalComponent_Template_input_ngModelChange_11_listener($event) {\n            return ctx.trackingId = $event;\n          })(\"keyup.enter\", function CustomerPortalComponent_Template_input_keyup_enter_11_listener() {\n            return ctx.trackDelivery();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function CustomerPortalComponent_Template_button_click_12_listener() {\n            return ctx.trackDelivery();\n          });\n          i0.ɵɵelement(13, \"i\", 12);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(14, \"div\", 13)(15, \"div\", 14);\n          i0.ɵɵtemplate(16, CustomerPortalComponent_div_16_Template, 4, 2, \"div\", 15);\n          i0.ɵɵelementStart(17, \"div\", 16)(18, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function CustomerPortalComponent_Template_button_click_18_listener() {\n            return ctx.setActiveTab(\"tracking\");\n          });\n          i0.ɵɵelement(19, \"i\", 18);\n          i0.ɵɵtext(20, \" Suivi \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function CustomerPortalComponent_Template_button_click_21_listener() {\n            return ctx.setActiveTab(\"orders\");\n          });\n          i0.ɵɵelement(22, \"i\", 19);\n          i0.ɵɵtext(23, \" Commandes \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function CustomerPortalComponent_Template_button_click_24_listener() {\n            return ctx.setActiveTab(\"profile\");\n          });\n          i0.ɵɵelement(25, \"i\", 20);\n          i0.ɵɵtext(26, \" Profil \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(27, \"button\", 21);\n          i0.ɵɵlistener(\"click\", function CustomerPortalComponent_Template_button_click_27_listener() {\n            return ctx.logout();\n          });\n          i0.ɵɵelement(28, \"i\", 22);\n          i0.ɵɵtext(29, \" D\\u00E9connexion \");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(30, \"div\", 3);\n          i0.ɵɵtemplate(31, CustomerPortalComponent_div_31_Template, 6, 0, \"div\", 23);\n          i0.ɵɵtemplate(32, CustomerPortalComponent_div_32_Template, 3, 1, \"div\", 24);\n          i0.ɵɵtemplate(33, CustomerPortalComponent_div_33_Template, 3, 2, \"div\", 25);\n          i0.ɵɵtemplate(34, CustomerPortalComponent_div_34_Template, 27, 4, \"div\", 25);\n          i0.ɵɵtemplate(35, CustomerPortalComponent_div_35_Template, 86, 7, \"div\", 25);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngModel\", ctx.trackingId);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentUser);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"active\", ctx.activeTab === \"tracking\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"active\", ctx.activeTab === \"orders\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"active\", ctx.activeTab === \"profile\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error && !ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"tracking\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"orders\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"profile\");\n        }\n      },\n      dependencies: [i6.NgForOf, i6.NgIf, i2.ɵNgNoValidate, i2.NgSelectOption, i2.ɵNgSelectMultipleOption, i2.DefaultValueAccessor, i2.SelectControlValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.NgModel, i2.FormGroupDirective, i2.FormControlName],\n      styles: [\".customer-portal[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  background-color: #f8f9fa;\\n  font-family: -apple-system, BlinkMacSystemFont, \\\"Segoe UI\\\", \\\"Roboto\\\", sans-serif;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .portal-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);\\n  box-shadow: 0 4px 20px rgba(0, 123, 255, 0.25);\\n  position: relative;\\n  overflow: hidden;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .portal-header[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 100 100\\\"><circle cx=\\\"20\\\" cy=\\\"20\\\" r=\\\"2\\\" fill=\\\"rgba(255,255,255,0.1)\\\"/><circle cx=\\\"80\\\" cy=\\\"40\\\" r=\\\"1\\\" fill=\\\"rgba(255,255,255,0.1)\\\"/><circle cx=\\\"40\\\" cy=\\\"80\\\" r=\\\"1.5\\\" fill=\\\"rgba(255,255,255,0.1)\\\"/></svg>');\\n  pointer-events: none;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .portal-header[_ngcontent-%COMP%]   .portal-content[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 1;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .portal-header[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 12px;\\n  padding: 4px;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.customer-portal[_ngcontent-%COMP%]   .portal-header[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  border: none;\\n  border-radius: 8px;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  font-weight: 500;\\n  padding: 8px 16px;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .portal-header[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn.active[_ngcontent-%COMP%] {\\n  background-color: rgba(255, 255, 255, 0.25);\\n  border-color: rgba(255, 255, 255, 0.4);\\n  color: white;\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n}\\n.customer-portal[_ngcontent-%COMP%]   .portal-header[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover:not(.active) {\\n  background-color: rgba(255, 255, 255, 0.15);\\n  transform: translateY(-1px);\\n}\\n.customer-portal[_ngcontent-%COMP%]   .portal-header[_ngcontent-%COMP%]   .search-input[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.15);\\n  border: 1px solid rgba(255, 255, 255, 0.3);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border-radius: 10px;\\n  color: white;\\n  transition: all 0.3s ease;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .portal-header[_ngcontent-%COMP%]   .search-input[_ngcontent-%COMP%]::placeholder {\\n  color: rgba(255, 255, 255, 0.7);\\n}\\n.customer-portal[_ngcontent-%COMP%]   .portal-header[_ngcontent-%COMP%]   .search-input[_ngcontent-%COMP%]:focus {\\n  background: rgba(255, 255, 255, 0.25);\\n  border-color: rgba(255, 255, 255, 0.5);\\n  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);\\n  outline: none;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .progress-tracker[_ngcontent-%COMP%] {\\n  position: relative;\\n  padding: 3rem 0;\\n  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);\\n  border-radius: 16px;\\n  margin: 2rem 0;\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06);\\n}\\n.customer-portal[_ngcontent-%COMP%]   .progress-tracker[_ngcontent-%COMP%]   .progress-line[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 12.5%;\\n  right: 12.5%;\\n  height: 3px;\\n  background: linear-gradient(90deg, #e9ecef 0%, #dee2e6 100%);\\n  border-radius: 2px;\\n  z-index: 1;\\n  transform: translateY(-50%);\\n}\\n.customer-portal[_ngcontent-%COMP%]   .progress-tracker[_ngcontent-%COMP%]   .progress-line[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  height: 100%;\\n  width: 66.66%;\\n  background: linear-gradient(90deg, #28a745 0%, #20c997 100%);\\n  border-radius: 2px;\\n  transition: width 0.8s ease-in-out;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .progress-tracker[_ngcontent-%COMP%]   .progress-step[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 2;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .progress-tracker[_ngcontent-%COMP%]   .progress-step[_ngcontent-%COMP%]   .step-icon[_ngcontent-%COMP%] {\\n  width: 70px;\\n  height: 70px;\\n  border-radius: 50%;\\n  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);\\n  color: #6c757d;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin: 0 auto;\\n  font-size: 1.75rem;\\n  font-weight: 600;\\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\\n  border: 4px solid #e9ecef;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\\n}\\n.customer-portal[_ngcontent-%COMP%]   .progress-tracker[_ngcontent-%COMP%]   .progress-step[_ngcontent-%COMP%]   .step-icon.active[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);\\n  color: white;\\n  border-color: #007bff;\\n  box-shadow: 0 0 0 6px rgba(0, 123, 255, 0.2), 0 8px 32px rgba(0, 123, 255, 0.3);\\n  animation: _ngcontent-%COMP%_pulse-glow 2s infinite;\\n  transform: scale(1.1);\\n}\\n.customer-portal[_ngcontent-%COMP%]   .progress-tracker[_ngcontent-%COMP%]   .progress-step[_ngcontent-%COMP%]   .step-icon.completed[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);\\n  color: white;\\n  border-color: #28a745;\\n  box-shadow: 0 4px 20px rgba(40, 167, 69, 0.3);\\n  transform: scale(1.05);\\n}\\n.customer-portal[_ngcontent-%COMP%]   .progress-tracker[_ngcontent-%COMP%]   .progress-step[_ngcontent-%COMP%]   .step-icon[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.15);\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);\\n}\\n.customer-portal[_ngcontent-%COMP%]   .progress-tracker[_ngcontent-%COMP%]   .progress-step[_ngcontent-%COMP%]   .step-title[_ngcontent-%COMP%] {\\n  font-size: 0.95rem;\\n  font-weight: 600;\\n  margin-top: 0.75rem;\\n  color: #495057;\\n  transition: color 0.3s ease;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .progress-tracker[_ngcontent-%COMP%]   .progress-step[_ngcontent-%COMP%]   .step-title.active[_ngcontent-%COMP%] {\\n  color: #007bff;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .progress-tracker[_ngcontent-%COMP%]   .progress-step[_ngcontent-%COMP%]   .step-title.completed[_ngcontent-%COMP%] {\\n  color: #28a745;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .progress-tracker[_ngcontent-%COMP%]   .progress-step[_ngcontent-%COMP%]   .step-description[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #6c757d;\\n  margin-top: 0.25rem;\\n  opacity: 0.8;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .timeline[_ngcontent-%COMP%] {\\n  position: relative;\\n  padding-left: 2.5rem;\\n  background: white;\\n  border-radius: 12px;\\n  padding: 2rem 2rem 2rem 3rem;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\\n}\\n.customer-portal[_ngcontent-%COMP%]   .timeline[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  left: 1rem;\\n  top: 2rem;\\n  bottom: 2rem;\\n  width: 3px;\\n  background: linear-gradient(180deg, #007bff 0%, #e9ecef 100%);\\n  border-radius: 2px;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .timeline[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%] {\\n  position: relative;\\n  margin-bottom: 2rem;\\n  padding: 1rem;\\n  background: #f8f9fa;\\n  border-radius: 8px;\\n  transition: all 0.3s ease;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .timeline[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]:hover {\\n  background: #ffffff;\\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);\\n  transform: translateX(4px);\\n}\\n.customer-portal[_ngcontent-%COMP%]   .timeline[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-marker[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: -2.5rem;\\n  top: 1.5rem;\\n  width: 14px;\\n  height: 14px;\\n  border-radius: 50%;\\n  border: 3px solid white;\\n  background: #007bff;\\n  box-shadow: 0 0 0 3px #e9ecef;\\n  transition: all 0.3s ease;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .timeline[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-marker.completed[_ngcontent-%COMP%] {\\n  background: #28a745;\\n  box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.2);\\n}\\n.customer-portal[_ngcontent-%COMP%]   .timeline[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-marker.active[_ngcontent-%COMP%] {\\n  background: #007bff;\\n  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.3);\\n  animation: _ngcontent-%COMP%_pulse-marker 2s infinite;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .timeline[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-content[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  margin-bottom: 0.5rem;\\n  font-weight: 600;\\n  color: #495057;\\n  font-size: 1rem;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .timeline[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n  font-size: 0.875rem;\\n  color: #6c757d;\\n  line-height: 1.5;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .timeline[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-content[_ngcontent-%COMP%]   .timeline-meta[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: #adb5bd;\\n  margin-top: 0.5rem;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .rating[_ngcontent-%COMP%]   .fa-star[_ngcontent-%COMP%], .customer-portal[_ngcontent-%COMP%]   .rating[_ngcontent-%COMP%]   .star-icon[_ngcontent-%COMP%] {\\n  font-size: 1.3rem;\\n  margin-right: 0.25rem;\\n  color: #ffc107;\\n  transition: all 0.2s ease;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .rating[_ngcontent-%COMP%]   .fa-star[_ngcontent-%COMP%]:hover, .customer-portal[_ngcontent-%COMP%]   .rating[_ngcontent-%COMP%]   .star-icon[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.2);\\n  filter: brightness(1.1);\\n}\\n.customer-portal[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%] {\\n  border: none;\\n  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.06);\\n  border-radius: 16px;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  background: white;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 8px 40px rgba(0, 0, 0, 0.12);\\n  transform: translateY(-2px);\\n}\\n.customer-portal[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);\\n  border-bottom: 1px solid rgba(0, 0, 0, 0.05);\\n  border-radius: 16px 16px 0 0 !important;\\n  padding: 1.5rem 2rem;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #495057;\\n  font-size: 1.25rem;\\n  margin: 0;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-subtitle[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-size: 0.9rem;\\n  margin-top: 0.25rem;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%] {\\n  padding: 2rem;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   .card-footer[_ngcontent-%COMP%] {\\n  background: rgba(248, 249, 250, 0.5);\\n  border-top: 1px solid rgba(0, 0, 0, 0.05);\\n  border-radius: 0 0 16px 16px;\\n  padding: 1rem 2rem;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n  padding: 0.5rem 0.875rem;\\n  border-radius: 8px;\\n  letter-spacing: 0.025em;\\n  text-transform: uppercase;\\n  transition: all 0.2s ease;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .badge.badge-primary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);\\n  color: white;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .badge.badge-success[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);\\n  color: white;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .badge.badge-warning[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);\\n  color: #212529;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .badge.badge-danger[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);\\n  color: white;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\\n}\\n.customer-portal[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%] {\\n  border: none;\\n  border-radius: 12px;\\n  padding: 1.25rem 1.5rem;\\n  border-left: 4px solid;\\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);\\n}\\n.customer-portal[_ngcontent-%COMP%]   .alert.alert-primary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, rgba(0, 123, 255, 0.1) 0%, rgba(0, 123, 255, 0.05) 100%);\\n  border-left-color: #007bff;\\n  color: #004085;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .alert.alert-success[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(40, 167, 69, 0.05) 100%);\\n  border-left-color: #28a745;\\n  color: #155724;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .alert.alert-warning[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 193, 7, 0.05) 100%);\\n  border-left-color: #ffc107;\\n  color: #856404;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .alert.alert-danger[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(220, 53, 69, 0.05) 100%);\\n  border-left-color: #dc3545;\\n  color: #721c24;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  font-weight: 500;\\n  padding: 0.625rem 1.25rem;\\n  font-size: 0.875rem;\\n  line-height: 1.5;\\n  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\\n  border: 2px solid transparent;\\n  text-decoration: none;\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 0.5rem;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);\\n}\\n.customer-portal[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:active {\\n  transform: translateY(0);\\n}\\n.customer-portal[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);\\n  color: white;\\n  border-color: #007bff;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .btn.btn-primary[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #0056b3 0%, #004085 100%);\\n  border-color: #0056b3;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .btn.btn-success[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);\\n  color: white;\\n  border-color: #28a745;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .btn.btn-success[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);\\n  border-color: #20c997;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .btn.btn-outline-primary[_ngcontent-%COMP%] {\\n  background: transparent;\\n  color: #007bff;\\n  border-color: #007bff;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .btn.btn-outline-primary[_ngcontent-%COMP%]:hover {\\n  background: #007bff;\\n  color: white;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  border: 2px solid #e9ecef;\\n  padding: 0.75rem 1rem;\\n  font-size: 0.875rem;\\n  transition: all 0.3s ease;\\n  background: white;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus {\\n  border-color: #007bff;\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n  outline: none;\\n  background: #ffffff;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]::placeholder {\\n  color: #adb5bd;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:hover {\\n  border-color: #ced4da;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  border-radius: 0 8px 8px 0;\\n  border-left: none;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  border-radius: 8px 0 0 8px;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .stats-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border: 2px solid #e9ecef;\\n  border-radius: 12px;\\n  padding: 1.5rem;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n  overflow: hidden;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .stats-card[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 4px;\\n  background: linear-gradient(90deg, #007bff 0%, #20c997 100%);\\n  transform: scaleX(0);\\n  transition: transform 0.3s ease;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .stats-card[_ngcontent-%COMP%]:hover {\\n  border-color: #007bff;\\n  background: linear-gradient(135deg, rgba(0, 123, 255, 0.02) 0%, rgba(32, 201, 151, 0.02) 100%);\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 32px rgba(0, 123, 255, 0.15);\\n}\\n.customer-portal[_ngcontent-%COMP%]   .stats-card[_ngcontent-%COMP%]:hover::before {\\n  transform: scaleX(1);\\n}\\n.customer-portal[_ngcontent-%COMP%]   .stats-card[_ngcontent-%COMP%]   .stats-icon[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  border-radius: 12px;\\n  background: linear-gradient(135deg, #007bff 0%, #20c997 100%);\\n  color: white;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 1.5rem;\\n  margin-bottom: 1rem;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .stats-card[_ngcontent-%COMP%]   .stats-value[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  font-weight: 700;\\n  color: #495057;\\n  margin-bottom: 0.25rem;\\n}\\n.customer-portal[_ngcontent-%COMP%]   .stats-card[_ngcontent-%COMP%]   .stats-label[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n}\\n\\n.table-responsive[_ngcontent-%COMP%] {\\n  border-radius: 12px;\\n  overflow: hidden;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\\n  background: white;\\n}\\n\\n.table[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n.table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);\\n  border-top: none;\\n  border-bottom: 2px solid #e9ecef;\\n  font-weight: 600;\\n  color: #495057;\\n  font-size: 0.875rem;\\n  text-transform: uppercase;\\n  letter-spacing: 0.025em;\\n  padding: 1rem;\\n}\\n.table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n  border-bottom: 1px solid #f1f3f4;\\n  vertical-align: middle;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(135deg, rgba(0, 123, 255, 0.03) 0%, rgba(0, 123, 255, 0.01) 100%);\\n  transform: scale(1.005);\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:last-child   td[_ngcontent-%COMP%] {\\n  border-bottom: none;\\n}\\n\\n.form-label[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #495057;\\n  margin-bottom: 0.5rem;\\n  font-size: 0.875rem;\\n  text-transform: uppercase;\\n  letter-spacing: 0.025em;\\n}\\n\\n.form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n}\\n.form-group[_ngcontent-%COMP%]   .form-text[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-size: 0.8rem;\\n  margin-top: 0.25rem;\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse-glow {\\n  0% {\\n    box-shadow: 0 0 0 6px rgba(0, 123, 255, 0.2), 0 8px 32px rgba(0, 123, 255, 0.3);\\n  }\\n  50% {\\n    box-shadow: 0 0 0 10px rgba(0, 123, 255, 0.1), 0 8px 32px rgba(0, 123, 255, 0.4);\\n  }\\n  100% {\\n    box-shadow: 0 0 0 6px rgba(0, 123, 255, 0.2), 0 8px 32px rgba(0, 123, 255, 0.3);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_pulse-marker {\\n  0% {\\n    transform: scale(1);\\n    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.3);\\n  }\\n  50% {\\n    transform: scale(1.1);\\n    box-shadow: 0 0 0 6px rgba(0, 123, 255, 0.2);\\n  }\\n  100% {\\n    transform: scale(1);\\n    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.3);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_fadeInUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(30px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.fade-in-up[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeInUp 0.6s ease-out;\\n}\\n\\n@media (max-width: 768px) {\\n  .customer-portal[_ngcontent-%COMP%]   .portal-header[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    width: 100%;\\n  }\\n  .customer-portal[_ngcontent-%COMP%]   .portal-header[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n    width: 100%;\\n    margin-bottom: 0.25rem;\\n    justify-content: center;\\n  }\\n  .customer-portal[_ngcontent-%COMP%]   .portal-header[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:last-child {\\n    margin-bottom: 0;\\n  }\\n  .customer-portal[_ngcontent-%COMP%]   .portal-header[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%] {\\n    margin-top: 1rem;\\n    max-width: 100% !important;\\n  }\\n  .customer-portal[_ngcontent-%COMP%]   .progress-tracker[_ngcontent-%COMP%] {\\n    padding: 2rem 1rem;\\n  }\\n  .customer-portal[_ngcontent-%COMP%]   .progress-tracker[_ngcontent-%COMP%]   .progress-line[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .customer-portal[_ngcontent-%COMP%]   .progress-tracker[_ngcontent-%COMP%]   .progress-step[_ngcontent-%COMP%] {\\n    margin-bottom: 2rem;\\n  }\\n  .customer-portal[_ngcontent-%COMP%]   .progress-tracker[_ngcontent-%COMP%]   .progress-step[_ngcontent-%COMP%]:last-child {\\n    margin-bottom: 0;\\n  }\\n  .customer-portal[_ngcontent-%COMP%]   .progress-tracker[_ngcontent-%COMP%]   .progress-step[_ngcontent-%COMP%]   .step-icon[_ngcontent-%COMP%] {\\n    width: 60px;\\n    height: 60px;\\n    font-size: 1.5rem;\\n  }\\n  .customer-portal[_ngcontent-%COMP%]   .timeline[_ngcontent-%COMP%] {\\n    padding-left: 2rem;\\n  }\\n  .customer-portal[_ngcontent-%COMP%]   .timeline[_ngcontent-%COMP%]   .timeline-item[_ngcontent-%COMP%]   .timeline-marker[_ngcontent-%COMP%] {\\n    left: -2rem;\\n  }\\n  .customer-portal[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n  }\\n  .customer-portal[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%], .customer-portal[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%], .customer-portal[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   .card-footer[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .customer-portal[_ngcontent-%COMP%]   .stats-card[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n    padding: 1rem;\\n  }\\n  .customer-portal[_ngcontent-%COMP%]   .stats-card[_ngcontent-%COMP%]   .stats-value[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n  .customer-portal[_ngcontent-%COMP%]   .table-responsive[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n  .customer-portal[_ngcontent-%COMP%]   .table-responsive[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .customer-portal[_ngcontent-%COMP%]   .table-responsive[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n    padding: 0.5rem;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .customer-portal[_ngcontent-%COMP%]   .portal-header[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n  .customer-portal[_ngcontent-%COMP%]   .portal-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 1.75rem;\\n  }\\n  .customer-portal[_ngcontent-%COMP%]   .progress-tracker[_ngcontent-%COMP%]   .step-icon[_ngcontent-%COMP%] {\\n    width: 50px;\\n    height: 50px;\\n    font-size: 1.25rem;\\n  }\\n  .customer-portal[_ngcontent-%COMP%]   .progress-tracker[_ngcontent-%COMP%]   .step-title[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n}\\n@media (prefers-color-scheme: dark) {\\n  .customer-portal[_ngcontent-%COMP%] {\\n    background-color: #1a1a1a;\\n  }\\n  .customer-portal[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%] {\\n    background: #2d2d2d;\\n    color: #e9ecef;\\n  }\\n  .customer-portal[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%] {\\n    background: linear-gradient(135deg, #2d2d2d 0%, #3a3a3a 100%);\\n    border-bottom-color: rgba(255, 255, 255, 0.1);\\n  }\\n  .customer-portal[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%] {\\n    color: #e9ecef;\\n  }\\n  .customer-portal[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n    background: #3a3a3a;\\n    border-color: #4a4a4a;\\n    color: #e9ecef;\\n  }\\n  .customer-portal[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus {\\n    border-color: #007bff;\\n    background: #4a4a4a;\\n  }\\n  .customer-portal[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n    background: linear-gradient(135deg, #2d2d2d 0%, #3a3a3a 100%);\\n    color: #e9ecef;\\n    border-bottom-color: #4a4a4a;\\n  }\\n  .customer-portal[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n    border-bottom-color: #3a3a3a;\\n    color: #e9ecef;\\n  }\\n  .customer-portal[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n    background: linear-gradient(135deg, rgba(0, 123, 255, 0.1) 0%, rgba(0, 123, 255, 0.05) 100%);\\n  }\\n  .customer-portal[_ngcontent-%COMP%]   .stats-card[_ngcontent-%COMP%] {\\n    background: #2d2d2d;\\n    border-color: #4a4a4a;\\n  }\\n  .customer-portal[_ngcontent-%COMP%]   .stats-card[_ngcontent-%COMP%]:hover {\\n    background: linear-gradient(135deg, rgba(0, 123, 255, 0.05) 0%, rgba(32, 201, 151, 0.05) 100%);\\n  }\\n  .customer-portal[_ngcontent-%COMP%]   .stats-card[_ngcontent-%COMP%]   .stats-value[_ngcontent-%COMP%] {\\n    color: #e9ecef;\\n  }\\n  .customer-portal[_ngcontent-%COMP%]   .stats-card[_ngcontent-%COMP%]   .stats-label[_ngcontent-%COMP%] {\\n    color: #adb5bd;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "DeliveryStatus", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate2", "ctx_r0", "currentUser", "firstName", "lastName", "ɵɵtextInterpolate1", "ctx_r2", "error", "ɵɵclassProp", "ctx_r8", "isStatusCompleted", "i_r14", "isStatusActive", "ɵɵclassMap", "step_r13", "icon", "label", "ɵɵtextInterpolate", "ctx_r9", "formatDate", "delivery", "pickupTime", "ctx_r10", "actualDeliveryTime", "ctx_r11", "notes", "star_r19", "ctx_r18", "customerRating", "ɵɵtemplate", "CustomerPortalComponent_div_33_div_1_div_92_div_6_i_4_Template", "ɵɵproperty", "ɵɵpureFunction0", "_c0", "ctx_r16", "customerFeedback", "CustomerPortalComponent_div_33_div_1_div_92_div_6_Template", "CustomerPortalComponent_div_33_div_1_div_92_div_7_Template", "CustomerPortalComponent_div_33_div_1_div_92_div_8_Template", "ctx_r12", "CustomerPortalComponent_div_33_div_1_div_9_Template", "CustomerPortalComponent_div_33_div_1_div_59_Template", "CustomerPortalComponent_div_33_div_1_div_60_Template", "CustomerPortalComponent_div_33_div_1_div_61_Template", "CustomerPortalComponent_div_33_div_1_div_92_Template", "ctx_r6", "statusSteps", "getStatusClass", "status", "getStatusText", "orderId", "customerName", "address", "<PERSON><PERSON><PERSON>", "priority", "createdAt", "getEstimatedTimeRemaining", "estimatedDeliveryTime", "CustomerPortalComponent_div_33_div_1_Template", "CustomerPortalComponent_div_33_div_2_Template", "ctx_r3", "loading", "trackingId", "ɵɵlistener", "CustomerPortalComponent_div_34_div_26_tr_15_Template_button_click_16_listener", "restoredCtx", "ɵɵrestoreView", "_r26", "order_r24", "$implicit", "ctx_r25", "ɵɵnextContext", "ɵɵresetView", "viewOrderDetails", "CustomerPortalComponent_div_34_div_26_tr_15_Template_button_click_19_listener", "ctx_r27", "trackOrder", "ctx_r23", "CustomerPortalComponent_div_34_div_26_tr_15_Template", "ctx_r22", "filteredOrders", "CustomerPortalComponent_div_34_Template_select_ngModelChange_9_listener", "$event", "_r29", "ctx_r28", "orderFilter", "CustomerPortalComponent_div_34_Template_select_change_9_listener", "ctx_r30", "filterOrders", "CustomerPortalComponent_div_34_Template_button_click_20_listener", "ctx_r31", "loadOrders", "CustomerPortalComponent_div_34_div_24_Template", "CustomerPortalComponent_div_34_div_25_Template", "CustomerPortalComponent_div_34_div_26_Template", "ctx_r4", "loadingOrders", "orders", "length", "CustomerPortalComponent_div_35_Template_form_ngSubmit_9_listener", "_r35", "ctx_r34", "updateProfile", "CustomerPortalComponent_div_35_Template_button_click_37_listener", "ctx_r36", "resetProfile", "CustomerPortalComponent_div_35_span_42_Template", "CustomerPortalComponent_div_35_span_43_Template", "CustomerPortalComponent_div_35_Template_button_click_52_listener", "ctx_r37", "changePassword", "CustomerPortalComponent_div_35_Template_button_click_55_listener", "ctx_r38", "manageNotifications", "CustomerPortalComponent_div_35_Template_button_click_58_listener", "ctx_r39", "downloadData", "ctx_r5", "profileForm", "valid", "updatingProfile", "userStats", "totalOrders", "deliveredOrders", "averageRating", "CustomerPortalComponent", "constructor", "route", "router", "formBuilder", "deliveryService", "realTimeService", "authService", "activeTab", "subscriptions", "key", "Pending", "InTransit", "Delivered", "group", "required", "email", "phone", "dateOfBirth", "ngOnInit", "getCurrentUser", "initializeProfileForm", "loadUserStats", "params", "subscribe", "setActiveTab", "loadDelivery", "setupRealTimeUpdates", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "stopConnection", "_this", "_asyncToGenerator", "deliveryByOrderId", "getDeliveryByOrderId", "to<PERSON>romise", "deliveryById", "getDelivery", "console", "startConnection", "then", "log", "push", "deliveryUpdates$", "updatedDelivery", "id", "catch", "err", "trackDelivery", "trim", "getStatusIndex", "findIndex", "step", "stepIndex", "currentIndex", "Delayed", "Cancelled", "date", "d", "Date", "toLocaleDateString", "day", "month", "year", "hour", "minute", "now", "estimated", "diffMs", "getTime", "diffHours", "Math", "floor", "diffMinutes", "tab", "logout", "next", "setTimeout", "navigate", "_this2", "getDeliveriesByCustomer", "filter", "order", "patchValue", "_this3", "formData", "value", "_this4", "o", "calculateAverageRating", "ratedOrders", "totalRating", "reduce", "sum", "round", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "FormBuilder", "i3", "DeliveryService", "i4", "RealTimeService", "i5", "AuthService", "selectors", "decls", "vars", "consts", "template", "CustomerPortalComponent_Template", "rf", "ctx", "CustomerPortalComponent_Template_input_ngModelChange_11_listener", "CustomerPortalComponent_Template_input_keyup_enter_11_listener", "CustomerPortalComponent_Template_button_click_12_listener", "CustomerPortalComponent_div_16_Template", "CustomerPortalComponent_Template_button_click_18_listener", "CustomerPortalComponent_Template_button_click_21_listener", "CustomerPortalComponent_Template_button_click_24_listener", "CustomerPortalComponent_Template_button_click_27_listener", "CustomerPortalComponent_div_31_Template", "CustomerPortalComponent_div_32_Template", "CustomerPortalComponent_div_33_Template", "CustomerPortalComponent_div_34_Template", "CustomerPortalComponent_div_35_Template"], "sources": ["C:\\Users\\<USER>\\delivery-dash-optimiser\\frontend\\src\\app\\pages\\customer-portal\\customer-portal.component.ts", "C:\\Users\\<USER>\\delivery-dash-optimiser\\frontend\\src\\app\\pages\\customer-portal\\customer-portal.component.html"], "sourcesContent": ["import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Subscription } from 'rxjs';\nimport { DeliveryService } from '../../core/services/delivery.service';\nimport { RealTimeService } from '../../core/services/real-time.service';\nimport { AuthService } from '../../core/services/auth.service';\nimport { Delivery, DeliveryStatus } from '../../core/models/delivery.model';\nimport { User } from '../../core/models/user.model';\n\n@Component({\n  selector: 'app-customer-portal',\n  templateUrl: './customer-portal.component.html',\n  styleUrls: ['./customer-portal.component.scss']\n})\nexport class CustomerPortalComponent implements OnInit, OnDestroy {\n  // Tab management\n  activeTab = 'tracking';\n\n  // User data\n  currentUser: User | null = null;\n\n  // Tracking tab\n  delivery: Delivery | null = null;\n  loading = true;\n  error = '';\n  trackingId = '';\n\n  // Orders tab\n  orders: Delivery[] = [];\n  filteredOrders: Delivery[] = [];\n  loadingOrders = false;\n  orderFilter = 'all';\n\n  // Profile tab\n  profileForm: FormGroup;\n  updatingProfile = false;\n  userStats = {\n    totalOrders: 0,\n    deliveredOrders: 0,\n    averageRating: 0\n  };\n\n  // Real-time updates\n  private subscriptions: Subscription[] = [];\n\n  // Status tracking\n  statusSteps = [\n    { key: DeliveryStatus.Pending, label: 'Commande confirmée', icon: 'fa-check-circle' },\n    { key: DeliveryStatus.InTransit, label: 'En cours de livraison', icon: 'fa-truck' },\n    { key: DeliveryStatus.Delivered, label: 'Livré', icon: 'fa-box-check' }\n  ];\n\n  constructor(\n    private route: ActivatedRoute,\n    private router: Router,\n    private formBuilder: FormBuilder,\n    private deliveryService: DeliveryService,\n    private realTimeService: RealTimeService,\n    private authService: AuthService\n  ) {\n    this.profileForm = this.formBuilder.group({\n      firstName: ['', Validators.required],\n      lastName: ['', Validators.required],\n      email: ['', [Validators.required, Validators.email]],\n      phone: [''],\n      dateOfBirth: [''],\n      address: ['']\n    });\n  }\n\n  ngOnInit(): void {\n    // Load current user\n    this.currentUser = this.authService.getCurrentUser();\n    if (this.currentUser) {\n      this.initializeProfileForm();\n      this.loadUserStats();\n    }\n\n    this.route.params.subscribe(params => {\n      this.trackingId = params['trackingId'] || '';\n      if (this.trackingId) {\n        this.setActiveTab('tracking');\n        this.loadDelivery();\n        this.setupRealTimeUpdates();\n      } else {\n        // No tracking ID provided, stop loading and show search form\n        this.loading = false;\n      }\n    });\n\n    // Load orders if on orders tab\n    if (this.activeTab === 'orders') {\n      this.loadOrders();\n    }\n  }\n\n  ngOnDestroy(): void {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.realTimeService.stopConnection();\n  }\n\n  private async loadDelivery(): Promise<void> {\n    try {\n      this.loading = true;\n      this.error = '';\n\n      // Try to find delivery by order ID or delivery ID\n      const deliveryByOrderId = await this.deliveryService.getDeliveryByOrderId(this.trackingId).toPromise();\n      this.delivery = deliveryByOrderId || null;\n\n      if (!this.delivery) {\n        const deliveryById = await this.deliveryService.getDelivery(this.trackingId).toPromise();\n        this.delivery = deliveryById || null;\n      }\n\n      if (!this.delivery) {\n        this.error = 'Livraison non trouvée. Vérifiez votre numéro de suivi.';\n      }\n    } catch (error) {\n      console.error('Error loading delivery:', error);\n      this.error = 'Erreur lors du chargement des informations de livraison.';\n    } finally {\n      this.loading = false;\n    }\n  }\n\n  private setupRealTimeUpdates(): void {\n    this.realTimeService.startConnection()\n      .then(() => {\n        console.log('Connected to real-time tracking');\n\n        // Subscribe to delivery updates\n        this.subscriptions.push(\n          this.realTimeService.deliveryUpdates$.subscribe(updatedDelivery => {\n            if (updatedDelivery && this.delivery && updatedDelivery.id === this.delivery.id) {\n              this.delivery = { ...this.delivery, ...updatedDelivery };\n            }\n          })\n        );\n      })\n      .catch(err => {\n        console.error('Error connecting to real-time tracking', err);\n      });\n  }\n\n  trackDelivery(): void {\n    if (this.trackingId.trim()) {\n      this.error = ''; // Clear any previous errors\n      this.loadDelivery();\n      this.setupRealTimeUpdates();\n    }\n  }\n\n  getStatusIndex(status: DeliveryStatus): number {\n    return this.statusSteps.findIndex(step => step.key === status);\n  }\n\n  isStatusCompleted(stepIndex: number): boolean {\n    if (!this.delivery) return false;\n    const currentIndex = this.getStatusIndex(this.delivery.status);\n    return stepIndex <= currentIndex;\n  }\n\n  isStatusActive(stepIndex: number): boolean {\n    if (!this.delivery) return false;\n    const currentIndex = this.getStatusIndex(this.delivery.status);\n    return stepIndex === currentIndex;\n  }\n\n  getStatusText(status: DeliveryStatus): string {\n    switch (status) {\n      case DeliveryStatus.Pending:\n        return 'En attente';\n      case DeliveryStatus.InTransit:\n        return 'En cours';\n      case DeliveryStatus.Delivered:\n        return 'Livré';\n      case DeliveryStatus.Delayed:\n        return 'Retardé';\n      case DeliveryStatus.Cancelled:\n        return 'Annulé';\n      default:\n        return 'Inconnu';\n    }\n  }\n\n  getStatusClass(status: DeliveryStatus): string {\n    switch (status) {\n      case DeliveryStatus.Pending:\n        return 'warning';\n      case DeliveryStatus.InTransit:\n        return 'info';\n      case DeliveryStatus.Delivered:\n        return 'success';\n      case DeliveryStatus.Delayed:\n        return 'danger';\n      case DeliveryStatus.Cancelled:\n        return 'secondary';\n      default:\n        return 'secondary';\n    }\n  }\n\n  formatDate(date: Date | string): string {\n    if (!date) return 'Non défini';\n    const d = new Date(date);\n    return d.toLocaleDateString('fr-FR', {\n      day: '2-digit',\n      month: '2-digit',\n      year: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n\n  getEstimatedTimeRemaining(): string {\n    if (!this.delivery || !this.delivery.estimatedDeliveryTime) {\n      return 'Non disponible';\n    }\n\n    const now = new Date();\n    const estimated = new Date(this.delivery.estimatedDeliveryTime);\n    const diffMs = estimated.getTime() - now.getTime();\n\n    if (diffMs <= 0) {\n      return 'Livraison prévue';\n    }\n\n    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));\n    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));\n\n    if (diffHours > 0) {\n      return `${diffHours}h ${diffMinutes}min`;\n    } else {\n      return `${diffMinutes}min`;\n    }\n  }\n\n  // Tab management\n  setActiveTab(tab: string): void {\n    this.activeTab = tab;\n\n    if (tab === 'orders' && this.orders.length === 0) {\n      this.loadOrders();\n    }\n  }\n\n  // Logout functionality\n  logout(): void {\n    this.authService.logout().subscribe({\n      next: () => {\n        // Small delay to ensure authentication state is cleared\n        setTimeout(() => {\n          this.router.navigate(['/login']);\n        }, 100);\n      },\n      error: (error) => {\n        console.error('Logout error:', error);\n        // Navigate anyway since local storage is already cleared\n        setTimeout(() => {\n          this.router.navigate(['/login']);\n        }, 100);\n      }\n    });\n  }\n\n  // Orders management\n  async loadOrders(): Promise<void> {\n    if (!this.currentUser) return;\n\n    try {\n      this.loadingOrders = true;\n      // Get all deliveries for the current user\n      this.orders = await this.deliveryService.getDeliveriesByCustomer(this.currentUser.id).toPromise() || [];\n      this.filterOrders();\n    } catch (error) {\n      console.error('Error loading orders:', error);\n    } finally {\n      this.loadingOrders = false;\n    }\n  }\n\n  filterOrders(): void {\n    if (this.orderFilter === 'all') {\n      this.filteredOrders = [...this.orders];\n    } else {\n      this.filteredOrders = this.orders.filter(order => {\n        switch (this.orderFilter) {\n          case 'pending':\n            return order.status === DeliveryStatus.Pending;\n          case 'in-transit':\n            return order.status === DeliveryStatus.InTransit;\n          case 'delivered':\n            return order.status === DeliveryStatus.Delivered;\n          case 'cancelled':\n            return order.status === DeliveryStatus.Cancelled;\n          default:\n            return true;\n        }\n      });\n    }\n  }\n\n  viewOrderDetails(order: Delivery): void {\n    // Navigate to tracking with this order\n    this.trackingId = order.orderId;\n    this.setActiveTab('tracking');\n    this.trackDelivery();\n  }\n\n  trackOrder(orderId: string): void {\n    this.trackingId = orderId;\n    this.setActiveTab('tracking');\n    this.trackDelivery();\n  }\n\n  // Profile management\n  initializeProfileForm(): void {\n    if (this.currentUser) {\n      this.profileForm.patchValue({\n        firstName: this.currentUser.firstName,\n        lastName: this.currentUser.lastName,\n        email: this.currentUser.email,\n        phone: this.currentUser.phone || '',\n        dateOfBirth: this.currentUser.dateOfBirth || '',\n        address: this.currentUser.address || ''\n      });\n    }\n  }\n\n  async updateProfile(): Promise<void> {\n    if (!this.profileForm.valid || !this.currentUser) return;\n\n    try {\n      this.updatingProfile = true;\n      const formData = this.profileForm.value;\n\n      // Update user profile\n      await this.authService.updateProfile({\n        ...this.currentUser,\n        ...formData\n      }).toPromise();\n\n      // Update local user data\n      this.currentUser = { ...this.currentUser, ...formData };\n\n      // Show success message (you can implement a toast service)\n      console.log('Profile updated successfully');\n    } catch (error) {\n      console.error('Error updating profile:', error);\n    } finally {\n      this.updatingProfile = false;\n    }\n  }\n\n  resetProfile(): void {\n    this.initializeProfileForm();\n  }\n\n  // Account settings\n  changePassword(): void {\n    // Implement password change modal/dialog\n    console.log('Change password clicked');\n  }\n\n  manageNotifications(): void {\n    // Implement notification settings\n    console.log('Manage notifications clicked');\n  }\n\n  downloadData(): void {\n    // Implement data download\n    console.log('Download data clicked');\n  }\n\n  // User statistics\n  async loadUserStats(): Promise<void> {\n    if (!this.currentUser) return;\n\n    try {\n      // Load user statistics\n      const orders = await this.deliveryService.getDeliveriesByCustomer(this.currentUser.id).toPromise() || [];\n\n      this.userStats = {\n        totalOrders: orders.length,\n        deliveredOrders: orders.filter(o => o.status === DeliveryStatus.Delivered).length,\n        averageRating: this.calculateAverageRating(orders)\n      };\n    } catch (error) {\n      console.error('Error loading user stats:', error);\n    }\n  }\n\n  private calculateAverageRating(orders: Delivery[]): number {\n    const ratedOrders = orders.filter(o => o.customerRating && o.customerRating > 0);\n    if (ratedOrders.length === 0) return 0;\n\n    const totalRating = ratedOrders.reduce((sum, order) => sum + (order.customerRating || 0), 0);\n    return Math.round((totalRating / ratedOrders.length) * 10) / 10;\n  }\n\n}\n", "<!-- Customer Portal Header -->\n<div class=\"customer-portal\">\n  <div class=\"container-fluid\">\n    <!-- Header Section -->\n    <div class=\"portal-header bg-primary text-white py-3 mb-4\">\n      <div class=\"container\">\n        <div class=\"row align-items-center\">\n          <div class=\"col-md-4\">\n            <h1 class=\"h4 mb-0\">\n              <i class=\"fa-solid fa-truck me-2\"></i>\n              Espace Client\n            </h1>\n          </div>\n          <div class=\"col-md-4 text-center\">\n            <div class=\"input-group\" style=\"max-width: 300px; margin: 0 auto;\">\n              <input\n                type=\"text\"\n                class=\"form-control\"\n                placeholder=\"Numéro de suivi\"\n                [(ngModel)]=\"trackingId\"\n                (keyup.enter)=\"trackDelivery()\"\n              >\n              <button\n                class=\"btn btn-light\"\n                type=\"button\"\n                (click)=\"trackDelivery()\"\n              >\n                <i class=\"fa-solid fa-search\"></i>\n              </button>\n            </div>\n          </div>\n          <div class=\"col-md-4 text-md-end\">\n            <div class=\"d-flex align-items-center justify-content-md-end gap-3\">\n              <!-- User Info -->\n              <div class=\"d-flex align-items-center\" *ngIf=\"currentUser\">\n                <i class=\"fa-solid fa-user-circle fa-lg me-2\"></i>\n                <span class=\"small\">{{ currentUser.firstName }} {{ currentUser.lastName }}</span>\n              </div>\n\n              <!-- Navigation Tabs -->\n              <div class=\"btn-group\" role=\"group\">\n                <button\n                  type=\"button\"\n                  class=\"btn btn-outline-light btn-sm\"\n                  [class.active]=\"activeTab === 'tracking'\"\n                  (click)=\"setActiveTab('tracking')\"\n                >\n                  <i class=\"fa-solid fa-search me-1\"></i>\n                  Suivi\n                </button>\n                <button\n                  type=\"button\"\n                  class=\"btn btn-outline-light btn-sm\"\n                  [class.active]=\"activeTab === 'orders'\"\n                  (click)=\"setActiveTab('orders')\"\n                >\n                  <i class=\"fa-solid fa-list me-1\"></i>\n                  Commandes\n                </button>\n                <button\n                  type=\"button\"\n                  class=\"btn btn-outline-light btn-sm\"\n                  [class.active]=\"activeTab === 'profile'\"\n                  (click)=\"setActiveTab('profile')\"\n                >\n                  <i class=\"fa-solid fa-user me-1\"></i>\n                  Profil\n                </button>\n              </div>\n\n              <!-- Logout Button -->\n              <button\n                class=\"btn btn-outline-light btn-sm\"\n                (click)=\"logout()\"\n                title=\"Se déconnecter\"\n              >\n                <i class=\"fa-solid fa-sign-out-alt me-1\"></i>\n                Déconnexion\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <div class=\"container\">\n      <!-- Loading State -->\n      <div *ngIf=\"loading\" class=\"text-center py-5\">\n        <div class=\"spinner-border text-primary\" role=\"status\">\n          <span class=\"visually-hidden\">Chargement...</span>\n        </div>\n        <p class=\"mt-3 text-muted\">Recherche de votre livraison...</p>\n      </div>\n\n      <!-- Error State -->\n      <div *ngIf=\"error && !loading\" class=\"alert alert-danger\" role=\"alert\">\n        <i class=\"fa-solid fa-exclamation-triangle me-2\"></i>\n        {{ error }}\n      </div>\n\n      <!-- Tab Content -->\n\n      <!-- Tracking Tab -->\n      <div *ngIf=\"activeTab === 'tracking'\">\n        <!-- Delivery Information -->\n        <div *ngIf=\"delivery && !loading && !error\">\n        <!-- Status Progress -->\n        <div class=\"card mb-4\">\n          <div class=\"card-body\">\n            <h5 class=\"card-title mb-4\">\n              <i class=\"fa-solid fa-route me-2\"></i>\n              État de votre livraison\n            </h5>\n\n            <div class=\"progress-tracker\">\n              <div class=\"progress-line\"></div>\n              <div class=\"row\">\n                <div\n                  *ngFor=\"let step of statusSteps; let i = index\"\n                  class=\"col-md-4\"\n                >\n                  <div class=\"progress-step text-center\">\n                    <div\n                      class=\"step-icon\"\n                      [class.completed]=\"isStatusCompleted(i)\"\n                      [class.active]=\"isStatusActive(i)\"\n                    >\n                      <i [class]=\"step.icon\"></i>\n                    </div>\n                    <h6 class=\"step-title mt-2\"\n                        [class.text-primary]=\"isStatusActive(i)\"\n                        [class.text-success]=\"isStatusCompleted(i) && !isStatusActive(i)\">\n                      {{ step.label }}\n                    </h6>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Delivery Details -->\n        <div class=\"row g-4\">\n          <!-- Main Information -->\n          <div class=\"col-lg-8\">\n            <div class=\"card\">\n              <div class=\"card-header d-flex justify-content-between align-items-center\">\n                <h5 class=\"card-title mb-0\">\n                  <i class=\"fa-solid fa-info-circle me-2\"></i>\n                  Détails de la livraison\n                </h5>\n                <span\n                  class=\"badge\"\n                  [class]=\"'bg-' + getStatusClass(delivery.status)\"\n                >\n                  {{ getStatusText(delivery.status) }}\n                </span>\n              </div>\n              <div class=\"card-body\">\n                <div class=\"row g-3\">\n                  <div class=\"col-md-6\">\n                    <label class=\"form-label text-muted\">Numéro de commande</label>\n                    <p class=\"fw-bold\">{{ delivery.orderId }}</p>\n                  </div>\n                  <div class=\"col-md-6\">\n                    <label class=\"form-label text-muted\">Client</label>\n                    <p class=\"fw-bold\">{{ delivery.customerName }}</p>\n                  </div>\n                  <div class=\"col-12\">\n                    <label class=\"form-label text-muted\">Adresse de livraison</label>\n                    <p class=\"fw-bold\">\n                      <i class=\"fa-solid fa-location-dot me-2 text-primary\"></i>\n                      {{ delivery.address }}\n                    </p>\n                  </div>\n                  <div class=\"col-md-6\">\n                    <label class=\"form-label text-muted\">Livreur</label>\n                    <p class=\"fw-bold\">\n                      <i class=\"fa-solid fa-user me-2 text-info\"></i>\n                      {{ delivery.driverName }}\n                    </p>\n                  </div>\n                  <div class=\"col-md-6\">\n                    <label class=\"form-label text-muted\">Priorité</label>\n                    <span\n                      class=\"badge\"\n                      [class]=\"delivery.priority === 3 ? 'bg-danger' :\n                               delivery.priority === 2 ? 'bg-warning' :\n                               delivery.priority === 1 ? 'bg-info' : 'bg-secondary'\"\n                    >\n                      {{ delivery.priority }}\n                    </span>\n                  </div>\n                </div>\n\n                <hr>\n\n                <!-- Timeline -->\n                <h6 class=\"text-muted mb-3\">Chronologie</h6>\n                <div class=\"timeline\">\n                  <div class=\"timeline-item\">\n                    <div class=\"timeline-marker bg-success\"></div>\n                    <div class=\"timeline-content\">\n                      <h6 class=\"mb-1\">Commande créée</h6>\n                      <p class=\"text-muted small mb-0\">{{ formatDate(delivery.createdAt) }}</p>\n                    </div>\n                  </div>\n\n                  <div *ngIf=\"delivery.pickupTime\" class=\"timeline-item\">\n                    <div class=\"timeline-marker bg-info\"></div>\n                    <div class=\"timeline-content\">\n                      <h6 class=\"mb-1\">Prise en charge</h6>\n                      <p class=\"text-muted small mb-0\">{{ formatDate(delivery.pickupTime) }}</p>\n                    </div>\n                  </div>\n\n                  <div *ngIf=\"delivery.actualDeliveryTime\" class=\"timeline-item\">\n                    <div class=\"timeline-marker bg-success\"></div>\n                    <div class=\"timeline-content\">\n                      <h6 class=\"mb-1\">Livraison effectuée</h6>\n                      <p class=\"text-muted small mb-0\">{{ formatDate(delivery.actualDeliveryTime) }}</p>\n                    </div>\n                  </div>\n                </div>\n\n                <!-- Notes -->\n                <div *ngIf=\"delivery.notes\" class=\"mt-4\">\n                  <h6 class=\"text-muted mb-2\">Instructions spéciales</h6>\n                  <div class=\"alert alert-info\">\n                    <i class=\"fa-solid fa-note-sticky me-2\"></i>\n                    {{ delivery.notes }}\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Sidebar Information -->\n          <div class=\"col-lg-4\">\n            <!-- Estimated Time -->\n            <div class=\"card mb-4\">\n              <div class=\"card-body text-center\">\n                <i class=\"fa-solid fa-clock fa-2x text-primary mb-3\"></i>\n                <h5 class=\"card-title\">Temps estimé</h5>\n                <p class=\"h4 text-primary mb-2\">{{ getEstimatedTimeRemaining() }}</p>\n                <p class=\"text-muted small mb-0\">\n                  Livraison prévue le {{ formatDate(delivery.estimatedDeliveryTime) }}\n                </p>\n              </div>\n            </div>\n\n            <!-- Contact Information -->\n            <div class=\"card mb-4\">\n              <div class=\"card-header\">\n                <h6 class=\"card-title mb-0\">\n                  <i class=\"fa-solid fa-phone me-2\"></i>\n                  Contact\n                </h6>\n              </div>\n              <div class=\"card-body\">\n                <p class=\"mb-2\">\n                  <strong>Service client:</strong><br>\n                  <a href=\"tel:+33123456789\" class=\"text-decoration-none\">\n                    <i class=\"fa-solid fa-phone me-2\"></i>\n                    01 23 45 67 89\n                  </a>\n                </p>\n                <p class=\"mb-0\">\n                  <strong>Email:</strong><br>\n                  <a href=\"mailto:<EMAIL>\" class=\"text-decoration-none\">\n                    <i class=\"fa-solid fa-envelope me-2\"></i>\n                    <EMAIL>\n                  </a>\n                </p>\n              </div>\n            </div>\n\n            <!-- Customer Feedback -->\n            <div *ngIf=\"delivery.status === 'Delivered'\" class=\"card\">\n              <div class=\"card-header\">\n                <h6 class=\"card-title mb-0\">\n                  <i class=\"fa-solid fa-star me-2\"></i>\n                  Votre avis\n                </h6>\n              </div>\n              <div class=\"card-body\">\n                <div *ngIf=\"delivery.customerRating\" class=\"mb-3\">\n                  <p class=\"mb-2\">Note donnée:</p>\n                  <div class=\"rating\">\n                    <i *ngFor=\"let star of [1,2,3,4,5]\"\n                       class=\"fa-solid fa-star\"\n                       [class.text-warning]=\"star <= delivery.customerRating!\"\n                       [class.text-muted]=\"star > delivery.customerRating!\"></i>\n                  </div>\n                </div>\n                <div *ngIf=\"delivery.customerFeedback\" class=\"mb-3\">\n                  <p class=\"mb-2\">Commentaire:</p>\n                  <p class=\"text-muted\">{{ delivery.customerFeedback }}</p>\n                </div>\n                <div *ngIf=\"!delivery.customerRating\">\n                  <p class=\"text-muted\">Merci de nous faire part de votre expérience!</p>\n                  <button class=\"btn btn-primary btn-sm\">\n                    <i class=\"fa-solid fa-star me-2\"></i>\n                    Donner mon avis\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n        </div>\n\n        <!-- No Tracking ID State -->\n        <div *ngIf=\"!trackingId && !loading\" class=\"text-center py-5\">\n          <i class=\"fa-solid fa-search fa-3x text-muted mb-3\"></i>\n          <h4 class=\"text-muted\">Entrez votre numéro de suivi</h4>\n          <p class=\"text-muted\">Saisissez votre numéro de commande ou de suivi dans le champ ci-dessus</p>\n        </div>\n      </div>\n\n      <!-- Orders Tab -->\n      <div *ngIf=\"activeTab === 'orders'\">\n        <div class=\"row\">\n          <div class=\"col-12\">\n            <div class=\"card\">\n              <div class=\"card-header d-flex justify-content-between align-items-center\">\n                <h5 class=\"card-title mb-0\">\n                  <i class=\"fa-solid fa-list me-2\"></i>\n                  Mes Commandes\n                </h5>\n                <div class=\"d-flex gap-2\">\n                  <select class=\"form-select form-select-sm\" [(ngModel)]=\"orderFilter\" (change)=\"filterOrders()\">\n                    <option value=\"all\">Toutes les commandes</option>\n                    <option value=\"pending\">En attente</option>\n                    <option value=\"in-transit\">En cours</option>\n                    <option value=\"delivered\">Livrées</option>\n                    <option value=\"cancelled\">Annulées</option>\n                  </select>\n                  <button class=\"btn btn-primary btn-sm\" (click)=\"loadOrders()\">\n                    <i class=\"fa-solid fa-refresh me-1\"></i>\n                    Actualiser\n                  </button>\n                </div>\n              </div>\n              <div class=\"card-body\">\n                <div *ngIf=\"loadingOrders\" class=\"text-center py-4\">\n                  <div class=\"spinner-border text-primary\" role=\"status\">\n                    <span class=\"visually-hidden\">Chargement...</span>\n                  </div>\n                </div>\n\n                <div *ngIf=\"!loadingOrders && orders.length === 0\" class=\"text-center py-4\">\n                  <i class=\"fa-solid fa-box-open fa-3x text-muted mb-3\"></i>\n                  <h5 class=\"text-muted\">Aucune commande trouvée</h5>\n                  <p class=\"text-muted\">Vous n'avez pas encore passé de commande.</p>\n                </div>\n\n                <div *ngIf=\"!loadingOrders && orders.length > 0\" class=\"table-responsive\">\n                  <table class=\"table table-hover\">\n                    <thead>\n                      <tr>\n                        <th>Commande</th>\n                        <th>Date</th>\n                        <th>Adresse</th>\n                        <th>Statut</th>\n                        <th>Actions</th>\n                      </tr>\n                    </thead>\n                    <tbody>\n                      <tr *ngFor=\"let order of filteredOrders\">\n                        <td>\n                          <strong>{{ order.orderId }}</strong><br>\n                          <small class=\"text-muted\">{{ order.customerName }}</small>\n                        </td>\n                        <td>{{ formatDate(order.createdAt) }}</td>\n                        <td>\n                          <i class=\"fa-solid fa-location-dot me-1 text-primary\"></i>\n                          {{ order.address }}\n                        </td>\n                        <td>\n                          <span class=\"badge\" [class]=\"'bg-' + getStatusClass(order.status)\">\n                            {{ getStatusText(order.status) }}\n                          </span>\n                        </td>\n                        <td>\n                          <button\n                            class=\"btn btn-outline-primary btn-sm me-2\"\n                            (click)=\"viewOrderDetails(order)\"\n                          >\n                            <i class=\"fa-solid fa-eye me-1\"></i>\n                            Voir\n                          </button>\n                          <button\n                            class=\"btn btn-outline-info btn-sm\"\n                            (click)=\"trackOrder(order.orderId)\"\n                          >\n                            <i class=\"fa-solid fa-search me-1\"></i>\n                            Suivre\n                          </button>\n                        </td>\n                      </tr>\n                    </tbody>\n                  </table>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Profile Tab -->\n      <div *ngIf=\"activeTab === 'profile'\">\n        <div class=\"row g-4\">\n          <!-- Profile Information -->\n          <div class=\"col-lg-8\">\n            <div class=\"card\">\n              <div class=\"card-header\">\n                <h5 class=\"card-title mb-0\">\n                  <i class=\"fa-solid fa-user me-2\"></i>\n                  Informations Personnelles\n                </h5>\n              </div>\n              <div class=\"card-body\">\n                <form [formGroup]=\"profileForm\" (ngSubmit)=\"updateProfile()\">\n                  <div class=\"row g-3\">\n                    <div class=\"col-md-6\">\n                      <label class=\"form-label\">Prénom</label>\n                      <input type=\"text\" class=\"form-control\" formControlName=\"firstName\">\n                    </div>\n                    <div class=\"col-md-6\">\n                      <label class=\"form-label\">Nom</label>\n                      <input type=\"text\" class=\"form-control\" formControlName=\"lastName\">\n                    </div>\n                    <div class=\"col-12\">\n                      <label class=\"form-label\">Email</label>\n                      <input type=\"email\" class=\"form-control\" formControlName=\"email\">\n                    </div>\n                    <div class=\"col-md-6\">\n                      <label class=\"form-label\">Téléphone</label>\n                      <input type=\"tel\" class=\"form-control\" formControlName=\"phone\">\n                    </div>\n                    <div class=\"col-md-6\">\n                      <label class=\"form-label\">Date de naissance</label>\n                      <input type=\"date\" class=\"form-control\" formControlName=\"dateOfBirth\">\n                    </div>\n                    <div class=\"col-12\">\n                      <label class=\"form-label\">Adresse</label>\n                      <textarea class=\"form-control\" rows=\"3\" formControlName=\"address\"></textarea>\n                    </div>\n                  </div>\n\n                  <hr>\n\n                  <div class=\"d-flex justify-content-between\">\n                    <button type=\"button\" class=\"btn btn-outline-secondary\" (click)=\"resetProfile()\">\n                      <i class=\"fa-solid fa-undo me-1\"></i>\n                      Annuler\n                    </button>\n                    <button type=\"submit\" class=\"btn btn-primary\" [disabled]=\"!profileForm.valid || updatingProfile\">\n                      <i class=\"fa-solid fa-save me-1\"></i>\n                      <span *ngIf=\"!updatingProfile\">Sauvegarder</span>\n                      <span *ngIf=\"updatingProfile\">Sauvegarde...</span>\n                    </button>\n                  </div>\n                </form>\n              </div>\n            </div>\n          </div>\n\n          <!-- Account Settings -->\n          <div class=\"col-lg-4\">\n            <div class=\"card mb-4\">\n              <div class=\"card-header\">\n                <h6 class=\"card-title mb-0\">\n                  <i class=\"fa-solid fa-cog me-2\"></i>\n                  Paramètres du Compte\n                </h6>\n              </div>\n              <div class=\"card-body\">\n                <div class=\"d-grid gap-2\">\n                  <button class=\"btn btn-outline-primary btn-sm\" (click)=\"changePassword()\">\n                    <i class=\"fa-solid fa-key me-1\"></i>\n                    Changer le mot de passe\n                  </button>\n                  <button class=\"btn btn-outline-info btn-sm\" (click)=\"manageNotifications()\">\n                    <i class=\"fa-solid fa-bell me-1\"></i>\n                    Notifications\n                  </button>\n                  <button class=\"btn btn-outline-secondary btn-sm\" (click)=\"downloadData()\">\n                    <i class=\"fa-solid fa-download me-1\"></i>\n                    Télécharger mes données\n                  </button>\n                </div>\n              </div>\n            </div>\n\n            <!-- Account Statistics -->\n            <div class=\"card\">\n              <div class=\"card-header\">\n                <h6 class=\"card-title mb-0\">\n                  <i class=\"fa-solid fa-chart-bar me-2\"></i>\n                  Statistiques\n                </h6>\n              </div>\n              <div class=\"card-body\">\n                <div class=\"row g-3 text-center\">\n                  <div class=\"col-6\">\n                    <div class=\"border rounded p-2\">\n                      <h4 class=\"text-primary mb-1\">{{ userStats.totalOrders }}</h4>\n                      <small class=\"text-muted\">Commandes</small>\n                    </div>\n                  </div>\n                  <div class=\"col-6\">\n                    <div class=\"border rounded p-2\">\n                      <h4 class=\"text-success mb-1\">{{ userStats.deliveredOrders }}</h4>\n                      <small class=\"text-muted\">Livrées</small>\n                    </div>\n                  </div>\n                  <div class=\"col-12\">\n                    <div class=\"border rounded p-2\">\n                      <h5 class=\"text-info mb-1\">{{ userStats.averageRating }}/5</h5>\n                      <small class=\"text-muted\">Note moyenne donnée</small>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": ";AAEA,SAAiCA,UAAU,QAAQ,gBAAgB;AAKnE,SAAmBC,cAAc,QAAQ,kCAAkC;;;;;;;;;;IC2B7DC,EAAA,CAAAC,cAAA,cAA2D;IACzDD,EAAA,CAAAE,SAAA,YAAkD;IAClDF,EAAA,CAAAC,cAAA,eAAoB;IAAAD,EAAA,CAAAG,MAAA,GAAsD;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAA7DJ,EAAA,CAAAK,SAAA,GAAsD;IAAtDL,EAAA,CAAAM,kBAAA,KAAAC,MAAA,CAAAC,WAAA,CAAAC,SAAA,OAAAF,MAAA,CAAAC,WAAA,CAAAE,QAAA,KAAsD;;;;;IAmDpFV,EAAA,CAAAC,cAAA,cAA8C;IAEZD,EAAA,CAAAG,MAAA,oBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEpDJ,EAAA,CAAAC,cAAA,YAA2B;IAAAD,EAAA,CAAAG,MAAA,sCAA+B;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IAIhEJ,EAAA,CAAAC,cAAA,cAAuE;IACrED,EAAA,CAAAE,SAAA,YAAqD;IACrDF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAW,kBAAA,MAAAC,MAAA,CAAAC,KAAA,MACF;;;;;IAmBUb,EAAA,CAAAC,cAAA,aAGC;IAOKD,EAAA,CAAAE,SAAA,QAA2B;IAC7BF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,aAEsE;IACpED,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;;;;;;IATHJ,EAAA,CAAAK,SAAA,GAAwC;IAAxCL,EAAA,CAAAc,WAAA,cAAAC,MAAA,CAAAC,iBAAA,CAAAC,KAAA,EAAwC,WAAAF,MAAA,CAAAG,cAAA,CAAAD,KAAA;IAGrCjB,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAmB,UAAA,CAAAC,QAAA,CAAAC,IAAA,CAAmB;IAGpBrB,EAAA,CAAAK,SAAA,GAAwC;IAAxCL,EAAA,CAAAc,WAAA,iBAAAC,MAAA,CAAAG,cAAA,CAAAD,KAAA,EAAwC,iBAAAF,MAAA,CAAAC,iBAAA,CAAAC,KAAA,MAAAF,MAAA,CAAAG,cAAA,CAAAD,KAAA;IAE1CjB,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAW,kBAAA,MAAAS,QAAA,CAAAE,KAAA,MACF;;;;;IA2EFtB,EAAA,CAAAC,cAAA,cAAuD;IACrDD,EAAA,CAAAE,SAAA,cAA2C;IAC3CF,EAAA,CAAAC,cAAA,cAA8B;IACXD,EAAA,CAAAG,MAAA,sBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACrCJ,EAAA,CAAAC,cAAA,YAAiC;IAAAD,EAAA,CAAAG,MAAA,GAAqC;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IAAzCJ,EAAA,CAAAK,SAAA,GAAqC;IAArCL,EAAA,CAAAuB,iBAAA,CAAAC,MAAA,CAAAC,UAAA,CAAAD,MAAA,CAAAE,QAAA,CAAAC,UAAA,EAAqC;;;;;IAI1E3B,EAAA,CAAAC,cAAA,cAA+D;IAC7DD,EAAA,CAAAE,SAAA,cAA8C;IAC9CF,EAAA,CAAAC,cAAA,cAA8B;IACXD,EAAA,CAAAG,MAAA,+BAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACzCJ,EAAA,CAAAC,cAAA,YAAiC;IAAAD,EAAA,CAAAG,MAAA,GAA6C;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IAAjDJ,EAAA,CAAAK,SAAA,GAA6C;IAA7CL,EAAA,CAAAuB,iBAAA,CAAAK,OAAA,CAAAH,UAAA,CAAAG,OAAA,CAAAF,QAAA,CAAAG,kBAAA,EAA6C;;;;;IAMpF7B,EAAA,CAAAC,cAAA,cAAyC;IACXD,EAAA,CAAAG,MAAA,kCAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACvDJ,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAE,SAAA,YAA4C;IAC5CF,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAW,kBAAA,MAAAmB,OAAA,CAAAJ,QAAA,CAAAK,KAAA,MACF;;;;;IA0DE/B,EAAA,CAAAE,SAAA,YAG4D;;;;;IADzDF,EAAA,CAAAc,WAAA,iBAAAkB,QAAA,IAAAC,OAAA,CAAAP,QAAA,CAAAQ,cAAA,CAAuD,eAAAF,QAAA,GAAAC,OAAA,CAAAP,QAAA,CAAAQ,cAAA;;;;;;;;IAL9DlC,EAAA,CAAAC,cAAA,cAAkD;IAChCD,EAAA,CAAAG,MAAA,wBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAChCJ,EAAA,CAAAC,cAAA,cAAoB;IAClBD,EAAA,CAAAmC,UAAA,IAAAC,8DAAA,gBAG4D;IAC9DpC,EAAA,CAAAI,YAAA,EAAM;;;IAJgBJ,EAAA,CAAAK,SAAA,GAAc;IAAdL,EAAA,CAAAqC,UAAA,YAAArC,EAAA,CAAAsC,eAAA,IAAAC,GAAA,EAAc;;;;;IAMtCvC,EAAA,CAAAC,cAAA,cAAoD;IAClCD,EAAA,CAAAG,MAAA,mBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAChCJ,EAAA,CAAAC,cAAA,YAAsB;IAAAD,EAAA,CAAAG,MAAA,GAA+B;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IAAnCJ,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAuB,iBAAA,CAAAiB,OAAA,CAAAd,QAAA,CAAAe,gBAAA,CAA+B;;;;;IAEvDzC,EAAA,CAAAC,cAAA,UAAsC;IACdD,EAAA,CAAAG,MAAA,yDAA6C;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACvEJ,EAAA,CAAAC,cAAA,iBAAuC;IACrCD,EAAA,CAAAE,SAAA,YAAqC;IACrCF,EAAA,CAAAG,MAAA,wBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;;IA1BfJ,EAAA,CAAAC,cAAA,cAA0D;IAGpDD,EAAA,CAAAE,SAAA,YAAqC;IACrCF,EAAA,CAAAG,MAAA,mBACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAEPJ,EAAA,CAAAC,cAAA,cAAuB;IACrBD,EAAA,CAAAmC,UAAA,IAAAO,0DAAA,kBAQM;IACN1C,EAAA,CAAAmC,UAAA,IAAAQ,0DAAA,kBAGM;IACN3C,EAAA,CAAAmC,UAAA,IAAAS,0DAAA,kBAMM;IACR5C,EAAA,CAAAI,YAAA,EAAM;;;;IApBEJ,EAAA,CAAAK,SAAA,GAA6B;IAA7BL,EAAA,CAAAqC,UAAA,SAAAQ,OAAA,CAAAnB,QAAA,CAAAQ,cAAA,CAA6B;IAS7BlC,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAqC,UAAA,SAAAQ,OAAA,CAAAnB,QAAA,CAAAe,gBAAA,CAA+B;IAI/BzC,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAAqC,UAAA,UAAAQ,OAAA,CAAAnB,QAAA,CAAAQ,cAAA,CAA8B;;;;;IAlM5ClC,EAAA,CAAAC,cAAA,UAA4C;IAKtCD,EAAA,CAAAE,SAAA,YAAsC;IACtCF,EAAA,CAAAG,MAAA,qCACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAELJ,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAE,SAAA,cAAiC;IACjCF,EAAA,CAAAC,cAAA,cAAiB;IACfD,EAAA,CAAAmC,UAAA,IAAAW,mDAAA,mBAkBM;IACR9C,EAAA,CAAAI,YAAA,EAAM;IAMZJ,EAAA,CAAAC,cAAA,eAAqB;IAMXD,EAAA,CAAAE,SAAA,aAA4C;IAC5CF,EAAA,CAAAG,MAAA,sCACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,gBAGC;IACCD,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAETJ,EAAA,CAAAC,cAAA,eAAuB;IAGoBD,EAAA,CAAAG,MAAA,+BAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC/DJ,EAAA,CAAAC,cAAA,aAAmB;IAAAD,EAAA,CAAAG,MAAA,IAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAE/CJ,EAAA,CAAAC,cAAA,eAAsB;IACiBD,EAAA,CAAAG,MAAA,cAAM;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACnDJ,EAAA,CAAAC,cAAA,aAAmB;IAAAD,EAAA,CAAAG,MAAA,IAA2B;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAEpDJ,EAAA,CAAAC,cAAA,eAAoB;IACmBD,EAAA,CAAAG,MAAA,4BAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACjEJ,EAAA,CAAAC,cAAA,aAAmB;IACjBD,EAAA,CAAAE,SAAA,aAA0D;IAC1DF,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAENJ,EAAA,CAAAC,cAAA,eAAsB;IACiBD,EAAA,CAAAG,MAAA,eAAO;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACpDJ,EAAA,CAAAC,cAAA,aAAmB;IACjBD,EAAA,CAAAE,SAAA,aAA+C;IAC/CF,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAENJ,EAAA,CAAAC,cAAA,eAAsB;IACiBD,EAAA,CAAAG,MAAA,qBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACrDJ,EAAA,CAAAC,cAAA,gBAKC;IACCD,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAIXJ,EAAA,CAAAE,SAAA,UAAI;IAGJF,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAG,MAAA,mBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC5CJ,EAAA,CAAAC,cAAA,eAAsB;IAElBD,EAAA,CAAAE,SAAA,eAA8C;IAC9CF,EAAA,CAAAC,cAAA,eAA8B;IACXD,EAAA,CAAAG,MAAA,gCAAc;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACpCJ,EAAA,CAAAC,cAAA,aAAiC;IAAAD,EAAA,CAAAG,MAAA,IAAoC;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAI7EJ,EAAA,CAAAmC,UAAA,KAAAY,oDAAA,kBAMM;IAEN/C,EAAA,CAAAmC,UAAA,KAAAa,oDAAA,kBAMM;IACRhD,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAmC,UAAA,KAAAc,oDAAA,kBAMM;IACRjD,EAAA,CAAAI,YAAA,EAAM;IAKVJ,EAAA,CAAAC,cAAA,eAAsB;IAIhBD,EAAA,CAAAE,SAAA,aAAyD;IACzDF,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAG,MAAA,yBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACxCJ,EAAA,CAAAC,cAAA,aAAgC;IAAAD,EAAA,CAAAG,MAAA,IAAiC;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACrEJ,EAAA,CAAAC,cAAA,aAAiC;IAC/BD,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAKRJ,EAAA,CAAAC,cAAA,eAAuB;IAGjBD,EAAA,CAAAE,SAAA,aAAsC;IACtCF,EAAA,CAAAG,MAAA,iBACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAEPJ,EAAA,CAAAC,cAAA,eAAuB;IAEXD,EAAA,CAAAG,MAAA,uBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAAAJ,EAAA,CAAAE,SAAA,UAAI;IACpCF,EAAA,CAAAC,cAAA,aAAwD;IACtDD,EAAA,CAAAE,SAAA,aAAsC;IACtCF,EAAA,CAAAG,MAAA,wBACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAENJ,EAAA,CAAAC,cAAA,aAAgB;IACND,EAAA,CAAAG,MAAA,cAAM;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAAAJ,EAAA,CAAAE,SAAA,UAAI;IAC3BF,EAAA,CAAAC,cAAA,aAAsE;IACpED,EAAA,CAAAE,SAAA,aAAyC;IACzCF,EAAA,CAAAG,MAAA,iCACF;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAMVJ,EAAA,CAAAmC,UAAA,KAAAe,oDAAA,kBA6BM;IACRlD,EAAA,CAAAI,YAAA,EAAM;;;;IA9LmBJ,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAAqC,UAAA,YAAAc,MAAA,CAAAC,WAAA,CAAgB;IAmCjCpD,EAAA,CAAAK,SAAA,GAAiD;IAAjDL,EAAA,CAAAmB,UAAA,SAAAgC,MAAA,CAAAE,cAAA,CAAAF,MAAA,CAAAzB,QAAA,CAAA4B,MAAA,EAAiD;IAEjDtD,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAW,kBAAA,MAAAwC,MAAA,CAAAI,aAAA,CAAAJ,MAAA,CAAAzB,QAAA,CAAA4B,MAAA,OACF;IAMuBtD,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAuB,iBAAA,CAAA4B,MAAA,CAAAzB,QAAA,CAAA8B,OAAA,CAAsB;IAItBxD,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAAuB,iBAAA,CAAA4B,MAAA,CAAAzB,QAAA,CAAA+B,YAAA,CAA2B;IAM5CzD,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAW,kBAAA,MAAAwC,MAAA,CAAAzB,QAAA,CAAAgC,OAAA,MACF;IAME1D,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAW,kBAAA,MAAAwC,MAAA,CAAAzB,QAAA,CAAAiC,UAAA,MACF;IAME3D,EAAA,CAAAK,SAAA,GAE8D;IAF9DL,EAAA,CAAAmB,UAAA,CAAAgC,MAAA,CAAAzB,QAAA,CAAAkC,QAAA,uBAAAT,MAAA,CAAAzB,QAAA,CAAAkC,QAAA,wBAAAT,MAAA,CAAAzB,QAAA,CAAAkC,QAAA,oCAE8D;IAE9D5D,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAW,kBAAA,MAAAwC,MAAA,CAAAzB,QAAA,CAAAkC,QAAA,MACF;IAamC5D,EAAA,CAAAK,SAAA,IAAoC;IAApCL,EAAA,CAAAuB,iBAAA,CAAA4B,MAAA,CAAA1B,UAAA,CAAA0B,MAAA,CAAAzB,QAAA,CAAAmC,SAAA,EAAoC;IAInE7D,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAAqC,UAAA,SAAAc,MAAA,CAAAzB,QAAA,CAAAC,UAAA,CAAyB;IAQzB3B,EAAA,CAAAK,SAAA,GAAiC;IAAjCL,EAAA,CAAAqC,UAAA,SAAAc,MAAA,CAAAzB,QAAA,CAAAG,kBAAA,CAAiC;IAUnC7B,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAqC,UAAA,SAAAc,MAAA,CAAAzB,QAAA,CAAAK,KAAA,CAAoB;IAkBM/B,EAAA,CAAAK,SAAA,GAAiC;IAAjCL,EAAA,CAAAuB,iBAAA,CAAA4B,MAAA,CAAAW,yBAAA,GAAiC;IAE/D9D,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAW,kBAAA,+BAAAwC,MAAA,CAAA1B,UAAA,CAAA0B,MAAA,CAAAzB,QAAA,CAAAqC,qBAAA,OACF;IA+BE/D,EAAA,CAAAK,SAAA,IAAqC;IAArCL,EAAA,CAAAqC,UAAA,SAAAc,MAAA,CAAAzB,QAAA,CAAA4B,MAAA,iBAAqC;;;;;IAmC/CtD,EAAA,CAAAC,cAAA,cAA8D;IAC5DD,EAAA,CAAAE,SAAA,YAAwD;IACxDF,EAAA,CAAAC,cAAA,aAAuB;IAAAD,EAAA,CAAAG,MAAA,wCAA4B;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACxDJ,EAAA,CAAAC,cAAA,YAAsB;IAAAD,EAAA,CAAAG,MAAA,kFAAsE;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;IArNpGJ,EAAA,CAAAC,cAAA,UAAsC;IAEpCD,EAAA,CAAAmC,UAAA,IAAA6B,6CAAA,oBA6MM;IAGNhE,EAAA,CAAAmC,UAAA,IAAA8B,6CAAA,kBAIM;IACRjE,EAAA,CAAAI,YAAA,EAAM;;;;IArNEJ,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAAqC,UAAA,SAAA6B,MAAA,CAAAxC,QAAA,KAAAwC,MAAA,CAAAC,OAAA,KAAAD,MAAA,CAAArD,KAAA,CAAoC;IAgNpCb,EAAA,CAAAK,SAAA,GAA6B;IAA7BL,EAAA,CAAAqC,UAAA,UAAA6B,MAAA,CAAAE,UAAA,KAAAF,MAAA,CAAAC,OAAA,CAA6B;;;;;IAgC3BnE,EAAA,CAAAC,cAAA,eAAoD;IAElBD,EAAA,CAAAG,MAAA,oBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAItDJ,EAAA,CAAAC,cAAA,eAA4E;IAC1ED,EAAA,CAAAE,SAAA,aAA0D;IAC1DF,EAAA,CAAAC,cAAA,aAAuB;IAAAD,EAAA,CAAAG,MAAA,mCAAuB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACnDJ,EAAA,CAAAC,cAAA,YAAsB;IAAAD,EAAA,CAAAG,MAAA,qDAAyC;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;;IAe/DJ,EAAA,CAAAC,cAAA,SAAyC;IAE7BD,EAAA,CAAAG,MAAA,GAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAAAJ,EAAA,CAAAE,SAAA,SAAI;IACxCF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAG,MAAA,GAAwB;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAE5DJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,GAAiC;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC1CJ,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAE,SAAA,cAA0D;IAC1DF,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,UAAI;IAEAD,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAETJ,EAAA,CAAAC,cAAA,UAAI;IAGAD,EAAA,CAAAqE,UAAA,mBAAAC,8EAAA;MAAA,MAAAC,WAAA,GAAAvE,EAAA,CAAAwE,aAAA,CAAAC,IAAA;MAAA,MAAAC,SAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,OAAA,GAAA5E,EAAA,CAAA6E,aAAA;MAAA,OAAS7E,EAAA,CAAA8E,WAAA,CAAAF,OAAA,CAAAG,gBAAA,CAAAL,SAAA,CAAuB;IAAA,EAAC;IAEjC1E,EAAA,CAAAE,SAAA,cAAoC;IACpCF,EAAA,CAAAG,MAAA,cACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,mBAGC;IADCD,EAAA,CAAAqE,UAAA,mBAAAW,8EAAA;MAAA,MAAAT,WAAA,GAAAvE,EAAA,CAAAwE,aAAA,CAAAC,IAAA;MAAA,MAAAC,SAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAM,OAAA,GAAAjF,EAAA,CAAA6E,aAAA;MAAA,OAAS7E,EAAA,CAAA8E,WAAA,CAAAG,OAAA,CAAAC,UAAA,CAAAR,SAAA,CAAAlB,OAAA,CAAyB;IAAA,EAAC;IAEnCxD,EAAA,CAAAE,SAAA,aAAuC;IACvCF,EAAA,CAAAG,MAAA,gBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;;IA3BDJ,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAuB,iBAAA,CAAAmD,SAAA,CAAAlB,OAAA,CAAmB;IACDxD,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAAuB,iBAAA,CAAAmD,SAAA,CAAAjB,YAAA,CAAwB;IAEhDzD,EAAA,CAAAK,SAAA,GAAiC;IAAjCL,EAAA,CAAAuB,iBAAA,CAAA4D,OAAA,CAAA1D,UAAA,CAAAiD,SAAA,CAAAb,SAAA,EAAiC;IAGnC7D,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAW,kBAAA,MAAA+D,SAAA,CAAAhB,OAAA,MACF;IAEsB1D,EAAA,CAAAK,SAAA,GAA8C;IAA9CL,EAAA,CAAAmB,UAAA,SAAAgE,OAAA,CAAA9B,cAAA,CAAAqB,SAAA,CAAApB,MAAA,EAA8C;IAChEtD,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAW,kBAAA,MAAAwE,OAAA,CAAA5B,aAAA,CAAAmB,SAAA,CAAApB,MAAA,OACF;;;;;IAzBVtD,EAAA,CAAAC,cAAA,eAA0E;IAI9DD,EAAA,CAAAG,MAAA,eAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACjBJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,WAAI;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACbJ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAG,MAAA,cAAO;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAChBJ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAG,MAAA,cAAM;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACfJ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAG,MAAA,eAAO;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAGpBJ,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAAmC,UAAA,KAAAiD,oDAAA,mBA+BK;IACPpF,EAAA,CAAAI,YAAA,EAAQ;;;;IAhCgBJ,EAAA,CAAAK,SAAA,IAAiB;IAAjBL,EAAA,CAAAqC,UAAA,YAAAgD,OAAA,CAAAC,cAAA,CAAiB;;;;;;IAhDvDtF,EAAA,CAAAC,cAAA,UAAoC;IAMxBD,EAAA,CAAAE,SAAA,YAAqC;IACrCF,EAAA,CAAAG,MAAA,sBACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,cAA0B;IACmBD,EAAA,CAAAqE,UAAA,2BAAAkB,wEAAAC,MAAA;MAAAxF,EAAA,CAAAwE,aAAA,CAAAiB,IAAA;MAAA,MAAAC,OAAA,GAAA1F,EAAA,CAAA6E,aAAA;MAAA,OAAA7E,EAAA,CAAA8E,WAAA,CAAAY,OAAA,CAAAC,WAAA,GAAAH,MAAA;IAAA,EAAyB,oBAAAI,iEAAA;MAAA5F,EAAA,CAAAwE,aAAA,CAAAiB,IAAA;MAAA,MAAAI,OAAA,GAAA7F,EAAA,CAAA6E,aAAA;MAAA,OAAW7E,EAAA,CAAA8E,WAAA,CAAAe,OAAA,CAAAC,YAAA,EAAc;IAAA,EAAzB;IAClE9F,EAAA,CAAAC,cAAA,kBAAoB;IAAAD,EAAA,CAAAG,MAAA,4BAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACjDJ,EAAA,CAAAC,cAAA,mBAAwB;IAAAD,EAAA,CAAAG,MAAA,kBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAC3CJ,EAAA,CAAAC,cAAA,mBAA2B;IAAAD,EAAA,CAAAG,MAAA,gBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAC5CJ,EAAA,CAAAC,cAAA,mBAA0B;IAAAD,EAAA,CAAAG,MAAA,oBAAO;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAC1CJ,EAAA,CAAAC,cAAA,mBAA0B;IAAAD,EAAA,CAAAG,MAAA,qBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAE7CJ,EAAA,CAAAC,cAAA,mBAA8D;IAAvBD,EAAA,CAAAqE,UAAA,mBAAA0B,iEAAA;MAAA/F,EAAA,CAAAwE,aAAA,CAAAiB,IAAA;MAAA,MAAAO,OAAA,GAAAhG,EAAA,CAAA6E,aAAA;MAAA,OAAS7E,EAAA,CAAA8E,WAAA,CAAAkB,OAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IAC3DjG,EAAA,CAAAE,SAAA,cAAwC;IACxCF,EAAA,CAAAG,MAAA,oBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAGbJ,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAmC,UAAA,KAAA+D,8CAAA,mBAIM;IAENlG,EAAA,CAAAmC,UAAA,KAAAgE,8CAAA,mBAIM;IAENnG,EAAA,CAAAmC,UAAA,KAAAiE,8CAAA,oBA8CM;IACRpG,EAAA,CAAAI,YAAA,EAAM;;;;IAzEyCJ,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAAqC,UAAA,YAAAgE,MAAA,CAAAV,WAAA,CAAyB;IAchE3F,EAAA,CAAAK,SAAA,IAAmB;IAAnBL,EAAA,CAAAqC,UAAA,SAAAgE,MAAA,CAAAC,aAAA,CAAmB;IAMnBtG,EAAA,CAAAK,SAAA,GAA2C;IAA3CL,EAAA,CAAAqC,UAAA,UAAAgE,MAAA,CAAAC,aAAA,IAAAD,MAAA,CAAAE,MAAA,CAAAC,MAAA,OAA2C;IAM3CxG,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAAqC,UAAA,UAAAgE,MAAA,CAAAC,aAAA,IAAAD,MAAA,CAAAE,MAAA,CAAAC,MAAA,KAAyC;;;;;IAuGzCxG,EAAA,CAAAC,cAAA,WAA+B;IAAAD,EAAA,CAAAG,MAAA,kBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IACjDJ,EAAA,CAAAC,cAAA,WAA8B;IAAAD,EAAA,CAAAG,MAAA,oBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;;IAlDlEJ,EAAA,CAAAC,cAAA,UAAqC;IAOzBD,EAAA,CAAAE,SAAA,aAAqC;IACrCF,EAAA,CAAAG,MAAA,kCACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAEPJ,EAAA,CAAAC,cAAA,cAAuB;IACWD,EAAA,CAAAqE,UAAA,sBAAAoC,iEAAA;MAAAzG,EAAA,CAAAwE,aAAA,CAAAkC,IAAA;MAAA,MAAAC,OAAA,GAAA3G,EAAA,CAAA6E,aAAA;MAAA,OAAY7E,EAAA,CAAA8E,WAAA,CAAA6B,OAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAC1D5G,EAAA,CAAAC,cAAA,eAAqB;IAESD,EAAA,CAAAG,MAAA,mBAAM;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACxCJ,EAAA,CAAAE,SAAA,kBAAoE;IACtEF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,eAAsB;IACMD,EAAA,CAAAG,MAAA,WAAG;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACrCJ,EAAA,CAAAE,SAAA,kBAAmE;IACrEF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,eAAoB;IACQD,EAAA,CAAAG,MAAA,aAAK;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACvCJ,EAAA,CAAAE,SAAA,kBAAiE;IACnEF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,eAAsB;IACMD,EAAA,CAAAG,MAAA,2BAAS;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAC3CJ,EAAA,CAAAE,SAAA,kBAA+D;IACjEF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,eAAsB;IACMD,EAAA,CAAAG,MAAA,yBAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACnDJ,EAAA,CAAAE,SAAA,kBAAsE;IACxEF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,eAAoB;IACQD,EAAA,CAAAG,MAAA,eAAO;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IACzCJ,EAAA,CAAAE,SAAA,qBAA6E;IAC/EF,EAAA,CAAAI,YAAA,EAAM;IAGRJ,EAAA,CAAAE,SAAA,UAAI;IAEJF,EAAA,CAAAC,cAAA,gBAA4C;IACcD,EAAA,CAAAqE,UAAA,mBAAAwC,iEAAA;MAAA7G,EAAA,CAAAwE,aAAA,CAAAkC,IAAA;MAAA,MAAAI,OAAA,GAAA9G,EAAA,CAAA6E,aAAA;MAAA,OAAS7E,EAAA,CAAA8E,WAAA,CAAAgC,OAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IAC9E/G,EAAA,CAAAE,SAAA,cAAqC;IACrCF,EAAA,CAAAG,MAAA,iBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,mBAAiG;IAC/FD,EAAA,CAAAE,SAAA,cAAqC;IACrCF,EAAA,CAAAmC,UAAA,KAAA6E,+CAAA,mBAAiD;IACjDhH,EAAA,CAAAmC,UAAA,KAAA8E,+CAAA,mBAAkD;IACpDjH,EAAA,CAAAI,YAAA,EAAS;IAQnBJ,EAAA,CAAAC,cAAA,eAAsB;IAIdD,EAAA,CAAAE,SAAA,cAAoC;IACpCF,EAAA,CAAAG,MAAA,mCACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAEPJ,EAAA,CAAAC,cAAA,eAAuB;IAE4BD,EAAA,CAAAqE,UAAA,mBAAA6C,iEAAA;MAAAlH,EAAA,CAAAwE,aAAA,CAAAkC,IAAA;MAAA,MAAAS,OAAA,GAAAnH,EAAA,CAAA6E,aAAA;MAAA,OAAS7E,EAAA,CAAA8E,WAAA,CAAAqC,OAAA,CAAAC,cAAA,EAAgB;IAAA,EAAC;IACvEpH,EAAA,CAAAE,SAAA,cAAoC;IACpCF,EAAA,CAAAG,MAAA,iCACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,mBAA4E;IAAhCD,EAAA,CAAAqE,UAAA,mBAAAgD,iEAAA;MAAArH,EAAA,CAAAwE,aAAA,CAAAkC,IAAA;MAAA,MAAAY,OAAA,GAAAtH,EAAA,CAAA6E,aAAA;MAAA,OAAS7E,EAAA,CAAA8E,WAAA,CAAAwC,OAAA,CAAAC,mBAAA,EAAqB;IAAA,EAAC;IACzEvH,EAAA,CAAAE,SAAA,cAAqC;IACrCF,EAAA,CAAAG,MAAA,uBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACTJ,EAAA,CAAAC,cAAA,mBAA0E;IAAzBD,EAAA,CAAAqE,UAAA,mBAAAmD,iEAAA;MAAAxH,EAAA,CAAAwE,aAAA,CAAAkC,IAAA;MAAA,MAAAe,OAAA,GAAAzH,EAAA,CAAA6E,aAAA;MAAA,OAAS7E,EAAA,CAAA8E,WAAA,CAAA2C,OAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IACvE1H,EAAA,CAAAE,SAAA,cAAyC;IACzCF,EAAA,CAAAG,MAAA,gDACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;IAMfJ,EAAA,CAAAC,cAAA,eAAkB;IAGZD,EAAA,CAAAE,SAAA,cAA0C;IAC1CF,EAAA,CAAAG,MAAA,sBACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAEPJ,EAAA,CAAAC,cAAA,eAAuB;IAIeD,EAAA,CAAAG,MAAA,IAA2B;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC9DJ,EAAA,CAAAC,cAAA,iBAA0B;IAAAD,EAAA,CAAAG,MAAA,iBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAG/CJ,EAAA,CAAAC,cAAA,gBAAmB;IAEeD,EAAA,CAAAG,MAAA,IAA+B;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAClEJ,EAAA,CAAAC,cAAA,iBAA0B;IAAAD,EAAA,CAAAG,MAAA,oBAAO;IAAAH,EAAA,CAAAI,YAAA,EAAQ;IAG7CJ,EAAA,CAAAC,cAAA,eAAoB;IAEWD,EAAA,CAAAG,MAAA,IAA+B;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC/DJ,EAAA,CAAAC,cAAA,iBAA0B;IAAAD,EAAA,CAAAG,MAAA,gCAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAQ;;;;IAlGrDJ,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAAqC,UAAA,cAAAsF,MAAA,CAAAC,WAAA,CAAyB;IAmCmB5H,EAAA,CAAAK,SAAA,IAAkD;IAAlDL,EAAA,CAAAqC,UAAA,cAAAsF,MAAA,CAAAC,WAAA,CAAAC,KAAA,IAAAF,MAAA,CAAAG,eAAA,CAAkD;IAEvF9H,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAqC,UAAA,UAAAsF,MAAA,CAAAG,eAAA,CAAsB;IACtB9H,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAAqC,UAAA,SAAAsF,MAAA,CAAAG,eAAA,CAAqB;IA+CE9H,EAAA,CAAAK,SAAA,IAA2B;IAA3BL,EAAA,CAAAuB,iBAAA,CAAAoG,MAAA,CAAAI,SAAA,CAAAC,WAAA,CAA2B;IAM3BhI,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAuB,iBAAA,CAAAoG,MAAA,CAAAI,SAAA,CAAAE,eAAA,CAA+B;IAMlCjI,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAW,kBAAA,KAAAgH,MAAA,CAAAI,SAAA,CAAAG,aAAA,OAA+B;;;ADzfhF,OAAM,MAAOC,uBAAuB;EAsClCC,YACUC,KAAqB,EACrBC,MAAc,EACdC,WAAwB,EACxBC,eAAgC,EAChCC,eAAgC,EAChCC,WAAwB;IALxB,KAAAL,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,WAAW,GAAXA,WAAW;IA3CrB;IACA,KAAAC,SAAS,GAAG,UAAU;IAEtB;IACA,KAAAnI,WAAW,GAAgB,IAAI;IAE/B;IACA,KAAAkB,QAAQ,GAAoB,IAAI;IAChC,KAAAyC,OAAO,GAAG,IAAI;IACd,KAAAtD,KAAK,GAAG,EAAE;IACV,KAAAuD,UAAU,GAAG,EAAE;IAEf;IACA,KAAAmC,MAAM,GAAe,EAAE;IACvB,KAAAjB,cAAc,GAAe,EAAE;IAC/B,KAAAgB,aAAa,GAAG,KAAK;IACrB,KAAAX,WAAW,GAAG,KAAK;IAInB,KAAAmC,eAAe,GAAG,KAAK;IACvB,KAAAC,SAAS,GAAG;MACVC,WAAW,EAAE,CAAC;MACdC,eAAe,EAAE,CAAC;MAClBC,aAAa,EAAE;KAChB;IAED;IACQ,KAAAU,aAAa,GAAmB,EAAE;IAE1C;IACA,KAAAxF,WAAW,GAAG,CACZ;MAAEyF,GAAG,EAAE9I,cAAc,CAAC+I,OAAO;MAAExH,KAAK,EAAE,oBAAoB;MAAED,IAAI,EAAE;IAAiB,CAAE,EACrF;MAAEwH,GAAG,EAAE9I,cAAc,CAACgJ,SAAS;MAAEzH,KAAK,EAAE,uBAAuB;MAAED,IAAI,EAAE;IAAU,CAAE,EACnF;MAAEwH,GAAG,EAAE9I,cAAc,CAACiJ,SAAS;MAAE1H,KAAK,EAAE,OAAO;MAAED,IAAI,EAAE;IAAc,CAAE,CACxE;IAUC,IAAI,CAACuG,WAAW,GAAG,IAAI,CAACW,WAAW,CAACU,KAAK,CAAC;MACxCxI,SAAS,EAAE,CAAC,EAAE,EAAEX,UAAU,CAACoJ,QAAQ,CAAC;MACpCxI,QAAQ,EAAE,CAAC,EAAE,EAAEZ,UAAU,CAACoJ,QAAQ,CAAC;MACnCC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACrJ,UAAU,CAACoJ,QAAQ,EAAEpJ,UAAU,CAACqJ,KAAK,CAAC,CAAC;MACpDC,KAAK,EAAE,CAAC,EAAE,CAAC;MACXC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjB3F,OAAO,EAAE,CAAC,EAAE;KACb,CAAC;EACJ;EAEA4F,QAAQA,CAAA;IACN;IACA,IAAI,CAAC9I,WAAW,GAAG,IAAI,CAACkI,WAAW,CAACa,cAAc,EAAE;IACpD,IAAI,IAAI,CAAC/I,WAAW,EAAE;MACpB,IAAI,CAACgJ,qBAAqB,EAAE;MAC5B,IAAI,CAACC,aAAa,EAAE;;IAGtB,IAAI,CAACpB,KAAK,CAACqB,MAAM,CAACC,SAAS,CAACD,MAAM,IAAG;MACnC,IAAI,CAACtF,UAAU,GAAGsF,MAAM,CAAC,YAAY,CAAC,IAAI,EAAE;MAC5C,IAAI,IAAI,CAACtF,UAAU,EAAE;QACnB,IAAI,CAACwF,YAAY,CAAC,UAAU,CAAC;QAC7B,IAAI,CAACC,YAAY,EAAE;QACnB,IAAI,CAACC,oBAAoB,EAAE;OAC5B,MAAM;QACL;QACA,IAAI,CAAC3F,OAAO,GAAG,KAAK;;IAExB,CAAC,CAAC;IAEF;IACA,IAAI,IAAI,CAACwE,SAAS,KAAK,QAAQ,EAAE;MAC/B,IAAI,CAAC1C,UAAU,EAAE;;EAErB;EAEA8D,WAAWA,CAAA;IACT,IAAI,CAACnB,aAAa,CAACoB,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;IACpD,IAAI,CAACzB,eAAe,CAAC0B,cAAc,EAAE;EACvC;EAEcN,YAAYA,CAAA;IAAA,IAAAO,KAAA;IAAA,OAAAC,iBAAA;MACxB,IAAI;QACFD,KAAI,CAACjG,OAAO,GAAG,IAAI;QACnBiG,KAAI,CAACvJ,KAAK,GAAG,EAAE;QAEf;QACA,MAAMyJ,iBAAiB,SAASF,KAAI,CAAC5B,eAAe,CAAC+B,oBAAoB,CAACH,KAAI,CAAChG,UAAU,CAAC,CAACoG,SAAS,EAAE;QACtGJ,KAAI,CAAC1I,QAAQ,GAAG4I,iBAAiB,IAAI,IAAI;QAEzC,IAAI,CAACF,KAAI,CAAC1I,QAAQ,EAAE;UAClB,MAAM+I,YAAY,SAASL,KAAI,CAAC5B,eAAe,CAACkC,WAAW,CAACN,KAAI,CAAChG,UAAU,CAAC,CAACoG,SAAS,EAAE;UACxFJ,KAAI,CAAC1I,QAAQ,GAAG+I,YAAY,IAAI,IAAI;;QAGtC,IAAI,CAACL,KAAI,CAAC1I,QAAQ,EAAE;UAClB0I,KAAI,CAACvJ,KAAK,GAAG,wDAAwD;;OAExE,CAAC,OAAOA,KAAK,EAAE;QACd8J,OAAO,CAAC9J,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/CuJ,KAAI,CAACvJ,KAAK,GAAG,0DAA0D;OACxE,SAAS;QACRuJ,KAAI,CAACjG,OAAO,GAAG,KAAK;;IACrB;EACH;EAEQ2F,oBAAoBA,CAAA;IAC1B,IAAI,CAACrB,eAAe,CAACmC,eAAe,EAAE,CACnCC,IAAI,CAAC,MAAK;MACTF,OAAO,CAACG,GAAG,CAAC,iCAAiC,CAAC;MAE9C;MACA,IAAI,CAAClC,aAAa,CAACmC,IAAI,CACrB,IAAI,CAACtC,eAAe,CAACuC,gBAAgB,CAACrB,SAAS,CAACsB,eAAe,IAAG;QAChE,IAAIA,eAAe,IAAI,IAAI,CAACvJ,QAAQ,IAAIuJ,eAAe,CAACC,EAAE,KAAK,IAAI,CAACxJ,QAAQ,CAACwJ,EAAE,EAAE;UAC/E,IAAI,CAACxJ,QAAQ,GAAG;YAAE,GAAG,IAAI,CAACA,QAAQ;YAAE,GAAGuJ;UAAe,CAAE;;MAE5D,CAAC,CAAC,CACH;IACH,CAAC,CAAC,CACDE,KAAK,CAACC,GAAG,IAAG;MACXT,OAAO,CAAC9J,KAAK,CAAC,wCAAwC,EAAEuK,GAAG,CAAC;IAC9D,CAAC,CAAC;EACN;EAEAC,aAAaA,CAAA;IACX,IAAI,IAAI,CAACjH,UAAU,CAACkH,IAAI,EAAE,EAAE;MAC1B,IAAI,CAACzK,KAAK,GAAG,EAAE,CAAC,CAAC;MACjB,IAAI,CAACgJ,YAAY,EAAE;MACnB,IAAI,CAACC,oBAAoB,EAAE;;EAE/B;EAEAyB,cAAcA,CAACjI,MAAsB;IACnC,OAAO,IAAI,CAACF,WAAW,CAACoI,SAAS,CAACC,IAAI,IAAIA,IAAI,CAAC5C,GAAG,KAAKvF,MAAM,CAAC;EAChE;EAEAtC,iBAAiBA,CAAC0K,SAAiB;IACjC,IAAI,CAAC,IAAI,CAAChK,QAAQ,EAAE,OAAO,KAAK;IAChC,MAAMiK,YAAY,GAAG,IAAI,CAACJ,cAAc,CAAC,IAAI,CAAC7J,QAAQ,CAAC4B,MAAM,CAAC;IAC9D,OAAOoI,SAAS,IAAIC,YAAY;EAClC;EAEAzK,cAAcA,CAACwK,SAAiB;IAC9B,IAAI,CAAC,IAAI,CAAChK,QAAQ,EAAE,OAAO,KAAK;IAChC,MAAMiK,YAAY,GAAG,IAAI,CAACJ,cAAc,CAAC,IAAI,CAAC7J,QAAQ,CAAC4B,MAAM,CAAC;IAC9D,OAAOoI,SAAS,KAAKC,YAAY;EACnC;EAEApI,aAAaA,CAACD,MAAsB;IAClC,QAAQA,MAAM;MACZ,KAAKvD,cAAc,CAAC+I,OAAO;QACzB,OAAO,YAAY;MACrB,KAAK/I,cAAc,CAACgJ,SAAS;QAC3B,OAAO,UAAU;MACnB,KAAKhJ,cAAc,CAACiJ,SAAS;QAC3B,OAAO,OAAO;MAChB,KAAKjJ,cAAc,CAAC6L,OAAO;QACzB,OAAO,SAAS;MAClB,KAAK7L,cAAc,CAAC8L,SAAS;QAC3B,OAAO,QAAQ;MACjB;QACE,OAAO,SAAS;;EAEtB;EAEAxI,cAAcA,CAACC,MAAsB;IACnC,QAAQA,MAAM;MACZ,KAAKvD,cAAc,CAAC+I,OAAO;QACzB,OAAO,SAAS;MAClB,KAAK/I,cAAc,CAACgJ,SAAS;QAC3B,OAAO,MAAM;MACf,KAAKhJ,cAAc,CAACiJ,SAAS;QAC3B,OAAO,SAAS;MAClB,KAAKjJ,cAAc,CAAC6L,OAAO;QACzB,OAAO,QAAQ;MACjB,KAAK7L,cAAc,CAAC8L,SAAS;QAC3B,OAAO,WAAW;MACpB;QACE,OAAO,WAAW;;EAExB;EAEApK,UAAUA,CAACqK,IAAmB;IAC5B,IAAI,CAACA,IAAI,EAAE,OAAO,YAAY;IAC9B,MAAMC,CAAC,GAAG,IAAIC,IAAI,CAACF,IAAI,CAAC;IACxB,OAAOC,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACnCC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE,SAAS;MACfC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;KACT,CAAC;EACJ;EAEAxI,yBAAyBA,CAAA;IACvB,IAAI,CAAC,IAAI,CAACpC,QAAQ,IAAI,CAAC,IAAI,CAACA,QAAQ,CAACqC,qBAAqB,EAAE;MAC1D,OAAO,gBAAgB;;IAGzB,MAAMwI,GAAG,GAAG,IAAIP,IAAI,EAAE;IACtB,MAAMQ,SAAS,GAAG,IAAIR,IAAI,CAAC,IAAI,CAACtK,QAAQ,CAACqC,qBAAqB,CAAC;IAC/D,MAAM0I,MAAM,GAAGD,SAAS,CAACE,OAAO,EAAE,GAAGH,GAAG,CAACG,OAAO,EAAE;IAElD,IAAID,MAAM,IAAI,CAAC,EAAE;MACf,OAAO,kBAAkB;;IAG3B,MAAME,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACJ,MAAM,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IACvD,MAAMK,WAAW,GAAGF,IAAI,CAACC,KAAK,CAAEJ,MAAM,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,IAAK,IAAI,GAAG,EAAE,CAAC,CAAC;IAEzE,IAAIE,SAAS,GAAG,CAAC,EAAE;MACjB,OAAO,GAAGA,SAAS,KAAKG,WAAW,KAAK;KACzC,MAAM;MACL,OAAO,GAAGA,WAAW,KAAK;;EAE9B;EAEA;EACAlD,YAAYA,CAACmD,GAAW;IACtB,IAAI,CAACpE,SAAS,GAAGoE,GAAG;IAEpB,IAAIA,GAAG,KAAK,QAAQ,IAAI,IAAI,CAACxG,MAAM,CAACC,MAAM,KAAK,CAAC,EAAE;MAChD,IAAI,CAACP,UAAU,EAAE;;EAErB;EAEA;EACA+G,MAAMA,CAAA;IACJ,IAAI,CAACtE,WAAW,CAACsE,MAAM,EAAE,CAACrD,SAAS,CAAC;MAClCsD,IAAI,EAAEA,CAAA,KAAK;QACT;QACAC,UAAU,CAAC,MAAK;UACd,IAAI,CAAC5E,MAAM,CAAC6E,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;QAClC,CAAC,EAAE,GAAG,CAAC;MACT,CAAC;MACDtM,KAAK,EAAGA,KAAK,IAAI;QACf8J,OAAO,CAAC9J,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;QACrC;QACAqM,UAAU,CAAC,MAAK;UACd,IAAI,CAAC5E,MAAM,CAAC6E,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;QAClC,CAAC,EAAE,GAAG,CAAC;MACT;KACD,CAAC;EACJ;EAEA;EACMlH,UAAUA,CAAA;IAAA,IAAAmH,MAAA;IAAA,OAAA/C,iBAAA;MACd,IAAI,CAAC+C,MAAI,CAAC5M,WAAW,EAAE;MAEvB,IAAI;QACF4M,MAAI,CAAC9G,aAAa,GAAG,IAAI;QACzB;QACA8G,MAAI,CAAC7G,MAAM,GAAG,OAAM6G,MAAI,CAAC5E,eAAe,CAAC6E,uBAAuB,CAACD,MAAI,CAAC5M,WAAW,CAAC0K,EAAE,CAAC,CAACV,SAAS,EAAE,KAAI,EAAE;QACvG4C,MAAI,CAACtH,YAAY,EAAE;OACpB,CAAC,OAAOjF,KAAK,EAAE;QACd8J,OAAO,CAAC9J,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;OAC9C,SAAS;QACRuM,MAAI,CAAC9G,aAAa,GAAG,KAAK;;IAC3B;EACH;EAEAR,YAAYA,CAAA;IACV,IAAI,IAAI,CAACH,WAAW,KAAK,KAAK,EAAE;MAC9B,IAAI,CAACL,cAAc,GAAG,CAAC,GAAG,IAAI,CAACiB,MAAM,CAAC;KACvC,MAAM;MACL,IAAI,CAACjB,cAAc,GAAG,IAAI,CAACiB,MAAM,CAAC+G,MAAM,CAACC,KAAK,IAAG;QAC/C,QAAQ,IAAI,CAAC5H,WAAW;UACtB,KAAK,SAAS;YACZ,OAAO4H,KAAK,CAACjK,MAAM,KAAKvD,cAAc,CAAC+I,OAAO;UAChD,KAAK,YAAY;YACf,OAAOyE,KAAK,CAACjK,MAAM,KAAKvD,cAAc,CAACgJ,SAAS;UAClD,KAAK,WAAW;YACd,OAAOwE,KAAK,CAACjK,MAAM,KAAKvD,cAAc,CAACiJ,SAAS;UAClD,KAAK,WAAW;YACd,OAAOuE,KAAK,CAACjK,MAAM,KAAKvD,cAAc,CAAC8L,SAAS;UAClD;YACE,OAAO,IAAI;;MAEjB,CAAC,CAAC;;EAEN;EAEA9G,gBAAgBA,CAACwI,KAAe;IAC9B;IACA,IAAI,CAACnJ,UAAU,GAAGmJ,KAAK,CAAC/J,OAAO;IAC/B,IAAI,CAACoG,YAAY,CAAC,UAAU,CAAC;IAC7B,IAAI,CAACyB,aAAa,EAAE;EACtB;EAEAnG,UAAUA,CAAC1B,OAAe;IACxB,IAAI,CAACY,UAAU,GAAGZ,OAAO;IACzB,IAAI,CAACoG,YAAY,CAAC,UAAU,CAAC;IAC7B,IAAI,CAACyB,aAAa,EAAE;EACtB;EAEA;EACA7B,qBAAqBA,CAAA;IACnB,IAAI,IAAI,CAAChJ,WAAW,EAAE;MACpB,IAAI,CAACoH,WAAW,CAAC4F,UAAU,CAAC;QAC1B/M,SAAS,EAAE,IAAI,CAACD,WAAW,CAACC,SAAS;QACrCC,QAAQ,EAAE,IAAI,CAACF,WAAW,CAACE,QAAQ;QACnCyI,KAAK,EAAE,IAAI,CAAC3I,WAAW,CAAC2I,KAAK;QAC7BC,KAAK,EAAE,IAAI,CAAC5I,WAAW,CAAC4I,KAAK,IAAI,EAAE;QACnCC,WAAW,EAAE,IAAI,CAAC7I,WAAW,CAAC6I,WAAW,IAAI,EAAE;QAC/C3F,OAAO,EAAE,IAAI,CAAClD,WAAW,CAACkD,OAAO,IAAI;OACtC,CAAC;;EAEN;EAEMkD,aAAaA,CAAA;IAAA,IAAA6G,MAAA;IAAA,OAAApD,iBAAA;MACjB,IAAI,CAACoD,MAAI,CAAC7F,WAAW,CAACC,KAAK,IAAI,CAAC4F,MAAI,CAACjN,WAAW,EAAE;MAElD,IAAI;QACFiN,MAAI,CAAC3F,eAAe,GAAG,IAAI;QAC3B,MAAM4F,QAAQ,GAAGD,MAAI,CAAC7F,WAAW,CAAC+F,KAAK;QAEvC;QACA,MAAMF,MAAI,CAAC/E,WAAW,CAAC9B,aAAa,CAAC;UACnC,GAAG6G,MAAI,CAACjN,WAAW;UACnB,GAAGkN;SACJ,CAAC,CAAClD,SAAS,EAAE;QAEd;QACAiD,MAAI,CAACjN,WAAW,GAAG;UAAE,GAAGiN,MAAI,CAACjN,WAAW;UAAE,GAAGkN;QAAQ,CAAE;QAEvD;QACA/C,OAAO,CAACG,GAAG,CAAC,8BAA8B,CAAC;OAC5C,CAAC,OAAOjK,KAAK,EAAE;QACd8J,OAAO,CAAC9J,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;OAChD,SAAS;QACR4M,MAAI,CAAC3F,eAAe,GAAG,KAAK;;IAC7B;EACH;EAEAf,YAAYA,CAAA;IACV,IAAI,CAACyC,qBAAqB,EAAE;EAC9B;EAEA;EACApC,cAAcA,CAAA;IACZ;IACAuD,OAAO,CAACG,GAAG,CAAC,yBAAyB,CAAC;EACxC;EAEAvD,mBAAmBA,CAAA;IACjB;IACAoD,OAAO,CAACG,GAAG,CAAC,8BAA8B,CAAC;EAC7C;EAEApD,YAAYA,CAAA;IACV;IACAiD,OAAO,CAACG,GAAG,CAAC,uBAAuB,CAAC;EACtC;EAEA;EACMrB,aAAaA,CAAA;IAAA,IAAAmE,MAAA;IAAA,OAAAvD,iBAAA;MACjB,IAAI,CAACuD,MAAI,CAACpN,WAAW,EAAE;MAEvB,IAAI;QACF;QACA,MAAM+F,MAAM,GAAG,OAAMqH,MAAI,CAACpF,eAAe,CAAC6E,uBAAuB,CAACO,MAAI,CAACpN,WAAW,CAAC0K,EAAE,CAAC,CAACV,SAAS,EAAE,KAAI,EAAE;QAExGoD,MAAI,CAAC7F,SAAS,GAAG;UACfC,WAAW,EAAEzB,MAAM,CAACC,MAAM;UAC1ByB,eAAe,EAAE1B,MAAM,CAAC+G,MAAM,CAACO,CAAC,IAAIA,CAAC,CAACvK,MAAM,KAAKvD,cAAc,CAACiJ,SAAS,CAAC,CAACxC,MAAM;UACjF0B,aAAa,EAAE0F,MAAI,CAACE,sBAAsB,CAACvH,MAAM;SAClD;OACF,CAAC,OAAO1F,KAAK,EAAE;QACd8J,OAAO,CAAC9J,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;;IAClD;EACH;EAEQiN,sBAAsBA,CAACvH,MAAkB;IAC/C,MAAMwH,WAAW,GAAGxH,MAAM,CAAC+G,MAAM,CAACO,CAAC,IAAIA,CAAC,CAAC3L,cAAc,IAAI2L,CAAC,CAAC3L,cAAc,GAAG,CAAC,CAAC;IAChF,IAAI6L,WAAW,CAACvH,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC;IAEtC,MAAMwH,WAAW,GAAGD,WAAW,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEX,KAAK,KAAKW,GAAG,IAAIX,KAAK,CAACrL,cAAc,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5F,OAAO0K,IAAI,CAACuB,KAAK,CAAEH,WAAW,GAAGD,WAAW,CAACvH,MAAM,GAAI,EAAE,CAAC,GAAG,EAAE;EACjE;;;uBAjYW2B,uBAAuB,EAAAnI,EAAA,CAAAoO,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAtO,EAAA,CAAAoO,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAAvO,EAAA,CAAAoO,iBAAA,CAAAI,EAAA,CAAAC,WAAA,GAAAzO,EAAA,CAAAoO,iBAAA,CAAAM,EAAA,CAAAC,eAAA,GAAA3O,EAAA,CAAAoO,iBAAA,CAAAQ,EAAA,CAAAC,eAAA,GAAA7O,EAAA,CAAAoO,iBAAA,CAAAU,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAvB5G,uBAAuB;MAAA6G,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCdpCtP,EAAA,CAAAC,cAAA,aAA6B;UAQfD,EAAA,CAAAE,SAAA,WAAsC;UACtCF,EAAA,CAAAG,MAAA,sBACF;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAEPJ,EAAA,CAAAC,cAAA,aAAkC;UAM5BD,EAAA,CAAAqE,UAAA,2BAAAmL,iEAAAhK,MAAA;YAAA,OAAA+J,GAAA,CAAAnL,UAAA,GAAAoB,MAAA;UAAA,EAAwB,yBAAAiK,+DAAA;YAAA,OACTF,GAAA,CAAAlE,aAAA,EAAe;UAAA,EADN;UAJ1BrL,EAAA,CAAAI,YAAA,EAMC;UACDJ,EAAA,CAAAC,cAAA,kBAIC;UADCD,EAAA,CAAAqE,UAAA,mBAAAqL,0DAAA;YAAA,OAASH,GAAA,CAAAlE,aAAA,EAAe;UAAA,EAAC;UAEzBrL,EAAA,CAAAE,SAAA,aAAkC;UACpCF,EAAA,CAAAI,YAAA,EAAS;UAGbJ,EAAA,CAAAC,cAAA,eAAkC;UAG9BD,EAAA,CAAAmC,UAAA,KAAAwN,uCAAA,kBAGM;UAGN3P,EAAA,CAAAC,cAAA,eAAoC;UAKhCD,EAAA,CAAAqE,UAAA,mBAAAuL,0DAAA;YAAA,OAASL,GAAA,CAAA3F,YAAA,CAAa,UAAU,CAAC;UAAA,EAAC;UAElC5J,EAAA,CAAAE,SAAA,aAAuC;UACvCF,EAAA,CAAAG,MAAA,eACF;UAAAH,EAAA,CAAAI,YAAA,EAAS;UACTJ,EAAA,CAAAC,cAAA,kBAKC;UADCD,EAAA,CAAAqE,UAAA,mBAAAwL,0DAAA;YAAA,OAASN,GAAA,CAAA3F,YAAA,CAAa,QAAQ,CAAC;UAAA,EAAC;UAEhC5J,EAAA,CAAAE,SAAA,aAAqC;UACrCF,EAAA,CAAAG,MAAA,mBACF;UAAAH,EAAA,CAAAI,YAAA,EAAS;UACTJ,EAAA,CAAAC,cAAA,kBAKC;UADCD,EAAA,CAAAqE,UAAA,mBAAAyL,0DAAA;YAAA,OAASP,GAAA,CAAA3F,YAAA,CAAa,SAAS,CAAC;UAAA,EAAC;UAEjC5J,EAAA,CAAAE,SAAA,aAAqC;UACrCF,EAAA,CAAAG,MAAA,gBACF;UAAAH,EAAA,CAAAI,YAAA,EAAS;UAIXJ,EAAA,CAAAC,cAAA,kBAIC;UAFCD,EAAA,CAAAqE,UAAA,mBAAA0L,0DAAA;YAAA,OAASR,GAAA,CAAAvC,MAAA,EAAQ;UAAA,EAAC;UAGlBhN,EAAA,CAAAE,SAAA,aAA6C;UAC7CF,EAAA,CAAAG,MAAA,0BACF;UAAAH,EAAA,CAAAI,YAAA,EAAS;UAOnBJ,EAAA,CAAAC,cAAA,cAAuB;UAErBD,EAAA,CAAAmC,UAAA,KAAA6N,uCAAA,kBAKM;UAGNhQ,EAAA,CAAAmC,UAAA,KAAA8N,uCAAA,kBAGM;UAKNjQ,EAAA,CAAAmC,UAAA,KAAA+N,uCAAA,kBAuNM;UAGNlQ,EAAA,CAAAmC,UAAA,KAAAgO,uCAAA,mBAuFM;UAGNnQ,EAAA,CAAAmC,UAAA,KAAAiO,uCAAA,mBAsHM;UACRpQ,EAAA,CAAAI,YAAA,EAAM;;;UA/fMJ,EAAA,CAAAK,SAAA,IAAwB;UAAxBL,EAAA,CAAAqC,UAAA,YAAAkN,GAAA,CAAAnL,UAAA,CAAwB;UAecpE,EAAA,CAAAK,SAAA,GAAiB;UAAjBL,EAAA,CAAAqC,UAAA,SAAAkN,GAAA,CAAA/O,WAAA,CAAiB;UAUrDR,EAAA,CAAAK,SAAA,GAAyC;UAAzCL,EAAA,CAAAc,WAAA,WAAAyO,GAAA,CAAA5G,SAAA,gBAAyC;UASzC3I,EAAA,CAAAK,SAAA,GAAuC;UAAvCL,EAAA,CAAAc,WAAA,WAAAyO,GAAA,CAAA5G,SAAA,cAAuC;UASvC3I,EAAA,CAAAK,SAAA,GAAwC;UAAxCL,EAAA,CAAAc,WAAA,WAAAyO,GAAA,CAAA5G,SAAA,eAAwC;UAyB9C3I,EAAA,CAAAK,SAAA,GAAa;UAAbL,EAAA,CAAAqC,UAAA,SAAAkN,GAAA,CAAApL,OAAA,CAAa;UAQbnE,EAAA,CAAAK,SAAA,GAAuB;UAAvBL,EAAA,CAAAqC,UAAA,SAAAkN,GAAA,CAAA1O,KAAA,KAAA0O,GAAA,CAAApL,OAAA,CAAuB;UAQvBnE,EAAA,CAAAK,SAAA,GAA8B;UAA9BL,EAAA,CAAAqC,UAAA,SAAAkN,GAAA,CAAA5G,SAAA,gBAA8B;UA0N9B3I,EAAA,CAAAK,SAAA,GAA4B;UAA5BL,EAAA,CAAAqC,UAAA,SAAAkN,GAAA,CAAA5G,SAAA,cAA4B;UA0F5B3I,EAAA,CAAAK,SAAA,GAA6B;UAA7BL,EAAA,CAAAqC,UAAA,SAAAkN,GAAA,CAAA5G,SAAA,eAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}