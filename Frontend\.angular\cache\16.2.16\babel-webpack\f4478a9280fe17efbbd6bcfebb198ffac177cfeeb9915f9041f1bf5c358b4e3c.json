{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport { DeliveryStatus, DeliveryPriority } from '@core/models/delivery.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@core/services/driver.service\";\nimport * as i3 from \"@angular/common\";\nfunction DeliveryFormComponent_option_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const driver_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", driver_r4.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", driver_r4.name, \" - \", driver_r4.vehicleType, \" \");\n  }\n}\nfunction DeliveryFormComponent_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵelement(1, \"i\", 37);\n    i0.ɵɵtext(2, \" Chargement des livreurs... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DeliveryFormComponent_option_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r5.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", option_r5.label, \" \");\n  }\n}\nfunction DeliveryFormComponent_option_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r6.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", option_r6.label, \" \");\n  }\n}\nexport class DeliveryFormComponent {\n  constructor(fb, driverService) {\n    this.fb = fb;\n    this.driverService = driverService;\n    this.delivery = null;\n    this.submitted = new EventEmitter();\n    this.cancelled = new EventEmitter();\n    this.drivers = [];\n    this.loading = false;\n    // Enum options for dropdowns\n    this.priorityOptions = [{\n      value: DeliveryPriority.Low,\n      label: 'Basse'\n    }, {\n      value: DeliveryPriority.Medium,\n      label: 'Moyenne'\n    }, {\n      value: DeliveryPriority.High,\n      label: 'Haute'\n    }, {\n      value: DeliveryPriority.Urgent,\n      label: 'Urgente'\n    }];\n    this.statusOptions = [{\n      value: DeliveryStatus.Pending,\n      label: 'En attente'\n    }, {\n      value: DeliveryStatus.InTransit,\n      label: 'En cours'\n    }, {\n      value: DeliveryStatus.Delivered,\n      label: 'Livré'\n    }, {\n      value: DeliveryStatus.Delayed,\n      label: 'Retardé'\n    }, {\n      value: DeliveryStatus.Cancelled,\n      label: 'Annulé'\n    }];\n    this.deliveryForm = this.fb.group({\n      orderId: ['', Validators.required],\n      customerId: [''],\n      customerName: ['', Validators.required],\n      address: ['', Validators.required],\n      phoneNumber: ['', Validators.required],\n      driverId: ['', Validators.required],\n      estimatedDeliveryTime: ['', Validators.required],\n      priority: [DeliveryPriority.Medium, Validators.required],\n      status: [DeliveryStatus.Pending, Validators.required],\n      notes: [''],\n      latitude: [48.8566, [Validators.required, Validators.min(-90), Validators.max(90)]],\n      longitude: [2.3522, [Validators.required, Validators.min(-180), Validators.max(180)]]\n    });\n  }\n  ngOnInit() {\n    this.loadDrivers();\n    if (this.delivery) {\n      this.deliveryForm.patchValue({\n        orderId: this.delivery.orderId,\n        customerId: this.delivery.customerId || '',\n        customerName: this.delivery.customerName,\n        address: this.delivery.address,\n        phoneNumber: this.delivery.customerFeedback || '',\n        driverId: this.delivery.driverId,\n        estimatedDeliveryTime: this.formatDateForInput(this.delivery.estimatedDeliveryTime),\n        priority: this.delivery.priority,\n        status: this.delivery.status,\n        notes: this.delivery.notes || '',\n        latitude: this.delivery.coordinates?.latitude || 48.8566,\n        longitude: this.delivery.coordinates?.longitude || 2.3522\n      });\n    } else {\n      // Set default values for new delivery\n      this.deliveryForm.patchValue({\n        orderId: this.generateOrderId(),\n        customerId: 'customer-001',\n        status: DeliveryStatus.Pending,\n        priority: DeliveryPriority.Medium\n      });\n    }\n  }\n  loadDrivers() {\n    this.loading = true;\n    this.driverService.getDrivers().subscribe({\n      next: drivers => {\n        this.drivers = drivers;\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error loading drivers:', error);\n        this.loading = false;\n      }\n    });\n  }\n  generateOrderId() {\n    const timestamp = Date.now().toString().slice(-6);\n    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');\n    return `ORD-${timestamp}${random}`;\n  }\n  formatDateForInput(date) {\n    const d = new Date(date);\n    return d.toISOString().slice(0, 16); // Format for datetime-local input\n  }\n\n  onSubmit() {\n    if (this.deliveryForm.valid) {\n      const formValue = this.deliveryForm.value;\n      // Map form data to delivery object expected by backend\n      const deliveryData = {\n        id: '',\n        orderId: formValue.orderId,\n        customerId: formValue.customerId,\n        customerName: formValue.customerName,\n        address: formValue.address,\n        status: formValue.status,\n        driverId: formValue.driverId,\n        driverName: this.getDriverName(formValue.driverId),\n        estimatedDeliveryTime: new Date(formValue.estimatedDeliveryTime).toISOString(),\n        priority: formValue.priority,\n        coordinates: {\n          latitude: parseFloat(formValue.latitude),\n          longitude: parseFloat(formValue.longitude)\n        },\n        notes: formValue.notes || '',\n        createdAt: this.delivery?.createdAt || new Date().toISOString()\n      };\n      this.submitted.emit(deliveryData);\n    }\n  }\n  getDriverName(driverId) {\n    const driver = this.drivers.find(d => d.id === driverId);\n    return driver ? driver.name : '';\n  }\n  onCancel() {\n    this.cancelled.emit();\n  }\n  static {\n    this.ɵfac = function DeliveryFormComponent_Factory(t) {\n      return new (t || DeliveryFormComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.DriverService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DeliveryFormComponent,\n      selectors: [[\"app-delivery-form\"]],\n      inputs: {\n        delivery: \"delivery\"\n      },\n      outputs: {\n        submitted: \"submitted\",\n        cancelled: \"cancelled\"\n      },\n      decls: 76,\n      vars: 21,\n      consts: [[3, \"formGroup\", \"ngSubmit\"], [1, \"row\"], [1, \"col-md-6\"], [1, \"mb-3\"], [\"for\", \"orderId\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"orderId\", \"formControlName\", \"orderId\", \"readonly\", \"\", 1, \"form-control\"], [\"for\", \"customerName\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"customerName\", \"formControlName\", \"customerName\", 1, \"form-control\"], [1, \"invalid-feedback\"], [\"for\", \"address\", 1, \"form-label\"], [\"id\", \"address\", \"formControlName\", \"address\", \"rows\", \"2\", 1, \"form-control\"], [\"for\", \"phoneNumber\", 1, \"form-label\"], [\"type\", \"tel\", \"id\", \"phoneNumber\", \"formControlName\", \"phoneNumber\", 1, \"form-control\"], [\"for\", \"estimatedDeliveryTime\", 1, \"form-label\"], [\"type\", \"datetime-local\", \"id\", \"estimatedDeliveryTime\", \"formControlName\", \"estimatedDeliveryTime\", 1, \"form-control\"], [\"for\", \"driverId\", 1, \"form-label\"], [\"id\", \"driverId\", \"formControlName\", \"driverId\", 1, \"form-select\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"form-text\", 4, \"ngIf\"], [\"for\", \"priority\", 1, \"form-label\"], [\"id\", \"priority\", \"formControlName\", \"priority\", 1, \"form-select\"], [\"for\", \"status\", 1, \"form-label\"], [\"id\", \"status\", \"formControlName\", \"status\", 1, \"form-select\"], [1, \"form-label\"], [1, \"col-6\"], [\"type\", \"number\", \"placeholder\", \"Latitude\", \"formControlName\", \"latitude\", \"step\", \"0.000001\", 1, \"form-control\"], [\"type\", \"number\", \"placeholder\", \"Longitude\", \"formControlName\", \"longitude\", \"step\", \"0.000001\", 1, \"form-control\"], [1, \"form-text\", \"text-muted\"], [\"for\", \"notes\", 1, \"form-label\"], [\"id\", \"notes\", \"formControlName\", \"notes\", \"rows\", \"3\", \"placeholder\", \"Instructions sp\\u00E9ciales, commentaires...\", 1, \"form-control\"], [1, \"d-flex\", \"gap-2\", \"justify-content-end\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"disabled\"], [1, \"fa-solid\", \"fa-save\", \"me-2\"], [3, \"value\"], [1, \"form-text\"], [1, \"fa-solid\", \"fa-spinner\", \"fa-spin\", \"me-1\"]],\n      template: function DeliveryFormComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"form\", 0);\n          i0.ɵɵlistener(\"ngSubmit\", function DeliveryFormComponent_Template_form_ngSubmit_0_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"label\", 4);\n          i0.ɵɵtext(5, \"ID Commande\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(6, \"input\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"div\", 3)(8, \"label\", 6);\n          i0.ɵɵtext(9, \"Nom du client *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(10, \"input\", 7);\n          i0.ɵɵelementStart(11, \"div\", 8);\n          i0.ɵɵtext(12, \" Le nom du client est requis \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"div\", 3)(14, \"label\", 9);\n          i0.ɵɵtext(15, \"Adresse de livraison *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(16, \"textarea\", 10);\n          i0.ɵɵelementStart(17, \"div\", 8);\n          i0.ɵɵtext(18, \" L'adresse de livraison est requise \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"div\", 3)(20, \"label\", 11);\n          i0.ɵɵtext(21, \"T\\u00E9l\\u00E9phone *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(22, \"input\", 12);\n          i0.ɵɵelementStart(23, \"div\", 8);\n          i0.ɵɵtext(24, \" Le num\\u00E9ro de t\\u00E9l\\u00E9phone est requis \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"div\", 3)(26, \"label\", 13);\n          i0.ɵɵtext(27, \"Date et heure pr\\u00E9vues *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(28, \"input\", 14);\n          i0.ɵɵelementStart(29, \"div\", 8);\n          i0.ɵɵtext(30, \" La date et heure de livraison sont requises \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(31, \"div\", 2)(32, \"div\", 3)(33, \"label\", 15);\n          i0.ɵɵtext(34, \"Livreur assign\\u00E9 *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"select\", 16)(36, \"option\", 17);\n          i0.ɵɵtext(37, \"S\\u00E9lectionner un livreur\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(38, DeliveryFormComponent_option_38_Template, 2, 3, \"option\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"div\", 8);\n          i0.ɵɵtext(40, \" Un livreur doit \\u00EAtre assign\\u00E9 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(41, DeliveryFormComponent_div_41_Template, 3, 0, \"div\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"div\", 3)(43, \"label\", 20);\n          i0.ɵɵtext(44, \"Priorit\\u00E9 *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"select\", 21);\n          i0.ɵɵtemplate(46, DeliveryFormComponent_option_46_Template, 2, 2, \"option\", 18);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(47, \"div\", 3)(48, \"label\", 22);\n          i0.ɵɵtext(49, \"Statut *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"select\", 23);\n          i0.ɵɵtemplate(51, DeliveryFormComponent_option_51_Template, 2, 2, \"option\", 18);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(52, \"div\", 3)(53, \"label\", 24);\n          i0.ɵɵtext(54, \"Coordonn\\u00E9es GPS\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"div\", 1)(56, \"div\", 25);\n          i0.ɵɵelement(57, \"input\", 26);\n          i0.ɵɵelementStart(58, \"div\", 8);\n          i0.ɵɵtext(59, \" Latitude invalide \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(60, \"div\", 25);\n          i0.ɵɵelement(61, \"input\", 27);\n          i0.ɵɵelementStart(62, \"div\", 8);\n          i0.ɵɵtext(63, \" Longitude invalide \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(64, \"small\", 28);\n          i0.ɵɵtext(65, \" Coordonn\\u00E9es par d\\u00E9faut: Paris (48.8566, 2.3522) \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(66, \"div\", 3)(67, \"label\", 29);\n          i0.ɵɵtext(68, \"Notes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(69, \"textarea\", 30);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(70, \"div\", 31)(71, \"button\", 32);\n          i0.ɵɵlistener(\"click\", function DeliveryFormComponent_Template_button_click_71_listener() {\n            return ctx.onCancel();\n          });\n          i0.ɵɵtext(72, \"Annuler\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"button\", 33);\n          i0.ɵɵelement(74, \"i\", 34);\n          i0.ɵɵtext(75);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          let tmp_1_0;\n          let tmp_2_0;\n          let tmp_3_0;\n          let tmp_4_0;\n          let tmp_5_0;\n          let tmp_10_0;\n          let tmp_11_0;\n          i0.ɵɵproperty(\"formGroup\", ctx.deliveryForm);\n          i0.ɵɵadvance(10);\n          i0.ɵɵclassProp(\"is-invalid\", ((tmp_1_0 = ctx.deliveryForm.get(\"customerName\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx.deliveryForm.get(\"customerName\")) == null ? null : tmp_1_0.touched));\n          i0.ɵɵadvance(6);\n          i0.ɵɵclassProp(\"is-invalid\", ((tmp_2_0 = ctx.deliveryForm.get(\"address\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.deliveryForm.get(\"address\")) == null ? null : tmp_2_0.touched));\n          i0.ɵɵadvance(6);\n          i0.ɵɵclassProp(\"is-invalid\", ((tmp_3_0 = ctx.deliveryForm.get(\"phoneNumber\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx.deliveryForm.get(\"phoneNumber\")) == null ? null : tmp_3_0.touched));\n          i0.ɵɵadvance(6);\n          i0.ɵɵclassProp(\"is-invalid\", ((tmp_4_0 = ctx.deliveryForm.get(\"estimatedDeliveryTime\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx.deliveryForm.get(\"estimatedDeliveryTime\")) == null ? null : tmp_4_0.touched));\n          i0.ɵɵadvance(7);\n          i0.ɵɵclassProp(\"is-invalid\", ((tmp_5_0 = ctx.deliveryForm.get(\"driverId\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx.deliveryForm.get(\"driverId\")) == null ? null : tmp_5_0.touched));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.drivers);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.priorityOptions);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.statusOptions);\n          i0.ɵɵadvance(6);\n          i0.ɵɵclassProp(\"is-invalid\", ((tmp_10_0 = ctx.deliveryForm.get(\"latitude\")) == null ? null : tmp_10_0.invalid) && ((tmp_10_0 = ctx.deliveryForm.get(\"latitude\")) == null ? null : tmp_10_0.touched));\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"is-invalid\", ((tmp_11_0 = ctx.deliveryForm.get(\"longitude\")) == null ? null : tmp_11_0.invalid) && ((tmp_11_0 = ctx.deliveryForm.get(\"longitude\")) == null ? null : tmp_11_0.touched));\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"disabled\", !ctx.deliveryForm.valid || ctx.loading);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.delivery ? \"Mettre \\u00E0 jour\" : \"Cr\\u00E9er la livraison\", \" \");\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["EventEmitter", "Validators", "DeliveryStatus", "DeliveryPriority", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "driver_r4", "id", "ɵɵadvance", "ɵɵtextInterpolate2", "name", "vehicleType", "ɵɵelement", "option_r5", "value", "ɵɵtextInterpolate1", "label", "option_r6", "DeliveryFormComponent", "constructor", "fb", "driverService", "delivery", "submitted", "cancelled", "drivers", "loading", "priorityOptions", "Low", "Medium", "High", "<PERSON><PERSON>", "statusOptions", "Pending", "InTransit", "Delivered", "Delayed", "Cancelled", "deliveryForm", "group", "orderId", "required", "customerId", "customerName", "address", "phoneNumber", "driverId", "estimatedDeliveryTime", "priority", "status", "notes", "latitude", "min", "max", "longitude", "ngOnInit", "loadDrivers", "patchValue", "customerFeedback", "formatDateForInput", "coordinates", "generateOrderId", "getDrivers", "subscribe", "next", "error", "console", "timestamp", "Date", "now", "toString", "slice", "random", "Math", "floor", "padStart", "date", "d", "toISOString", "onSubmit", "valid", "formValue", "deliveryData", "<PERSON><PERSON><PERSON>", "getDriverName", "parseFloat", "createdAt", "emit", "driver", "find", "onCancel", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "DriverService", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "DeliveryFormComponent_Template", "rf", "ctx", "ɵɵlistener", "DeliveryFormComponent_Template_form_ngSubmit_0_listener", "ɵɵtemplate", "DeliveryFormComponent_option_38_Template", "DeliveryFormComponent_div_41_Template", "DeliveryFormComponent_option_46_Template", "DeliveryFormComponent_option_51_Template", "DeliveryFormComponent_Template_button_click_71_listener", "ɵɵclassProp", "tmp_1_0", "get", "invalid", "touched", "tmp_2_0", "tmp_3_0", "tmp_4_0", "tmp_5_0", "tmp_10_0", "tmp_11_0"], "sources": ["C:\\Users\\<USER>\\delivery-dash-optimiser\\frontend\\src\\app\\shared\\components\\delivery-form\\delivery-form.component.ts", "C:\\Users\\<USER>\\delivery-dash-optimiser\\frontend\\src\\app\\shared\\components\\delivery-form\\delivery-form.component.html"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Delivery, DeliveryStatus, DeliveryPriority } from '@core/models/delivery.model';\nimport { DriverService } from '@core/services/driver.service';\nimport { Driver } from '@core/models/driver.model';\n\n@Component({\n  selector: 'app-delivery-form',\n  templateUrl: './delivery-form.component.html',\n  styleUrls: ['./delivery-form.component.scss']\n})\nexport class DeliveryFormComponent implements OnInit {\n  @Input() delivery: Delivery | null = null;\n  @Output() submitted = new EventEmitter<any>();\n  @Output() cancelled = new EventEmitter<void>();\n\n  deliveryForm: FormGroup;\n  drivers: Driver[] = [];\n  loading = false;\n\n  // Enum options for dropdowns\n  priorityOptions = [\n    { value: DeliveryPriority.Low, label: 'Basse' },\n    { value: DeliveryPriority.Medium, label: 'Moyenne' },\n    { value: DeliveryPriority.High, label: 'Haute' },\n    { value: DeliveryPriority.Urgent, label: 'Urgente' }\n  ];\n\n  statusOptions = [\n    { value: DeliveryStatus.Pending, label: 'En attente' },\n    { value: DeliveryStatus.InTransit, label: 'En cours' },\n    { value: DeliveryStatus.Delivered, label: 'Livré' },\n    { value: DeliveryStatus.Delayed, label: 'Retardé' },\n    { value: DeliveryStatus.Cancelled, label: 'Annulé' }\n  ];\n\n  constructor(\n    private fb: FormBuilder,\n    private driverService: DriverService\n  ) {\n    this.deliveryForm = this.fb.group({\n      orderId: ['', Validators.required],\n      customerId: [''],\n      customerName: ['', Validators.required],\n      address: ['', Validators.required],\n      phoneNumber: ['', Validators.required],\n      driverId: ['', Validators.required],\n      estimatedDeliveryTime: ['', Validators.required],\n      priority: [DeliveryPriority.Medium, Validators.required],\n      status: [DeliveryStatus.Pending, Validators.required],\n      notes: [''],\n      latitude: [48.8566, [Validators.required, Validators.min(-90), Validators.max(90)]],\n      longitude: [2.3522, [Validators.required, Validators.min(-180), Validators.max(180)]]\n    });\n  }\n\n  ngOnInit() {\n    this.loadDrivers();\n\n    if (this.delivery) {\n      this.deliveryForm.patchValue({\n        orderId: this.delivery.orderId,\n        customerId: this.delivery.customerId || '',\n        customerName: this.delivery.customerName,\n        address: this.delivery.address,\n        phoneNumber: this.delivery.customerFeedback || '', // Using available field\n        driverId: this.delivery.driverId,\n        estimatedDeliveryTime: this.formatDateForInput(this.delivery.estimatedDeliveryTime),\n        priority: this.delivery.priority,\n        status: this.delivery.status,\n        notes: this.delivery.notes || '',\n        latitude: this.delivery.coordinates?.latitude || 48.8566,\n        longitude: this.delivery.coordinates?.longitude || 2.3522\n      });\n    } else {\n      // Set default values for new delivery\n      this.deliveryForm.patchValue({\n        orderId: this.generateOrderId(),\n        customerId: 'customer-001', // Default customer ID\n        status: DeliveryStatus.Pending,\n        priority: DeliveryPriority.Medium\n      });\n    }\n  }\n\n  private loadDrivers(): void {\n    this.loading = true;\n    this.driverService.getDrivers().subscribe({\n      next: (drivers) => {\n        this.drivers = drivers;\n        this.loading = false;\n      },\n      error: (error) => {\n        console.error('Error loading drivers:', error);\n        this.loading = false;\n      }\n    });\n  }\n\n  private generateOrderId(): string {\n    const timestamp = Date.now().toString().slice(-6);\n    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');\n    return `ORD-${timestamp}${random}`;\n  }\n\n  private formatDateForInput(date: Date | string): string {\n    const d = new Date(date);\n    return d.toISOString().slice(0, 16); // Format for datetime-local input\n  }\n\n  onSubmit() {\n    if (this.deliveryForm.valid) {\n      const formValue = this.deliveryForm.value;\n\n      // Map form data to delivery object expected by backend\n      const deliveryData = {\n        id: '', // Always empty for new deliveries - backend will generate\n        orderId: formValue.orderId,\n        customerId: formValue.customerId,\n        customerName: formValue.customerName,\n        address: formValue.address,\n        status: formValue.status,\n        driverId: formValue.driverId,\n        driverName: this.getDriverName(formValue.driverId),\n        estimatedDeliveryTime: new Date(formValue.estimatedDeliveryTime).toISOString(),\n        priority: formValue.priority,\n        coordinates: {\n          latitude: parseFloat(formValue.latitude),\n          longitude: parseFloat(formValue.longitude)\n        },\n        notes: formValue.notes || '',\n        createdAt: this.delivery?.createdAt || new Date().toISOString()\n      };\n\n      this.submitted.emit(deliveryData);\n    }\n  }\n\n  private getDriverName(driverId: string): string {\n    const driver = this.drivers.find(d => d.id === driverId);\n    return driver ? driver.name : '';\n  }\n\n  onCancel() {\n    this.cancelled.emit();\n  }\n}\n", "<form [formGroup]=\"deliveryForm\" (ngSubmit)=\"onSubmit()\">\n  <div class=\"row\">\n    <!-- Left Column -->\n    <div class=\"col-md-6\">\n      <div class=\"mb-3\">\n        <label for=\"orderId\" class=\"form-label\">ID Commande</label>\n        <input type=\"text\" class=\"form-control\" id=\"orderId\" formControlName=\"orderId\" readonly>\n      </div>\n\n      <div class=\"mb-3\">\n        <label for=\"customerName\" class=\"form-label\">Nom du client *</label>\n        <input type=\"text\" class=\"form-control\" id=\"customerName\" formControlName=\"customerName\"\n               [class.is-invalid]=\"deliveryForm.get('customerName')?.invalid && deliveryForm.get('customerName')?.touched\">\n        <div class=\"invalid-feedback\">\n          Le nom du client est requis\n        </div>\n      </div>\n\n      <div class=\"mb-3\">\n        <label for=\"address\" class=\"form-label\">Adresse de livraison *</label>\n        <textarea class=\"form-control\" id=\"address\" formControlName=\"address\" rows=\"2\"\n                  [class.is-invalid]=\"deliveryForm.get('address')?.invalid && deliveryForm.get('address')?.touched\"></textarea>\n        <div class=\"invalid-feedback\">\n          L'adresse de livraison est requise\n        </div>\n      </div>\n\n      <div class=\"mb-3\">\n        <label for=\"phoneNumber\" class=\"form-label\">Téléphone *</label>\n        <input type=\"tel\" class=\"form-control\" id=\"phoneNumber\" formControlName=\"phoneNumber\"\n               [class.is-invalid]=\"deliveryForm.get('phoneNumber')?.invalid && deliveryForm.get('phoneNumber')?.touched\">\n        <div class=\"invalid-feedback\">\n          Le numéro de téléphone est requis\n        </div>\n      </div>\n\n      <div class=\"mb-3\">\n        <label for=\"estimatedDeliveryTime\" class=\"form-label\">Date et heure prévues *</label>\n        <input type=\"datetime-local\" class=\"form-control\" id=\"estimatedDeliveryTime\" formControlName=\"estimatedDeliveryTime\"\n               [class.is-invalid]=\"deliveryForm.get('estimatedDeliveryTime')?.invalid && deliveryForm.get('estimatedDeliveryTime')?.touched\">\n        <div class=\"invalid-feedback\">\n          La date et heure de livraison sont requises\n        </div>\n      </div>\n    </div>\n\n    <!-- Right Column -->\n    <div class=\"col-md-6\">\n      <div class=\"mb-3\">\n        <label for=\"driverId\" class=\"form-label\">Livreur assigné *</label>\n        <select class=\"form-select\" id=\"driverId\" formControlName=\"driverId\"\n                [class.is-invalid]=\"deliveryForm.get('driverId')?.invalid && deliveryForm.get('driverId')?.touched\">\n          <option value=\"\">Sélectionner un livreur</option>\n          <option *ngFor=\"let driver of drivers\" [value]=\"driver.id\">\n            {{ driver.name }} - {{ driver.vehicleType }}\n          </option>\n        </select>\n        <div class=\"invalid-feedback\">\n          Un livreur doit être assigné\n        </div>\n        <div *ngIf=\"loading\" class=\"form-text\">\n          <i class=\"fa-solid fa-spinner fa-spin me-1\"></i>\n          Chargement des livreurs...\n        </div>\n      </div>\n\n      <div class=\"mb-3\">\n        <label for=\"priority\" class=\"form-label\">Priorité *</label>\n        <select class=\"form-select\" id=\"priority\" formControlName=\"priority\">\n          <option *ngFor=\"let option of priorityOptions\" [value]=\"option.value\">\n            {{ option.label }}\n          </option>\n        </select>\n      </div>\n\n      <div class=\"mb-3\">\n        <label for=\"status\" class=\"form-label\">Statut *</label>\n        <select class=\"form-select\" id=\"status\" formControlName=\"status\">\n          <option *ngFor=\"let option of statusOptions\" [value]=\"option.value\">\n            {{ option.label }}\n          </option>\n        </select>\n      </div>\n\n      <div class=\"mb-3\">\n        <label class=\"form-label\">Coordonnées GPS</label>\n        <div class=\"row\">\n          <div class=\"col-6\">\n            <input type=\"number\" class=\"form-control\" placeholder=\"Latitude\" formControlName=\"latitude\"\n                   step=\"0.000001\" [class.is-invalid]=\"deliveryForm.get('latitude')?.invalid && deliveryForm.get('latitude')?.touched\">\n            <div class=\"invalid-feedback\">\n              Latitude invalide\n            </div>\n          </div>\n          <div class=\"col-6\">\n            <input type=\"number\" class=\"form-control\" placeholder=\"Longitude\" formControlName=\"longitude\"\n                   step=\"0.000001\" [class.is-invalid]=\"deliveryForm.get('longitude')?.invalid && deliveryForm.get('longitude')?.touched\">\n            <div class=\"invalid-feedback\">\n              Longitude invalide\n            </div>\n          </div>\n        </div>\n        <small class=\"form-text text-muted\">\n          Coordonnées par défaut: Paris (48.8566, 2.3522)\n        </small>\n      </div>\n\n      <div class=\"mb-3\">\n        <label for=\"notes\" class=\"form-label\">Notes</label>\n        <textarea class=\"form-control\" id=\"notes\" formControlName=\"notes\" rows=\"3\"\n                  placeholder=\"Instructions spéciales, commentaires...\"></textarea>\n      </div>\n    </div>\n  </div>\n\n  <div class=\"d-flex gap-2 justify-content-end\">\n    <button type=\"button\" class=\"btn btn-secondary\" (click)=\"onCancel()\">Annuler</button>\n    <button type=\"submit\" class=\"btn btn-primary\" [disabled]=\"!deliveryForm.valid || loading\">\n      <i class=\"fa-solid fa-save me-2\"></i>\n      {{ delivery ? 'Mettre à jour' : 'Créer la livraison' }}\n    </button>\n  </div>\n</form>\n"], "mappings": "AAAA,SAAmCA,YAAY,QAAgB,eAAe;AAC9E,SAAiCC,UAAU,QAAQ,gBAAgB;AACnE,SAAmBC,cAAc,EAAEC,gBAAgB,QAAQ,6BAA6B;;;;;;;ICmD9EC,EAAA,CAAAC,cAAA,iBAA2D;IACzDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAF8BH,EAAA,CAAAI,UAAA,UAAAC,SAAA,CAAAC,EAAA,CAAmB;IACxDN,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,SAAA,CAAAI,IAAA,SAAAJ,SAAA,CAAAK,WAAA,MACF;;;;;IAKFV,EAAA,CAAAC,cAAA,cAAuC;IACrCD,EAAA,CAAAW,SAAA,YAAgD;IAChDX,EAAA,CAAAE,MAAA,mCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAMJH,EAAA,CAAAC,cAAA,iBAAsE;IACpED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFsCH,EAAA,CAAAI,UAAA,UAAAQ,SAAA,CAAAC,KAAA,CAAsB;IACnEb,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAc,kBAAA,MAAAF,SAAA,CAAAG,KAAA,MACF;;;;;IAOAf,EAAA,CAAAC,cAAA,iBAAoE;IAClED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFoCH,EAAA,CAAAI,UAAA,UAAAY,SAAA,CAAAH,KAAA,CAAsB;IACjEb,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAc,kBAAA,MAAAE,SAAA,CAAAD,KAAA,MACF;;;ADrEV,OAAM,MAAOE,qBAAqB;EAyBhCC,YACUC,EAAe,EACfC,aAA4B;IAD5B,KAAAD,EAAE,GAAFA,EAAE;IACF,KAAAC,aAAa,GAAbA,aAAa;IA1Bd,KAAAC,QAAQ,GAAoB,IAAI;IAC/B,KAAAC,SAAS,GAAG,IAAI1B,YAAY,EAAO;IACnC,KAAA2B,SAAS,GAAG,IAAI3B,YAAY,EAAQ;IAG9C,KAAA4B,OAAO,GAAa,EAAE;IACtB,KAAAC,OAAO,GAAG,KAAK;IAEf;IACA,KAAAC,eAAe,GAAG,CAChB;MAAEb,KAAK,EAAEd,gBAAgB,CAAC4B,GAAG;MAAEZ,KAAK,EAAE;IAAO,CAAE,EAC/C;MAAEF,KAAK,EAAEd,gBAAgB,CAAC6B,MAAM;MAAEb,KAAK,EAAE;IAAS,CAAE,EACpD;MAAEF,KAAK,EAAEd,gBAAgB,CAAC8B,IAAI;MAAEd,KAAK,EAAE;IAAO,CAAE,EAChD;MAAEF,KAAK,EAAEd,gBAAgB,CAAC+B,MAAM;MAAEf,KAAK,EAAE;IAAS,CAAE,CACrD;IAED,KAAAgB,aAAa,GAAG,CACd;MAAElB,KAAK,EAAEf,cAAc,CAACkC,OAAO;MAAEjB,KAAK,EAAE;IAAY,CAAE,EACtD;MAAEF,KAAK,EAAEf,cAAc,CAACmC,SAAS;MAAElB,KAAK,EAAE;IAAU,CAAE,EACtD;MAAEF,KAAK,EAAEf,cAAc,CAACoC,SAAS;MAAEnB,KAAK,EAAE;IAAO,CAAE,EACnD;MAAEF,KAAK,EAAEf,cAAc,CAACqC,OAAO;MAAEpB,KAAK,EAAE;IAAS,CAAE,EACnD;MAAEF,KAAK,EAAEf,cAAc,CAACsC,SAAS;MAAErB,KAAK,EAAE;IAAQ,CAAE,CACrD;IAMC,IAAI,CAACsB,YAAY,GAAG,IAAI,CAAClB,EAAE,CAACmB,KAAK,CAAC;MAChCC,OAAO,EAAE,CAAC,EAAE,EAAE1C,UAAU,CAAC2C,QAAQ,CAAC;MAClCC,UAAU,EAAE,CAAC,EAAE,CAAC;MAChBC,YAAY,EAAE,CAAC,EAAE,EAAE7C,UAAU,CAAC2C,QAAQ,CAAC;MACvCG,OAAO,EAAE,CAAC,EAAE,EAAE9C,UAAU,CAAC2C,QAAQ,CAAC;MAClCI,WAAW,EAAE,CAAC,EAAE,EAAE/C,UAAU,CAAC2C,QAAQ,CAAC;MACtCK,QAAQ,EAAE,CAAC,EAAE,EAAEhD,UAAU,CAAC2C,QAAQ,CAAC;MACnCM,qBAAqB,EAAE,CAAC,EAAE,EAAEjD,UAAU,CAAC2C,QAAQ,CAAC;MAChDO,QAAQ,EAAE,CAAChD,gBAAgB,CAAC6B,MAAM,EAAE/B,UAAU,CAAC2C,QAAQ,CAAC;MACxDQ,MAAM,EAAE,CAAClD,cAAc,CAACkC,OAAO,EAAEnC,UAAU,CAAC2C,QAAQ,CAAC;MACrDS,KAAK,EAAE,CAAC,EAAE,CAAC;MACXC,QAAQ,EAAE,CAAC,OAAO,EAAE,CAACrD,UAAU,CAAC2C,QAAQ,EAAE3C,UAAU,CAACsD,GAAG,CAAC,CAAC,EAAE,CAAC,EAAEtD,UAAU,CAACuD,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;MACnFC,SAAS,EAAE,CAAC,MAAM,EAAE,CAACxD,UAAU,CAAC2C,QAAQ,EAAE3C,UAAU,CAACsD,GAAG,CAAC,CAAC,GAAG,CAAC,EAAEtD,UAAU,CAACuD,GAAG,CAAC,GAAG,CAAC,CAAC;KACrF,CAAC;EACJ;EAEAE,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE;IAElB,IAAI,IAAI,CAAClC,QAAQ,EAAE;MACjB,IAAI,CAACgB,YAAY,CAACmB,UAAU,CAAC;QAC3BjB,OAAO,EAAE,IAAI,CAAClB,QAAQ,CAACkB,OAAO;QAC9BE,UAAU,EAAE,IAAI,CAACpB,QAAQ,CAACoB,UAAU,IAAI,EAAE;QAC1CC,YAAY,EAAE,IAAI,CAACrB,QAAQ,CAACqB,YAAY;QACxCC,OAAO,EAAE,IAAI,CAACtB,QAAQ,CAACsB,OAAO;QAC9BC,WAAW,EAAE,IAAI,CAACvB,QAAQ,CAACoC,gBAAgB,IAAI,EAAE;QACjDZ,QAAQ,EAAE,IAAI,CAACxB,QAAQ,CAACwB,QAAQ;QAChCC,qBAAqB,EAAE,IAAI,CAACY,kBAAkB,CAAC,IAAI,CAACrC,QAAQ,CAACyB,qBAAqB,CAAC;QACnFC,QAAQ,EAAE,IAAI,CAAC1B,QAAQ,CAAC0B,QAAQ;QAChCC,MAAM,EAAE,IAAI,CAAC3B,QAAQ,CAAC2B,MAAM;QAC5BC,KAAK,EAAE,IAAI,CAAC5B,QAAQ,CAAC4B,KAAK,IAAI,EAAE;QAChCC,QAAQ,EAAE,IAAI,CAAC7B,QAAQ,CAACsC,WAAW,EAAET,QAAQ,IAAI,OAAO;QACxDG,SAAS,EAAE,IAAI,CAAChC,QAAQ,CAACsC,WAAW,EAAEN,SAAS,IAAI;OACpD,CAAC;KACH,MAAM;MACL;MACA,IAAI,CAAChB,YAAY,CAACmB,UAAU,CAAC;QAC3BjB,OAAO,EAAE,IAAI,CAACqB,eAAe,EAAE;QAC/BnB,UAAU,EAAE,cAAc;QAC1BO,MAAM,EAAElD,cAAc,CAACkC,OAAO;QAC9Be,QAAQ,EAAEhD,gBAAgB,CAAC6B;OAC5B,CAAC;;EAEN;EAEQ2B,WAAWA,CAAA;IACjB,IAAI,CAAC9B,OAAO,GAAG,IAAI;IACnB,IAAI,CAACL,aAAa,CAACyC,UAAU,EAAE,CAACC,SAAS,CAAC;MACxCC,IAAI,EAAGvC,OAAO,IAAI;QAChB,IAAI,CAACA,OAAO,GAAGA,OAAO;QACtB,IAAI,CAACC,OAAO,GAAG,KAAK;MACtB,CAAC;MACDuC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAACvC,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEQmC,eAAeA,CAAA;IACrB,MAAMM,SAAS,GAAGC,IAAI,CAACC,GAAG,EAAE,CAACC,QAAQ,EAAE,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;IACjD,MAAMC,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACD,MAAM,EAAE,GAAG,IAAI,CAAC,CAACF,QAAQ,EAAE,CAACK,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC3E,OAAO,OAAOR,SAAS,GAAGK,MAAM,EAAE;EACpC;EAEQb,kBAAkBA,CAACiB,IAAmB;IAC5C,MAAMC,CAAC,GAAG,IAAIT,IAAI,CAACQ,IAAI,CAAC;IACxB,OAAOC,CAAC,CAACC,WAAW,EAAE,CAACP,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;EACvC;;EAEAQ,QAAQA,CAAA;IACN,IAAI,IAAI,CAACzC,YAAY,CAAC0C,KAAK,EAAE;MAC3B,MAAMC,SAAS,GAAG,IAAI,CAAC3C,YAAY,CAACxB,KAAK;MAEzC;MACA,MAAMoE,YAAY,GAAG;QACnB3E,EAAE,EAAE,EAAE;QACNiC,OAAO,EAAEyC,SAAS,CAACzC,OAAO;QAC1BE,UAAU,EAAEuC,SAAS,CAACvC,UAAU;QAChCC,YAAY,EAAEsC,SAAS,CAACtC,YAAY;QACpCC,OAAO,EAAEqC,SAAS,CAACrC,OAAO;QAC1BK,MAAM,EAAEgC,SAAS,CAAChC,MAAM;QACxBH,QAAQ,EAAEmC,SAAS,CAACnC,QAAQ;QAC5BqC,UAAU,EAAE,IAAI,CAACC,aAAa,CAACH,SAAS,CAACnC,QAAQ,CAAC;QAClDC,qBAAqB,EAAE,IAAIqB,IAAI,CAACa,SAAS,CAAClC,qBAAqB,CAAC,CAAC+B,WAAW,EAAE;QAC9E9B,QAAQ,EAAEiC,SAAS,CAACjC,QAAQ;QAC5BY,WAAW,EAAE;UACXT,QAAQ,EAAEkC,UAAU,CAACJ,SAAS,CAAC9B,QAAQ,CAAC;UACxCG,SAAS,EAAE+B,UAAU,CAACJ,SAAS,CAAC3B,SAAS;SAC1C;QACDJ,KAAK,EAAE+B,SAAS,CAAC/B,KAAK,IAAI,EAAE;QAC5BoC,SAAS,EAAE,IAAI,CAAChE,QAAQ,EAAEgE,SAAS,IAAI,IAAIlB,IAAI,EAAE,CAACU,WAAW;OAC9D;MAED,IAAI,CAACvD,SAAS,CAACgE,IAAI,CAACL,YAAY,CAAC;;EAErC;EAEQE,aAAaA,CAACtC,QAAgB;IACpC,MAAM0C,MAAM,GAAG,IAAI,CAAC/D,OAAO,CAACgE,IAAI,CAACZ,CAAC,IAAIA,CAAC,CAACtE,EAAE,KAAKuC,QAAQ,CAAC;IACxD,OAAO0C,MAAM,GAAGA,MAAM,CAAC9E,IAAI,GAAG,EAAE;EAClC;EAEAgF,QAAQA,CAAA;IACN,IAAI,CAAClE,SAAS,CAAC+D,IAAI,EAAE;EACvB;;;uBAtIWrE,qBAAqB,EAAAjB,EAAA,CAAA0F,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA5F,EAAA,CAAA0F,iBAAA,CAAAG,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAArB7E,qBAAqB;MAAA8E,SAAA;MAAAC,MAAA;QAAA3E,QAAA;MAAA;MAAA4E,OAAA;QAAA3E,SAAA;QAAAC,SAAA;MAAA;MAAA2E,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCXlCvG,EAAA,CAAAC,cAAA,cAAyD;UAAxBD,EAAA,CAAAyG,UAAA,sBAAAC,wDAAA;YAAA,OAAYF,GAAA,CAAA1B,QAAA,EAAU;UAAA,EAAC;UACtD9E,EAAA,CAAAC,cAAA,aAAiB;UAI6BD,EAAA,CAAAE,MAAA,kBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC3DH,EAAA,CAAAW,SAAA,eAAwF;UAC1FX,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,aAAkB;UAC6BD,EAAA,CAAAE,MAAA,sBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACpEH,EAAA,CAAAW,SAAA,gBACmH;UACnHX,EAAA,CAAAC,cAAA,cAA8B;UAC5BD,EAAA,CAAAE,MAAA,qCACF;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAGRH,EAAA,CAAAC,cAAA,cAAkB;UACwBD,EAAA,CAAAE,MAAA,8BAAsB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtEH,EAAA,CAAAW,SAAA,oBACuH;UACvHX,EAAA,CAAAC,cAAA,cAA8B;UAC5BD,EAAA,CAAAE,MAAA,4CACF;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAGRH,EAAA,CAAAC,cAAA,cAAkB;UAC4BD,EAAA,CAAAE,MAAA,6BAAW;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC/DH,EAAA,CAAAW,SAAA,iBACiH;UACjHX,EAAA,CAAAC,cAAA,cAA8B;UAC5BD,EAAA,CAAAE,MAAA,0DACF;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAGRH,EAAA,CAAAC,cAAA,cAAkB;UACsCD,EAAA,CAAAE,MAAA,oCAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACrFH,EAAA,CAAAW,SAAA,iBACqI;UACrIX,EAAA,CAAAC,cAAA,cAA8B;UAC5BD,EAAA,CAAAE,MAAA,qDACF;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAKVH,EAAA,CAAAC,cAAA,cAAsB;UAEuBD,EAAA,CAAAE,MAAA,8BAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAClEH,EAAA,CAAAC,cAAA,kBAC4G;UACzFD,EAAA,CAAAE,MAAA,oCAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACjDH,EAAA,CAAA2G,UAAA,KAAAC,wCAAA,qBAES;UACX5G,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,cAA8B;UAC5BD,EAAA,CAAAE,MAAA,gDACF;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAA2G,UAAA,KAAAE,qCAAA,kBAGM;UACR7G,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,cAAkB;UACyBD,EAAA,CAAAE,MAAA,uBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC3DH,EAAA,CAAAC,cAAA,kBAAqE;UACnED,EAAA,CAAA2G,UAAA,KAAAG,wCAAA,qBAES;UACX9G,EAAA,CAAAG,YAAA,EAAS;UAGXH,EAAA,CAAAC,cAAA,cAAkB;UACuBD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvDH,EAAA,CAAAC,cAAA,kBAAiE;UAC/DD,EAAA,CAAA2G,UAAA,KAAAI,wCAAA,qBAES;UACX/G,EAAA,CAAAG,YAAA,EAAS;UAGXH,EAAA,CAAAC,cAAA,cAAkB;UACUD,EAAA,CAAAE,MAAA,4BAAe;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACjDH,EAAA,CAAAC,cAAA,cAAiB;UAEbD,EAAA,CAAAW,SAAA,iBAC2H;UAC3HX,EAAA,CAAAC,cAAA,cAA8B;UAC5BD,EAAA,CAAAE,MAAA,2BACF;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAERH,EAAA,CAAAC,cAAA,eAAmB;UACjBD,EAAA,CAAAW,SAAA,iBAC6H;UAC7HX,EAAA,CAAAC,cAAA,cAA8B;UAC5BD,EAAA,CAAAE,MAAA,4BACF;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAGVH,EAAA,CAAAC,cAAA,iBAAoC;UAClCD,EAAA,CAAAE,MAAA,mEACF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAGVH,EAAA,CAAAC,cAAA,cAAkB;UACsBD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACnDH,EAAA,CAAAW,SAAA,oBAC2E;UAC7EX,EAAA,CAAAG,YAAA,EAAM;UAIVH,EAAA,CAAAC,cAAA,eAA8C;UACID,EAAA,CAAAyG,UAAA,mBAAAO,wDAAA;YAAA,OAASR,GAAA,CAAAf,QAAA,EAAU;UAAA,EAAC;UAACzF,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACrFH,EAAA,CAAAC,cAAA,kBAA0F;UACxFD,EAAA,CAAAW,SAAA,aAAqC;UACrCX,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;;;;;UAxHPH,EAAA,CAAAI,UAAA,cAAAoG,GAAA,CAAAnE,YAAA,CAA0B;UAYjBrC,EAAA,CAAAO,SAAA,IAA2G;UAA3GP,EAAA,CAAAiH,WAAA,iBAAAC,OAAA,GAAAV,GAAA,CAAAnE,YAAA,CAAA8E,GAAA,mCAAAD,OAAA,CAAAE,OAAA,OAAAF,OAAA,GAAAV,GAAA,CAAAnE,YAAA,CAAA8E,GAAA,mCAAAD,OAAA,CAAAG,OAAA,EAA2G;UASxGrH,EAAA,CAAAO,SAAA,GAAiG;UAAjGP,EAAA,CAAAiH,WAAA,iBAAAK,OAAA,GAAAd,GAAA,CAAAnE,YAAA,CAAA8E,GAAA,8BAAAG,OAAA,CAAAF,OAAA,OAAAE,OAAA,GAAAd,GAAA,CAAAnE,YAAA,CAAA8E,GAAA,8BAAAG,OAAA,CAAAD,OAAA,EAAiG;UASpGrH,EAAA,CAAAO,SAAA,GAAyG;UAAzGP,EAAA,CAAAiH,WAAA,iBAAAM,OAAA,GAAAf,GAAA,CAAAnE,YAAA,CAAA8E,GAAA,kCAAAI,OAAA,CAAAH,OAAA,OAAAG,OAAA,GAAAf,GAAA,CAAAnE,YAAA,CAAA8E,GAAA,kCAAAI,OAAA,CAAAF,OAAA,EAAyG;UASzGrH,EAAA,CAAAO,SAAA,GAA6H;UAA7HP,EAAA,CAAAiH,WAAA,iBAAAO,OAAA,GAAAhB,GAAA,CAAAnE,YAAA,CAAA8E,GAAA,4CAAAK,OAAA,CAAAJ,OAAA,OAAAI,OAAA,GAAAhB,GAAA,CAAAnE,YAAA,CAAA8E,GAAA,4CAAAK,OAAA,CAAAH,OAAA,EAA6H;UAY5HrH,EAAA,CAAAO,SAAA,GAAmG;UAAnGP,EAAA,CAAAiH,WAAA,iBAAAQ,OAAA,GAAAjB,GAAA,CAAAnE,YAAA,CAAA8E,GAAA,+BAAAM,OAAA,CAAAL,OAAA,OAAAK,OAAA,GAAAjB,GAAA,CAAAnE,YAAA,CAAA8E,GAAA,+BAAAM,OAAA,CAAAJ,OAAA,EAAmG;UAE9ErH,EAAA,CAAAO,SAAA,GAAU;UAAVP,EAAA,CAAAI,UAAA,YAAAoG,GAAA,CAAAhF,OAAA,CAAU;UAOjCxB,EAAA,CAAAO,SAAA,GAAa;UAAbP,EAAA,CAAAI,UAAA,SAAAoG,GAAA,CAAA/E,OAAA,CAAa;UASUzB,EAAA,CAAAO,SAAA,GAAkB;UAAlBP,EAAA,CAAAI,UAAA,YAAAoG,GAAA,CAAA9E,eAAA,CAAkB;UASlB1B,EAAA,CAAAO,SAAA,GAAgB;UAAhBP,EAAA,CAAAI,UAAA,YAAAoG,GAAA,CAAAzE,aAAA,CAAgB;UAWlB/B,EAAA,CAAAO,SAAA,GAAmG;UAAnGP,EAAA,CAAAiH,WAAA,iBAAAS,QAAA,GAAAlB,GAAA,CAAAnE,YAAA,CAAA8E,GAAA,+BAAAO,QAAA,CAAAN,OAAA,OAAAM,QAAA,GAAAlB,GAAA,CAAAnE,YAAA,CAAA8E,GAAA,+BAAAO,QAAA,CAAAL,OAAA,EAAmG;UAOnGrH,EAAA,CAAAO,SAAA,GAAqG;UAArGP,EAAA,CAAAiH,WAAA,iBAAAU,QAAA,GAAAnB,GAAA,CAAAnE,YAAA,CAAA8E,GAAA,gCAAAQ,QAAA,CAAAP,OAAA,OAAAO,QAAA,GAAAnB,GAAA,CAAAnE,YAAA,CAAA8E,GAAA,gCAAAQ,QAAA,CAAAN,OAAA,EAAqG;UAqBtFrH,EAAA,CAAAO,SAAA,IAA2C;UAA3CP,EAAA,CAAAI,UAAA,cAAAoG,GAAA,CAAAnE,YAAA,CAAA0C,KAAA,IAAAyB,GAAA,CAAA/E,OAAA,CAA2C;UAEvFzB,EAAA,CAAAO,SAAA,GACF;UADEP,EAAA,CAAAc,kBAAA,MAAA0F,GAAA,CAAAnF,QAAA,yDACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}