{"ast": null, "code": "import { DriverStatus } from '../../core/models/driver.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../core/services/driver.service\";\nimport * as i2 from \"../../core/services/delivery.service\";\nimport * as i3 from \"../../core/services/real-time.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"../../shared/components/sidebar/sidebar.component\";\nimport * as i7 from \"../../shared/components/header/header.component\";\nimport * as i8 from \"../../shared/components/status-badge/status-badge.component\";\nimport * as i9 from \"../../shared/components/map-view/map-view.component\";\nimport * as i10 from \"../../shared/components/driver-form/driver-form.component\";\nfunction DriversManagementComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"div\", 27)(2, \"span\", 28);\n    i0.ɵɵtext(3, \"Chargement...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 29);\n    i0.ɵɵtext(5, \"Chargement des donn\\u00E9es...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DriversManagementComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction DriversManagementComponent_ng_container_14_button_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function DriversManagementComponent_ng_container_14_button_35_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r10);\n      const driver_r8 = restoredCtx.$implicit;\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.selectDriver(driver_r8));\n    });\n    i0.ɵɵelementStart(1, \"div\", 61)(2, \"div\", 62);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 63)(5, \"h6\", 64);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 65);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 66);\n    i0.ɵɵelement(10, \"app-status-badge\", 67);\n    i0.ɵɵelementStart(11, \"p\", 68);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const driver_r8 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", (ctx_r4.selectedDriver == null ? null : ctx_r4.selectedDriver.id) === driver_r8.id);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", driver_r8.name.charAt(0), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(driver_r8.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(driver_r8.vehicleType);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"status\", ctx_r4.getStatusText(driver_r8.status))(\"variant\", ctx_r4.getStatusClass(driver_r8.status));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", driver_r8.todayDeliveries, \" livraisons \");\n  }\n}\nfunction DriversManagementComponent_ng_container_14_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69);\n    i0.ɵɵelement(1, \"i\", 70);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Aucun livreur trouv\\u00E9\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DriversManagementComponent_ng_container_14_div_38_span_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 99);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r11.selectedDriver.vehicleId, \")\");\n  }\n}\nfunction DriversManagementComponent_ng_container_14_div_38_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 100)(1, \"div\", 101)(2, \"span\", 28);\n    i0.ɵɵtext(3, \"Chargement...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction DriversManagementComponent_ng_container_14_div_38_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"div\", 102)(2, \"div\", 103)(3, \"div\", 104);\n    i0.ɵɵtext(4, \"Livraisons totales\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 105);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"div\", 102)(8, \"div\", 103)(9, \"div\", 104);\n    i0.ɵɵtext(10, \"Taux de ponctualit\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 105);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"number\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"div\", 102)(15, \"div\", 103)(16, \"div\", 104);\n    i0.ɵɵtext(17, \"Temps moyen\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 105);\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"number\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(21, \"div\", 102)(22, \"div\", 103)(23, \"div\", 104);\n    i0.ɵɵtext(24, \"Satisfaction client\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 105);\n    i0.ɵɵtext(26);\n    i0.ɵɵpipe(27, \"number\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(28, \"div\", 102)(29, \"div\", 103)(30, \"div\", 104);\n    i0.ɵɵtext(31, \"Vitesse moyenne\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 105);\n    i0.ɵɵtext(33);\n    i0.ɵɵpipe(34, \"number\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(35, \"div\", 102)(36, \"div\", 103)(37, \"div\", 104);\n    i0.ɵɵtext(38, \"Livraisons par heure\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"div\", 105);\n    i0.ɵɵtext(40);\n    i0.ɵɵpipe(41, \"number\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r13.driverPerformance.totalDeliveries);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(13, 6, ctx_r13.driverPerformance.onTimeRate, \"1.0-0\"), \"%\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(20, 9, ctx_r13.driverPerformance.avgDeliveryTime, \"1.0-0\"), \" min\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(27, 12, ctx_r13.driverPerformance.customerSatisfaction, \"1.1-1\"), \"/5\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(34, 15, ctx_r13.driverPerformance.avgSpeed, \"1.0-0\"), \" km/h\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(41, 18, ctx_r13.driverPerformance.deliveriesPerHour, \"1.1-1\"));\n  }\n}\nfunction DriversManagementComponent_ng_container_14_div_38_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 106);\n    i0.ɵɵelement(1, \"i\", 107);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Aucune donn\\u00E9e de performance disponible\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DriversManagementComponent_ng_container_14_div_38_div_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 100)(1, \"div\", 101)(2, \"span\", 28);\n    i0.ɵɵtext(3, \"Chargement...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction DriversManagementComponent_ng_container_14_div_38_div_62_tr_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵelement(8, \"app-status-badge\", 67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const delivery_r19 = ctx.$implicit;\n    const ctx_r18 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(delivery_r19.orderId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(delivery_r19.customerName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(delivery_r19.address);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"status\", ctx_r18.getDeliveryStatusText(delivery_r19.status))(\"variant\", ctx_r18.getDeliveryStatusClass(delivery_r19.status));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(11, 7, delivery_r19.estimatedDeliveryTime, \"HH:mm\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(delivery_r19.actualDeliveryTime ? i0.ɵɵpipeBind2(14, 10, delivery_r19.actualDeliveryTime, \"HH:mm\") : \"-\");\n  }\n}\nfunction DriversManagementComponent_ng_container_14_div_38_div_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 108)(1, \"table\", 109)(2, \"thead\")(3, \"tr\")(4, \"th\");\n    i0.ɵɵtext(5, \"ID\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7, \"Client\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"Adresse\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Statut\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\");\n    i0.ɵɵtext(13, \"Heure estim\\u00E9e\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\");\n    i0.ɵɵtext(15, \"Heure r\\u00E9elle\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"tbody\");\n    i0.ɵɵtemplate(17, DriversManagementComponent_ng_container_14_div_38_div_62_tr_17_Template, 15, 13, \"tr\", 110);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(17);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r16.driverDeliveries);\n  }\n}\nfunction DriversManagementComponent_ng_container_14_div_38_div_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 106);\n    i0.ɵɵelement(1, \"i\", 111);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Aucune livraison trouv\\u00E9e pour ce livreur\");\n    i0.ɵɵelementEnd()();\n  }\n}\nconst _c0 = function (a0) {\n  return [a0];\n};\nfunction DriversManagementComponent_ng_container_14_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"div\", 71)(2, \"div\", 49)(3, \"div\", 34)(4, \"div\", 72)(5, \"div\", 73);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 63)(8, \"div\", 74)(9, \"h4\", 75);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(11, \"app-status-badge\", 67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 76)(13, \"div\", 36)(14, \"p\", 64);\n    i0.ɵɵelement(15, \"i\", 77);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"p\", 64);\n    i0.ɵɵelement(18, \"i\", 78);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"p\", 75);\n    i0.ɵɵelement(21, \"i\", 79);\n    i0.ɵɵtext(22);\n    i0.ɵɵtemplate(23, DriversManagementComponent_ng_container_14_div_38_span_23_Template, 2, 1, \"span\", 80);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 36)(25, \"p\", 64);\n    i0.ɵɵelement(26, \"i\", 81);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"p\", 64);\n    i0.ɵɵelement(29, \"i\", 82);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"p\", 75);\n    i0.ɵɵelement(32, \"i\", 83);\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd()()()()()()()();\n    i0.ɵɵelementStart(34, \"div\", 36)(35, \"div\", 84)(36, \"div\", 85)(37, \"h5\", 51);\n    i0.ɵɵelement(38, \"i\", 86);\n    i0.ɵɵtext(39, \" M\\u00E9triques de performance \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"div\", 34);\n    i0.ɵɵtemplate(41, DriversManagementComponent_ng_container_14_div_38_div_41_Template, 4, 0, \"div\", 87);\n    i0.ɵɵtemplate(42, DriversManagementComponent_ng_container_14_div_38_div_42_Template, 42, 21, \"div\", 88);\n    i0.ɵɵtemplate(43, DriversManagementComponent_ng_container_14_div_38_div_43_Template, 4, 0, \"div\", 89);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(44, \"div\", 36)(45, \"div\", 84)(46, \"div\", 85)(47, \"h5\", 51);\n    i0.ɵɵelement(48, \"i\", 90);\n    i0.ɵɵtext(49, \" Position actuelle \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(50, \"div\", 53);\n    i0.ɵɵelement(51, \"app-map-view\", 91);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(52, \"div\", 71)(53, \"div\", 49)(54, \"div\", 50)(55, \"h5\", 51);\n    i0.ɵɵelement(56, \"i\", 92);\n    i0.ɵɵtext(57, \" Livraisons r\\u00E9centes \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"span\", 52);\n    i0.ɵɵtext(59);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(60, \"div\", 53);\n    i0.ɵɵtemplate(61, DriversManagementComponent_ng_container_14_div_38_div_61_Template, 4, 0, \"div\", 87);\n    i0.ɵɵtemplate(62, DriversManagementComponent_ng_container_14_div_38_div_62_Template, 18, 1, \"div\", 93);\n    i0.ɵɵtemplate(63, DriversManagementComponent_ng_container_14_div_38_div_63_Template, 4, 0, \"div\", 89);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(64, \"div\", 71)(65, \"div\", 3)(66, \"button\", 4);\n    i0.ɵɵlistener(\"click\", function DriversManagementComponent_ng_container_14_div_38_Template_button_click_66_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r20.onEditDriver(ctx_r20.selectedDriver));\n    });\n    i0.ɵɵelement(67, \"i\", 94);\n    i0.ɵɵtext(68, \" Modifier \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(69, \"button\", 95);\n    i0.ɵɵelement(70, \"i\", 96);\n    i0.ɵɵtext(71, \" Optimiser l'itin\\u00E9raire \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(72, \"button\", 97);\n    i0.ɵɵlistener(\"click\", function DriversManagementComponent_ng_container_14_div_38_Template_button_click_72_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r22 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r22.onDeleteDriver(ctx_r22.selectedDriver));\n    });\n    i0.ɵɵelement(73, \"i\", 98);\n    i0.ɵɵtext(74, \" Supprimer \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.selectedDriver.name.charAt(0), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r6.selectedDriver.name);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"status\", ctx_r6.getStatusText(ctx_r6.selectedDriver.status))(\"variant\", ctx_r6.getStatusClass(ctx_r6.selectedDriver.status));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.selectedDriver.email, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.selectedDriver.phone, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.selectedDriver.vehicleType, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.selectedDriver.vehicleId);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" Embauch\\u00E9 le \", ctx_r6.formatDate(ctx_r6.selectedDriver.hireDate), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" Permis: \", ctx_r6.selectedDriver.licenseNumber || \"N/A\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" Derni\\u00E8re activit\\u00E9: \", ctx_r6.formatDate(ctx_r6.selectedDriver.lastActive), \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.performanceLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r6.performanceLoading && ctx_r6.driverPerformance);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r6.performanceLoading && !ctx_r6.driverPerformance);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"drivers\", i0.ɵɵpureFunction1(22, _c0, ctx_r6.selectedDriver))(\"height\", 250)(\"center\", ctx_r6.getDriverMapCenter())(\"zoom\", 14);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r6.driverDeliveries.length);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.deliveriesLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r6.deliveriesLoading && ctx_r6.driverDeliveries.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r6.deliveriesLoading && ctx_r6.driverDeliveries.length === 0);\n  }\n}\nfunction DriversManagementComponent_ng_container_14_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"div\", 112);\n    i0.ɵɵelement(2, \"i\", 113);\n    i0.ɵɵelementStart(3, \"h5\");\n    i0.ɵɵtext(4, \"Aucun livreur s\\u00E9lectionn\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 21);\n    i0.ɵɵtext(6, \"S\\u00E9lectionnez un livreur dans la liste pour voir les d\\u00E9tails\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction DriversManagementComponent_ng_container_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 31)(2, \"div\", 32)(3, \"div\", 33)(4, \"div\", 34)(5, \"div\", 35)(6, \"div\", 36)(7, \"label\", 37);\n    i0.ɵɵtext(8, \"Statut\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"select\", 38);\n    i0.ɵɵlistener(\"ngModelChange\", function DriversManagementComponent_ng_container_14_Template_select_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.statusFilter = $event);\n    })(\"change\", function DriversManagementComponent_ng_container_14_Template_select_change_9_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r25 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r25.applyFilters());\n    });\n    i0.ɵɵelementStart(10, \"option\", 39);\n    i0.ɵɵtext(11, \"Tous les statuts\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"option\", 40);\n    i0.ɵɵtext(13, \"Actif\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"option\", 41);\n    i0.ɵɵtext(15, \"Inactif\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"option\", 42);\n    i0.ɵɵtext(17, \"En pause\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"option\", 43);\n    i0.ɵɵtext(19, \"Hors ligne\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(20, \"div\", 36)(21, \"label\", 44);\n    i0.ɵɵtext(22, \"Recherche\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 45)(24, \"input\", 46);\n    i0.ɵɵlistener(\"ngModelChange\", function DriversManagementComponent_ng_container_14_Template_input_ngModelChange_24_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.searchTerm = $event);\n    })(\"input\", function DriversManagementComponent_ng_container_14_Template_input_input_24_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r27 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r27.applyFilters());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"span\", 47);\n    i0.ɵɵelement(26, \"i\", 48);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(27, \"div\", 49)(28, \"div\", 50)(29, \"h5\", 51);\n    i0.ɵɵtext(30, \"Livreurs\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"span\", 52);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"div\", 53)(34, \"div\", 54);\n    i0.ɵɵtemplate(35, DriversManagementComponent_ng_container_14_button_35_Template, 13, 8, \"button\", 55);\n    i0.ɵɵtemplate(36, DriversManagementComponent_ng_container_14_div_36_Template, 4, 0, \"div\", 56);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(37, \"div\", 57);\n    i0.ɵɵtemplate(38, DriversManagementComponent_ng_container_14_div_38_Template, 75, 24, \"div\", 58);\n    i0.ɵɵtemplate(39, DriversManagementComponent_ng_container_14_div_39_Template, 7, 0, \"div\", 59);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.statusFilter);\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.searchTerm);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r2.filteredDrivers.length);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.filteredDrivers);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.filteredDrivers.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedDriver);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.selectedDriver);\n  }\n}\nfunction DriversManagementComponent_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 114);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"show\", ctx_r3.showDriverForm || ctx_r3.showDeleteConfirm);\n  }\n}\nexport let DriversManagementComponent = /*#__PURE__*/(() => {\n  class DriversManagementComponent {\n    constructor(driverService, deliveryService, realTimeService) {\n      this.driverService = driverService;\n      this.deliveryService = deliveryService;\n      this.realTimeService = realTimeService;\n      this.drivers = [];\n      this.filteredDrivers = [];\n      this.selectedDriver = null;\n      this.driverPerformance = null;\n      this.driverDeliveries = [];\n      this.statusFilter = 'all';\n      this.searchTerm = '';\n      this.loading = true;\n      this.performanceLoading = false;\n      this.deliveriesLoading = false;\n      this.error = '';\n      // Modal states\n      this.showDriverForm = false;\n      this.showDeleteConfirm = false;\n      this.editingDriver = null;\n      this.subscriptions = [];\n    }\n    ngOnInit() {\n      this.loadDrivers();\n      this.setupRealTimeUpdates();\n    }\n    ngOnDestroy() {\n      this.subscriptions.forEach(sub => sub.unsubscribe());\n    }\n    loadDrivers() {\n      this.loading = true;\n      this.subscriptions.push(this.driverService.getDrivers().subscribe({\n        next: drivers => {\n          this.drivers = drivers;\n          this.applyFilters();\n          this.loading = false;\n        },\n        error: err => {\n          console.error('Error loading drivers', err);\n          this.error = 'Erreur lors du chargement des livreurs';\n          this.loading = false;\n        }\n      }));\n    }\n    setupRealTimeUpdates() {\n      // Start SignalR connection\n      this.realTimeService.startConnection().then(() => {\n        console.log('Connected to real-time hub');\n        this.realTimeService.joinAdminGroup();\n        // Subscribe to driver updates\n        this.subscriptions.push(this.realTimeService.driverUpdates$.subscribe(driver => {\n          if (driver) {\n            this.updateDriver(driver);\n          }\n        }));\n      }).catch(err => {\n        console.error('Error connecting to real-time hub', err);\n      });\n    }\n    updateDriver(updatedDriver) {\n      const index = this.drivers.findIndex(d => d.id === updatedDriver.id);\n      if (index !== -1) {\n        this.drivers[index] = updatedDriver;\n      } else {\n        this.drivers.push(updatedDriver);\n      }\n      this.applyFilters();\n      // Update selected driver if it's the one that was updated\n      if (this.selectedDriver && this.selectedDriver.id === updatedDriver.id) {\n        this.selectedDriver = updatedDriver;\n      }\n    }\n    applyFilters() {\n      let filtered = [...this.drivers];\n      // Apply status filter\n      if (this.statusFilter !== 'all') {\n        filtered = filtered.filter(d => d.status === parseInt(this.statusFilter, 10));\n      }\n      // Apply search filter\n      if (this.searchTerm.trim() !== '') {\n        const search = this.searchTerm.toLowerCase();\n        filtered = filtered.filter(d => d.name.toLowerCase().includes(search) || d.email.toLowerCase().includes(search) || d.phone.toLowerCase().includes(search) || d.vehicleType.toLowerCase().includes(search));\n      }\n      this.filteredDrivers = filtered;\n    }\n    selectDriver(driver) {\n      this.selectedDriver = driver;\n      this.loadDriverPerformance(driver.id);\n      this.loadDriverDeliveries(driver.id);\n    }\n    loadDriverPerformance(driverId) {\n      this.performanceLoading = true;\n      this.subscriptions.push(this.driverService.getDriverPerformance(driverId).subscribe({\n        next: performance => {\n          this.driverPerformance = performance;\n          this.performanceLoading = false;\n        },\n        error: err => {\n          console.error('Error loading driver performance', err);\n          this.performanceLoading = false;\n        }\n      }));\n    }\n    loadDriverDeliveries(driverId) {\n      this.deliveriesLoading = true;\n      this.subscriptions.push(this.deliveryService.getDeliveriesByDriverId(driverId).subscribe({\n        next: deliveries => {\n          this.driverDeliveries = deliveries;\n          this.deliveriesLoading = false;\n        },\n        error: err => {\n          console.error('Error loading driver deliveries', err);\n          this.deliveriesLoading = false;\n        }\n      }));\n    }\n    getStatusClass(status) {\n      switch (status) {\n        case DriverStatus.Available:\n          return 'success';\n        case DriverStatus.Busy:\n          return 'info';\n        case DriverStatus.OnBreak:\n          return 'warning';\n        case DriverStatus.Offline:\n          return 'default';\n        default:\n          return 'default';\n      }\n    }\n    getStatusText(status) {\n      switch (status) {\n        case DriverStatus.Available:\n          return 'Disponible';\n        case DriverStatus.Busy:\n          return 'Occupé';\n        case DriverStatus.OnBreak:\n          return 'En pause';\n        case DriverStatus.Offline:\n          return 'Hors ligne';\n        default:\n          return status;\n      }\n    }\n    getDeliveryStatusClass(status) {\n      switch (status) {\n        case DeliveryStatus.Delivered:\n          return 'success';\n        case DeliveryStatus.InTransit:\n          return 'info';\n        case DeliveryStatus.Delayed:\n          return 'danger';\n        case DeliveryStatus.Pending:\n          return 'warning';\n        case DeliveryStatus.Cancelled:\n          return 'default';\n        default:\n          return 'default';\n      }\n    }\n    getDeliveryStatusText(status) {\n      switch (status) {\n        case DeliveryStatus.Delivered:\n          return 'Livré';\n        case DeliveryStatus.InTransit:\n          return 'En cours';\n        case DeliveryStatus.Delayed:\n          return 'Retardé';\n        case DeliveryStatus.Pending:\n          return 'En attente';\n        case DeliveryStatus.Cancelled:\n          return 'Annulé';\n        default:\n          return status.toString();\n      }\n    }\n    formatDate(date) {\n      return new Date(date).toLocaleString();\n    }\n    getDriverMapCenter() {\n      if (!this.selectedDriver) return undefined;\n      return [this.selectedDriver.currentLocation.latitude, this.selectedDriver.currentLocation.longitude];\n    }\n    // CRUD Operations\n    onNewDriver() {\n      this.editingDriver = null;\n      this.showDriverForm = true;\n    }\n    onEditDriver(driver) {\n      this.editingDriver = driver;\n      this.showDriverForm = true;\n    }\n    onDeleteDriver(driver) {\n      this.selectedDriver = driver;\n      this.showDeleteConfirm = true;\n    }\n    onDriverFormSubmit(driverData) {\n      if (this.editingDriver) {\n        // Update existing driver\n        const updatedDriver = {\n          ...this.editingDriver,\n          ...driverData\n        };\n        this.subscriptions.push(this.driverService.updateDriver(updatedDriver).subscribe({\n          next: () => {\n            this.loadDrivers();\n            this.showDriverForm = false;\n            this.editingDriver = null;\n          },\n          error: err => {\n            console.error('Error updating driver', err);\n            this.error = 'Erreur lors de la mise à jour du livreur';\n          }\n        }));\n      } else {\n        // Create new driver - use all data from form, only add missing fields\n        const newDriver = {\n          ...driverData,\n          // Only override fields that are not in the form or need defaults\n          rating: 0,\n          totalDeliveries: 0,\n          onTimeRate: 0,\n          todayDeliveries: 0,\n          avgDeliveryTime: 0,\n          lastActive: new Date(),\n          hireDate: new Date()\n        };\n        this.subscriptions.push(this.driverService.createDriver(newDriver).subscribe({\n          next: () => {\n            this.loadDrivers();\n            this.showDriverForm = false;\n          },\n          error: err => {\n            console.error('Error creating driver', err);\n            this.error = 'Erreur lors de la création du livreur';\n          }\n        }));\n      }\n    }\n    onDriverFormCancel() {\n      this.showDriverForm = false;\n      this.editingDriver = null;\n    }\n    confirmDelete() {\n      if (this.selectedDriver) {\n        this.subscriptions.push(this.driverService.deleteDriver(this.selectedDriver.id).subscribe({\n          next: () => {\n            this.loadDrivers();\n            this.selectedDriver = null;\n            this.showDeleteConfirm = false;\n          },\n          error: err => {\n            console.error('Error deleting driver', err);\n            this.error = 'Erreur lors de la suppression du livreur';\n          }\n        }));\n      }\n    }\n    cancelDelete() {\n      this.showDeleteConfirm = false;\n      this.selectedDriver = null;\n    }\n    onExport() {\n      // TODO: Implement export functionality\n      alert('Fonctionnalité d\\'export à venir!');\n    }\n    static {\n      this.ɵfac = function DriversManagementComponent_Factory(t) {\n        return new (t || DriversManagementComponent)(i0.ɵɵdirectiveInject(i1.DriverService), i0.ɵɵdirectiveInject(i2.DeliveryService), i0.ɵɵdirectiveInject(i3.RealTimeService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: DriversManagementComponent,\n        selectors: [[\"app-drivers-management\"]],\n        decls: 45,\n        vars: 15,\n        consts: [[1, \"drivers-container\"], [1, \"drivers-content\"], [\"title\", \"Gestion des livreurs\", \"subtitle\", \"G\\u00E9rez et suivez les performances de vos livreurs\"], [1, \"d-flex\", \"gap-2\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"fa-solid\", \"fa-plus\", \"me-2\"], [1, \"btn\", \"btn-outline-secondary\", 3, \"click\"], [1, \"fa-solid\", \"fa-file-export\", \"me-2\"], [1, \"drivers-body\", \"p-4\"], [\"class\", \"text-center py-5\", 4, \"ngIf\"], [\"class\", \"alert alert-danger\", 4, \"ngIf\"], [4, \"ngIf\"], [\"tabindex\", \"-1\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\", \"modal-lg\"], [1, \"modal-content\"], [1, \"modal-header\"], [1, \"modal-title\"], [\"type\", \"button\", 1, \"btn-close\", 3, \"click\"], [1, \"modal-body\"], [3, \"driver\", \"submitted\", \"cancelled\"], [1, \"modal-dialog\"], [1, \"text-muted\"], [1, \"modal-footer\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-danger\", 3, \"click\"], [\"class\", \"modal-backdrop fade\", 3, \"show\", 4, \"ngIf\"], [1, \"text-center\", \"py-5\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"mt-2\"], [1, \"alert\", \"alert-danger\"], [1, \"row\", \"g-4\"], [1, \"col-lg-4\"], [1, \"card\", \"mb-4\"], [1, \"card-body\"], [1, \"row\", \"g-3\"], [1, \"col-md-6\"], [\"for\", \"statusFilter\", 1, \"form-label\"], [\"id\", \"statusFilter\", 1, \"form-select\", 3, \"ngModel\", \"ngModelChange\", \"change\"], [\"value\", \"all\"], [\"value\", \"Active\"], [\"value\", \"Inactive\"], [\"value\", \"OnBreak\"], [\"value\", \"Offline\"], [\"for\", \"searchFilter\", 1, \"form-label\"], [1, \"input-group\"], [\"type\", \"text\", \"id\", \"searchFilter\", \"placeholder\", \"Rechercher...\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\", \"input\"], [1, \"input-group-text\"], [1, \"fa-solid\", \"fa-search\"], [1, \"card\"], [1, \"card-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"card-title\", \"mb-0\"], [1, \"badge\", \"bg-primary\"], [1, \"card-body\", \"p-0\"], [1, \"list-group\", \"list-group-flush\", \"drivers-list\"], [\"class\", \"list-group-item list-group-item-action p-3\", 3, \"active\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"p-4 text-center text-muted\", 4, \"ngIf\"], [1, \"col-lg-8\"], [\"class\", \"row g-4\", 4, \"ngIf\"], [\"class\", \"card\", 4, \"ngIf\"], [1, \"list-group-item\", \"list-group-item-action\", \"p-3\", 3, \"click\"], [1, \"d-flex\", \"align-items-center\"], [1, \"avatar\", \"me-3\"], [1, \"flex-grow-1\"], [1, \"mb-1\"], [1, \"text-muted\", \"small\", \"mb-0\"], [1, \"text-end\"], [3, \"status\", \"variant\"], [1, \"text-muted\", \"small\", \"mt-1\"], [1, \"p-4\", \"text-center\", \"text-muted\"], [1, \"fa-solid\", \"fa-users\", \"fa-2x\", \"mb-3\"], [1, \"col-12\"], [1, \"d-flex\"], [1, \"avatar-lg\", \"me-4\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-2\"], [1, \"mb-0\"], [1, \"row\"], [1, \"fa-solid\", \"fa-envelope\", \"text-muted\", \"me-2\"], [1, \"fa-solid\", \"fa-phone\", \"text-muted\", \"me-2\"], [1, \"fa-solid\", \"fa-car\", \"text-muted\", \"me-2\"], [\"class\", \"text-muted ms-1\", 4, \"ngIf\"], [1, \"fa-solid\", \"fa-calendar\", \"text-muted\", \"me-2\"], [1, \"fa-solid\", \"fa-id-card\", \"text-muted\", \"me-2\"], [1, \"fa-solid\", \"fa-clock\", \"text-muted\", \"me-2\"], [1, \"card\", \"h-100\"], [1, \"card-header\"], [1, \"fa-solid\", \"fa-chart-line\", \"me-2\", \"text-primary\"], [\"class\", \"text-center py-4\", 4, \"ngIf\"], [\"class\", \"row g-3\", 4, \"ngIf\"], [\"class\", \"text-center py-4 text-muted\", 4, \"ngIf\"], [1, \"fa-solid\", \"fa-location-dot\", \"me-2\", \"text-primary\"], [3, \"drivers\", \"height\", \"center\", \"zoom\"], [1, \"fa-solid\", \"fa-box\", \"me-2\", \"text-primary\"], [\"class\", \"table-responsive\", 4, \"ngIf\"], [1, \"fa-solid\", \"fa-pen-to-square\", \"me-2\"], [1, \"btn\", \"btn-success\"], [1, \"fa-solid\", \"fa-route\", \"me-2\"], [1, \"btn\", \"btn-outline-danger\", 3, \"click\"], [1, \"fa-solid\", \"fa-trash\", \"me-2\"], [1, \"text-muted\", \"ms-1\"], [1, \"text-center\", \"py-4\"], [\"role\", \"status\", 1, \"spinner-border\", \"spinner-border-sm\", \"text-primary\"], [1, \"col-6\"], [1, \"metric-mini-card\"], [1, \"metric-mini-title\"], [1, \"metric-mini-value\"], [1, \"text-center\", \"py-4\", \"text-muted\"], [1, \"fa-solid\", \"fa-chart-simple\", \"fa-2x\", \"mb-3\"], [1, \"table-responsive\"], [1, \"table\", \"table-hover\", \"mb-0\"], [4, \"ngFor\", \"ngForOf\"], [1, \"fa-solid\", \"fa-box-open\", \"fa-2x\", \"mb-3\"], [1, \"card-body\", \"text-center\", \"p-5\"], [1, \"fa-solid\", \"fa-user\", \"fa-3x\", \"text-muted\", \"mb-3\"], [1, \"modal-backdrop\", \"fade\"]],\n        template: function DriversManagementComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0);\n            i0.ɵɵelement(1, \"app-sidebar\");\n            i0.ɵɵelementStart(2, \"div\", 1)(3, \"app-header\", 2)(4, \"div\", 3)(5, \"button\", 4);\n            i0.ɵɵlistener(\"click\", function DriversManagementComponent_Template_button_click_5_listener() {\n              return ctx.onNewDriver();\n            });\n            i0.ɵɵelement(6, \"i\", 5);\n            i0.ɵɵtext(7, \" Nouveau livreur \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(8, \"button\", 6);\n            i0.ɵɵlistener(\"click\", function DriversManagementComponent_Template_button_click_8_listener() {\n              return ctx.onExport();\n            });\n            i0.ɵɵelement(9, \"i\", 7);\n            i0.ɵɵtext(10, \" Exporter \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(11, \"div\", 8);\n            i0.ɵɵtemplate(12, DriversManagementComponent_div_12_Template, 6, 0, \"div\", 9);\n            i0.ɵɵtemplate(13, DriversManagementComponent_div_13_Template, 2, 1, \"div\", 10);\n            i0.ɵɵtemplate(14, DriversManagementComponent_ng_container_14_Template, 40, 7, \"ng-container\", 11);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(15, \"div\", 12)(16, \"div\", 13)(17, \"div\", 14)(18, \"div\", 15)(19, \"h5\", 16);\n            i0.ɵɵtext(20);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(21, \"button\", 17);\n            i0.ɵɵlistener(\"click\", function DriversManagementComponent_Template_button_click_21_listener() {\n              return ctx.onDriverFormCancel();\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(22, \"div\", 18)(23, \"app-driver-form\", 19);\n            i0.ɵɵlistener(\"submitted\", function DriversManagementComponent_Template_app_driver_form_submitted_23_listener($event) {\n              return ctx.onDriverFormSubmit($event);\n            })(\"cancelled\", function DriversManagementComponent_Template_app_driver_form_cancelled_23_listener() {\n              return ctx.onDriverFormCancel();\n            });\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(24, \"div\", 12)(25, \"div\", 20)(26, \"div\", 14)(27, \"div\", 15)(28, \"h5\", 16);\n            i0.ɵɵtext(29, \"Confirmer la suppression\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(30, \"button\", 17);\n            i0.ɵɵlistener(\"click\", function DriversManagementComponent_Template_button_click_30_listener() {\n              return ctx.cancelDelete();\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(31, \"div\", 18)(32, \"p\");\n            i0.ɵɵtext(33, \"\\u00CAtes-vous s\\u00FBr de vouloir supprimer le livreur \");\n            i0.ɵɵelementStart(34, \"strong\");\n            i0.ɵɵtext(35);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtext(36, \" ?\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(37, \"p\", 21);\n            i0.ɵɵtext(38, \"Cette action est irr\\u00E9versible.\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(39, \"div\", 22)(40, \"button\", 23);\n            i0.ɵɵlistener(\"click\", function DriversManagementComponent_Template_button_click_40_listener() {\n              return ctx.cancelDelete();\n            });\n            i0.ɵɵtext(41, \"Annuler\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(42, \"button\", 24);\n            i0.ɵɵlistener(\"click\", function DriversManagementComponent_Template_button_click_42_listener() {\n              return ctx.confirmDelete();\n            });\n            i0.ɵɵtext(43, \"Supprimer\");\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵtemplate(44, DriversManagementComponent_div_44_Template, 1, 2, \"div\", 25);\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(12);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵstyleProp(\"display\", ctx.showDriverForm ? \"block\" : \"none\");\n            i0.ɵɵclassProp(\"show\", ctx.showDriverForm);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate1(\" \", ctx.editingDriver ? \"Modifier le livreur\" : \"Nouveau livreur\", \" \");\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"driver\", ctx.editingDriver);\n            i0.ɵɵadvance(1);\n            i0.ɵɵstyleProp(\"display\", ctx.showDeleteConfirm ? \"block\" : \"none\");\n            i0.ɵɵclassProp(\"show\", ctx.showDeleteConfirm);\n            i0.ɵɵadvance(11);\n            i0.ɵɵtextInterpolate(ctx.selectedDriver == null ? null : ctx.selectedDriver.name);\n            i0.ɵɵadvance(9);\n            i0.ɵɵproperty(\"ngIf\", ctx.showDriverForm || ctx.showDeleteConfirm);\n          }\n        },\n        dependencies: [i4.NgForOf, i4.NgIf, i5.NgSelectOption, i5.ɵNgSelectMultipleOption, i5.DefaultValueAccessor, i5.SelectControlValueAccessor, i5.NgControlStatus, i5.NgModel, i6.SidebarComponent, i7.HeaderComponent, i8.StatusBadgeComponent, i9.MapViewComponent, i10.DriverFormComponent, i4.DecimalPipe, i4.DatePipe],\n        styles: [\".drivers-container[_ngcontent-%COMP%]{display:flex;height:100vh;overflow:hidden}.drivers-content[_ngcontent-%COMP%]{flex:1;display:flex;flex-direction:column;overflow:hidden}.drivers-body[_ngcontent-%COMP%]{flex:1;overflow-y:auto;background-color:var(--gray-50)}.drivers-list[_ngcontent-%COMP%]{max-height:600px;overflow-y:auto}.list-group-item.active[_ngcontent-%COMP%]{background-color:var(--primary-light);color:var(--primary-dark);border-color:var(--primary-light)}.list-group-item.active[_ngcontent-%COMP%]   .text-muted[_ngcontent-%COMP%]{color:var(--primary-dark)!important;opacity:.8}.avatar[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;background-color:var(--primary-light);color:var(--primary-dark);display:flex;align-items:center;justify-content:center;font-weight:600}.avatar-lg[_ngcontent-%COMP%]{width:64px;height:64px;border-radius:50%;background-color:var(--primary-light);color:var(--primary-dark);display:flex;align-items:center;justify-content:center;font-weight:600;font-size:1.5rem}.metric-mini-card[_ngcontent-%COMP%]{background-color:#fff;border-radius:.5rem;padding:1rem;box-shadow:0 1px 2px #0000000d;border:1px solid var(--gray-200)}.metric-mini-title[_ngcontent-%COMP%]{font-size:.75rem;color:var(--gray-500);margin-bottom:.5rem}.metric-mini-value[_ngcontent-%COMP%]{font-size:1.25rem;font-weight:600;color:var(--gray-800)}\"]\n      });\n    }\n  }\n  return DriversManagementComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}