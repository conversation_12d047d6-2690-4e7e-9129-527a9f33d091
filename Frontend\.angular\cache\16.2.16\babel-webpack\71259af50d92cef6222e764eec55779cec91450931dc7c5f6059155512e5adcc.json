{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { environment } from '@env/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let DeliveryService = /*#__PURE__*/(() => {\n  class DeliveryService {\n    constructor(http) {\n      this.http = http;\n      this.apiUrl = `${environment.apiUrl}/deliveries`;\n    }\n    getDeliveries() {\n      return this.http.get(this.apiUrl);\n    }\n    getDeliveryById(id) {\n      return this.http.get(`${this.apiUrl}/${id}`);\n    }\n    getDelivery(id) {\n      return this.http.get(`${this.apiUrl}/${id}`);\n    }\n    getDeliveryByOrderId(orderId) {\n      return this.http.get(`${this.apiUrl}/order/${orderId}`);\n    }\n    getDeliveriesByDriverId(driverId) {\n      return this.http.get(`${this.apiUrl}/driver/${driverId}`);\n    }\n    getDeliveriesByCustomer(customerId) {\n      const params = new HttpParams().set('customerId', customerId);\n      return this.http.get(`${this.apiUrl}/customer`, {\n        params\n      });\n    }\n    getDeliveriesByStatus(status) {\n      const params = new HttpParams().set('status', status);\n      return this.http.get(this.apiUrl, {\n        params\n      });\n    }\n    createDelivery(delivery) {\n      return this.http.post(this.apiUrl, delivery);\n    }\n    updateDelivery(delivery) {\n      return this.http.put(`${this.apiUrl}/${delivery.id}`, delivery);\n    }\n    deleteDelivery(id) {\n      return this.http.delete(`${this.apiUrl}/${id}`);\n    }\n    updateDeliveryStatus(id, status) {\n      return this.http.put(`${this.apiUrl}/${id}/status`, {\n        status\n      });\n    }\n    getDeliveriesInArea(latitude, longitude, radiusKm) {\n      const params = new HttpParams().set('latitude', latitude.toString()).set('longitude', longitude.toString()).set('radiusKm', radiusKm.toString());\n      return this.http.get(`${this.apiUrl}/area`, {\n        params\n      });\n    }\n    static {\n      this.ɵfac = function DeliveryService_Factory(t) {\n        return new (t || DeliveryService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: DeliveryService,\n        factory: DeliveryService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return DeliveryService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}