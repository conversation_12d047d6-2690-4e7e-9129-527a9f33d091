import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { Subscription } from 'rxjs';
import { DeliveryService } from '../../core/services/delivery.service';
import { DriverService } from '../../core/services/driver.service';
import { RealTimeService } from '../../core/services/real-time.service';
import { AuthService } from '../../core/services/auth.service';
import { Delivery, DeliveryStatus } from '../../core/models/delivery.model';
import { Driver } from '../../core/models/driver.model';
import { User, UserRole } from '../../core/models/user.model';

@Component({
  selector: 'app-delivery-tracking',
  templateUrl: './delivery-tracking.component.html',
  styleUrls: ['./delivery-tracking.component.scss']
})
export class DeliveryTrackingComponent implements OnInit, OnDestroy {
  deliveries: Delivery[] = [];
  drivers: Driver[] = [];
  filteredDeliveries: Delivery[] = [];
  selectedDelivery: Delivery | null = null;

  // User context
  currentUser: User | null = null;
  isDriverView = false;

  statusFilter: string = 'all';
  searchTerm: string = '';

  loading = true;
  error = '';

  // Modal states
  showDeliveryForm = false;
  showDeleteConfirm = false;
  showStatusUpdate = false;
  editingDelivery: Delivery | null = null;
  deletingDelivery: Delivery | null = null;
  newStatus: string = '';

  private subscriptions: Subscription[] = [];

  constructor(
    private deliveryService: DeliveryService,
    private driverService: DriverService,
    private realTimeService: RealTimeService,
    private authService: AuthService
  ) { }

  ngOnInit(): void {
    // Check user role to determine view type
    this.currentUser = this.authService.getCurrentUser();
    this.isDriverView = this.currentUser?.role === UserRole.Driver;

    this.loadData();
    this.setupRealTimeUpdates();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  private loadData(): void {
    this.loading = true;

    if (this.isDriverView) {
      // Load driver's deliveries
      this.subscriptions.push(
        this.driverService.getCurrentDriverDeliveries().subscribe({
          next: (deliveries) => {
            this.deliveries = deliveries;
            this.applyFilters();
            this.loading = false;
          },
          error: (err) => {
            console.error('Error loading driver deliveries', err);
            this.error = 'Erreur lors du chargement de vos livraisons';
            this.loading = false;
          }
        })
      );
    } else {
      // Load all deliveries for admin/manager
      this.subscriptions.push(
        this.deliveryService.getDeliveries().subscribe({
          next: (deliveries) => {
            this.deliveries = deliveries;
            this.applyFilters();
            this.loading = false;
          },
          error: (err) => {
            console.error('Error loading deliveries', err);
            this.error = 'Erreur lors du chargement des livraisons';
            this.loading = false;
          }
        })
      );
    }

    // Load drivers
    this.subscriptions.push(
      this.driverService.getDrivers().subscribe({
        next: (drivers) => {
          this.drivers = drivers;
        },
        error: (err) => {
          console.error('Error loading drivers', err);
        }
      })
    );
  }

  private setupRealTimeUpdates(): void {
    // Start SignalR connection
    this.realTimeService.startConnection()
      .then(() => {
        console.log('Connected to real-time hub');
        this.realTimeService.joinAdminGroup();

        // Subscribe to delivery updates
        this.subscriptions.push(
          this.realTimeService.deliveryUpdates$.subscribe(delivery => {
            if (delivery) {
              this.updateDelivery(delivery);
            }
          })
        );
      })
      .catch(err => {
        console.error('Error connecting to real-time hub', err);
      });
  }

  private updateDelivery(updatedDelivery: Delivery): void {
    const index = this.deliveries.findIndex(d => d.id === updatedDelivery.id);

    if (index !== -1) {
      this.deliveries[index] = updatedDelivery;
    } else {
      this.deliveries.push(updatedDelivery);
    }

    this.applyFilters();

    // Update selected delivery if it's the one that was updated
    if (this.selectedDelivery && this.selectedDelivery.id === updatedDelivery.id) {
      this.selectedDelivery = updatedDelivery;
    }
  }

  applyFilters(): void {
    let filtered = [...this.deliveries];

    // Apply status filter
    if (this.statusFilter !== 'all') {
      filtered = filtered.filter(d => d.status === parseInt(this.statusFilter, 10));
    }

    // Apply search filter
    if (this.searchTerm.trim() !== '') {
      const search = this.searchTerm.toLowerCase();
      filtered = filtered.filter(d =>
        d.customerName.toLowerCase().includes(search) ||
        d.address.toLowerCase().includes(search) ||
        d.orderId.toLowerCase().includes(search) ||
        d.driverName?.toLowerCase().includes(search)
      );
    }

    this.filteredDeliveries = filtered;
  }

  selectDelivery(delivery: Delivery): void {
    this.selectedDelivery = delivery;
  }

  // CRUD Operations
  onNewDelivery(): void {
    this.editingDelivery = null;
    this.showDeliveryForm = true;
  }

  onEditDelivery(delivery: Delivery): void {
    this.editingDelivery = { ...delivery };
    this.showDeliveryForm = true;
  }

  onDeleteDelivery(delivery: Delivery): void {
    this.deletingDelivery = delivery;
    this.showDeleteConfirm = true;
  }

  onUpdateDeliveryStatus(delivery: Delivery): void {
    this.editingDelivery = delivery;
    this.newStatus = delivery.status.toString();
    this.showStatusUpdate = true;
  }

  // Form handlers
  onDeliveryFormSubmit(deliveryData: any): void {
    if (this.editingDelivery) {
      // Update existing delivery
      const updatedDelivery = { ...this.editingDelivery, ...deliveryData };
      this.subscriptions.push(
        this.deliveryService.updateDelivery(updatedDelivery).subscribe({
          next: () => {
            this.updateDelivery(updatedDelivery);
            this.showDeliveryForm = false;
            this.editingDelivery = null;
          },
          error: (err) => {
            console.error('Error updating delivery', err);
            this.error = 'Erreur lors de la mise à jour de la livraison';
          }
        })
      );
    } else {
      // Create new delivery
      this.subscriptions.push(
        this.deliveryService.createDelivery(deliveryData).subscribe({
          next: (newDelivery) => {
            this.deliveries.push(newDelivery);
            this.applyFilters();
            this.showDeliveryForm = false;
          },
          error: (err) => {
            console.error('Error creating delivery', err);
            this.error = 'Erreur lors de la création de la livraison';
          }
        })
      );
    }
  }

  onDeliveryFormCancel(): void {
    this.showDeliveryForm = false;
    this.editingDelivery = null;
  }

  // Delete handlers
  onDeleteConfirm(): void {
    if (this.deletingDelivery) {
      this.subscriptions.push(
        this.deliveryService.deleteDelivery(this.deletingDelivery.id).subscribe({
          next: () => {
            this.deliveries = this.deliveries.filter(d => d.id !== this.deletingDelivery!.id);
            this.applyFilters();
            if (this.selectedDelivery?.id === this.deletingDelivery!.id) {
              this.selectedDelivery = null;
            }
            this.showDeleteConfirm = false;
            this.deletingDelivery = null;
          },
          error: (err) => {
            console.error('Error deleting delivery', err);
            this.error = 'Erreur lors de la suppression de la livraison';
          }
        })
      );
    }
  }

  onDeleteCancel(): void {
    this.showDeleteConfirm = false;
    this.deletingDelivery = null;
  }

  // Status update handlers
  onStatusUpdateConfirm(): void {
    if (this.editingDelivery && this.newStatus) {
      const statusValue = parseInt(this.newStatus, 10) as DeliveryStatus;
      this.subscriptions.push(
        this.deliveryService.updateDeliveryStatus(this.editingDelivery.id, statusValue).subscribe({
          next: (updatedDelivery) => {
            this.updateDelivery(updatedDelivery);
            this.showStatusUpdate = false;
            this.editingDelivery = null;
            this.newStatus = '';
          },
          error: (err) => {
            console.error('Error updating delivery status', err);
            this.error = 'Erreur lors de la mise à jour du statut';
          }
        })
      );
    }
  }

  onStatusUpdateCancel(): void {
    this.showStatusUpdate = false;
    this.editingDelivery = null;
    this.newStatus = '';
  }

  // Export functionality
  onExport(): void {
    const csvData = this.convertToCSV(this.filteredDeliveries);
    const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `livraisons_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  private convertToCSV(deliveries: Delivery[]): string {
    const headers = ['ID Commande', 'Client', 'Adresse', 'Statut', 'Livreur', 'Date création', 'Heure estimée', 'Heure réelle'];
    const csvContent = [
      headers.join(','),
      ...deliveries.map(d => [
        d.orderId,
        `"${d.customerName}"`,
        `"${d.address}"`,
        this.getStatusText(d.status),
        `"${d.driverName || ''}"`,
        new Date(d.createdAt).toLocaleDateString(),
        d.estimatedDeliveryTime ? new Date(d.estimatedDeliveryTime).toLocaleString() : '',
        d.actualDeliveryTime ? new Date(d.actualDeliveryTime).toLocaleString() : ''
      ].join(','))
    ].join('\n');

    return csvContent;
  }

  getStatusClass(status: DeliveryStatus): 'success' | 'danger' | 'warning' | 'info' | 'default' {
    switch (status) {
      case DeliveryStatus.Delivered:
        return 'success';
      case DeliveryStatus.InTransit:
        return 'info';
      case DeliveryStatus.Delayed:
        return 'danger';
      case DeliveryStatus.Pending:
        return 'warning';
      case DeliveryStatus.Cancelled:
        return 'default';
      default:
        return 'default';
    }
  }

  getStatusText(status: DeliveryStatus): string {
    switch (status) {
      case DeliveryStatus.Delivered:
        return 'Livré';
      case DeliveryStatus.InTransit:
        return 'En cours';
      case DeliveryStatus.Delayed:
        return 'Retardé';
      case DeliveryStatus.Pending:
        return 'En attente';
      case DeliveryStatus.Cancelled:
        return 'Annulé';
      default:
        return status;
    }
  }

  getPriorityText(priority: any): string {
    const priorityValue = typeof priority === 'string' ? parseInt(priority, 10) : priority;
    switch (priorityValue) {
      case 0: // Low
        return 'Basse';
      case 1: // Medium
        return 'Moyenne';
      case 2: // High
        return 'Haute';
      case 3: // Urgent
        return 'Urgente';
      default:
        return priority;
    }
  }

  formatDate(date: Date | string): string {
    return new Date(date).toLocaleString();
  }

  getSelectedDrivers(): Driver[] {
    if (!this.selectedDelivery) return [];
    return this.drivers.filter(d => d.id === this.selectedDelivery?.driverId);
  }

  getMapCenter(): [number, number] | undefined {
    if (!this.selectedDelivery) return undefined;
    return [this.selectedDelivery.coordinates.latitude, this.selectedDelivery.coordinates.longitude];
  }
}
