{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"../loading-spinner/loading-spinner.component\";\nfunction DeliveryListComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵelement(1, \"app-loading-spinner\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DeliveryListComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"p\");\n    i0.ɵɵtext(2, \"Aucune livraison trouv\\u00E9e\");\n    i0.ɵɵelementEnd()();\n  }\n}\nconst _c0 = function (a0, a1, a2) {\n  return {\n    \"bg-success\": a0,\n    \"bg-warning\": a1,\n    \"bg-secondary\": a2\n  };\n};\nfunction DeliveryListComponent_div_3_tr_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\")(8, \"span\", 9);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"td\");\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const delivery_r4 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(delivery_r4.id);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(delivery_r4.customerName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(delivery_r4.address);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(9, _c0, delivery_r4.status === 2, delivery_r4.status === 1, delivery_r4.status === 0));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", delivery_r4.status, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(12, 6, delivery_r4.estimatedDeliveryTime, \"short\"));\n  }\n}\nfunction DeliveryListComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"table\", 7)(2, \"thead\")(3, \"tr\")(4, \"th\");\n    i0.ɵɵtext(5, \"ID\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7, \"Client\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"Adresse\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Statut\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\");\n    i0.ɵɵtext(13, \"Date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"tbody\");\n    i0.ɵɵtemplate(15, DeliveryListComponent_div_3_tr_15_Template, 13, 13, \"tr\", 8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.deliveries);\n  }\n}\nexport class DeliveryListComponent {\n  constructor() {\n    this.deliveries = [];\n    this.loading = false;\n  }\n  static {\n    this.ɵfac = function DeliveryListComponent_Factory(t) {\n      return new (t || DeliveryListComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DeliveryListComponent,\n      selectors: [[\"app-delivery-list\"]],\n      inputs: {\n        deliveries: \"deliveries\",\n        loading: \"loading\"\n      },\n      decls: 4,\n      vars: 3,\n      consts: [[1, \"delivery-list\"], [\"class\", \"text-center\", 4, \"ngIf\"], [\"class\", \"text-center text-muted\", 4, \"ngIf\"], [\"class\", \"table-responsive\", 4, \"ngIf\"], [1, \"text-center\"], [1, \"text-center\", \"text-muted\"], [1, \"table-responsive\"], [1, \"table\", \"table-striped\"], [4, \"ngFor\", \"ngForOf\"], [1, \"badge\", 3, \"ngClass\"]],\n      template: function DeliveryListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, DeliveryListComponent_div_1_Template, 2, 0, \"div\", 1);\n          i0.ɵɵtemplate(2, DeliveryListComponent_div_2_Template, 3, 0, \"div\", 2);\n          i0.ɵɵtemplate(3, DeliveryListComponent_div_3_Template, 16, 1, \"div\", 3);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.deliveries.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.deliveries.length > 0);\n        }\n      },\n      dependencies: [i1.NgClass, i1.NgForOf, i1.NgIf, i2.LoadingSpinnerComponent, i1.DatePipe],\n      styles: [\".delivery-list[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n.delivery-list[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2hhcmVkL2NvbXBvbmVudHMvZGVsaXZlcnktbGlzdC9kZWxpdmVyeS1saXN0LmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUNFO0VBQ0UsZ0JBQUE7QUFBSjtBQUdFO0VBQ0Usa0JBQUE7QUFESiIsInNvdXJjZXNDb250ZW50IjpbIi5kZWxpdmVyeS1saXN0IHtcbiAgLnRhYmxlIHtcbiAgICBtYXJnaW4tYm90dG9tOiAwO1xuICB9XG4gIFxuICAuYmFkZ2Uge1xuICAgIGZvbnQtc2l6ZTogMC43NXJlbTtcbiAgfVxufVxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate", "delivery_r4", "id", "customerName", "address", "ɵɵproperty", "ɵɵpureFunction3", "_c0", "status", "ɵɵtextInterpolate1", "ɵɵpipeBind2", "estimatedDeliveryTime", "ɵɵtemplate", "DeliveryListComponent_div_3_tr_15_Template", "ctx_r2", "deliveries", "DeliveryListComponent", "constructor", "loading", "selectors", "inputs", "decls", "vars", "consts", "template", "DeliveryListComponent_Template", "rf", "ctx", "DeliveryListComponent_div_1_Template", "DeliveryListComponent_div_2_Template", "DeliveryListComponent_div_3_Template", "length"], "sources": ["C:\\Users\\<USER>\\delivery-dash-optimiser\\frontend\\src\\app\\shared\\components\\delivery-list\\delivery-list.component.ts", "C:\\Users\\<USER>\\delivery-dash-optimiser\\frontend\\src\\app\\shared\\components\\delivery-list\\delivery-list.component.html"], "sourcesContent": ["import { Component, Input } from '@angular/core';\nimport { Delivery } from '@core/models/delivery.model';\n\n@Component({\n  selector: 'app-delivery-list',\n  templateUrl: './delivery-list.component.html',\n  styleUrls: ['./delivery-list.component.scss']\n})\nexport class DeliveryListComponent {\n  @Input() deliveries: Delivery[] = [];\n  @Input() loading: boolean = false;\n\n  constructor() { }\n}\n", "<div class=\"delivery-list\">\n  <div *ngIf=\"loading\" class=\"text-center\">\n    <app-loading-spinner></app-loading-spinner>\n  </div>\n\n  <div *ngIf=\"!loading && deliveries.length === 0\" class=\"text-center text-muted\">\n    <p>Aucune livraison trouvée</p>\n  </div>\n\n  <div *ngIf=\"!loading && deliveries.length > 0\" class=\"table-responsive\">\n    <table class=\"table table-striped\">\n      <thead>\n        <tr>\n          <th>ID</th>\n          <th>Client</th>\n          <th>Adresse</th>\n          <th>Statut</th>\n          <th>Date</th>\n        </tr>\n      </thead>\n      <tbody>\n        <tr *ngFor=\"let delivery of deliveries\">\n          <td>{{ delivery.id }}</td>\n          <td>{{ delivery.customerName }}</td>\n          <td>{{ delivery.address }}</td>\n          <td>\n            <span class=\"badge\" [ngClass]=\"{\n              'bg-success': delivery.status === 2,\n              'bg-warning': delivery.status === 1,\n              'bg-secondary': delivery.status === 0\n            }\">\n              {{ delivery.status }}\n            </span>\n          </td>\n          <td>{{ delivery.estimatedDeliveryTime | date:'short' }}</td>\n        </tr>\n      </tbody>\n    </table>\n  </div>\n</div>\n"], "mappings": ";;;;;ICCEA,EAAA,CAAAC,cAAA,aAAyC;IACvCD,EAAA,CAAAE,SAAA,0BAA2C;IAC7CF,EAAA,CAAAG,YAAA,EAAM;;;;;IAENH,EAAA,CAAAC,cAAA,aAAgF;IAC3ED,EAAA,CAAAI,MAAA,oCAAwB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;;;;;;;;;IAe3BH,EAAA,CAAAC,cAAA,SAAwC;IAClCD,EAAA,CAAAI,MAAA,GAAiB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC1BH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAI,MAAA,GAA2B;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACpCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAI,MAAA,GAAsB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC/BH,EAAA,CAAAC,cAAA,SAAI;IAMAD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAETH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAI,MAAA,IAAmD;;IAAAJ,EAAA,CAAAG,YAAA,EAAK;;;;IAZxDH,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAAM,iBAAA,CAAAC,WAAA,CAAAC,EAAA,CAAiB;IACjBR,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAAM,iBAAA,CAAAC,WAAA,CAAAE,YAAA,CAA2B;IAC3BT,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAM,iBAAA,CAAAC,WAAA,CAAAG,OAAA,CAAsB;IAEJV,EAAA,CAAAK,SAAA,GAIlB;IAJkBL,EAAA,CAAAW,UAAA,YAAAX,EAAA,CAAAY,eAAA,IAAAC,GAAA,EAAAN,WAAA,CAAAO,MAAA,QAAAP,WAAA,CAAAO,MAAA,QAAAP,WAAA,CAAAO,MAAA,QAIlB;IACAd,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAe,kBAAA,MAAAR,WAAA,CAAAO,MAAA,MACF;IAEEd,EAAA,CAAAK,SAAA,GAAmD;IAAnDL,EAAA,CAAAM,iBAAA,CAAAN,EAAA,CAAAgB,WAAA,QAAAT,WAAA,CAAAU,qBAAA,WAAmD;;;;;IAzB/DjB,EAAA,CAAAC,cAAA,aAAwE;IAI5DD,EAAA,CAAAI,MAAA,SAAE;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACXH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAI,MAAA,aAAM;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACfH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAI,MAAA,cAAO;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAChBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAI,MAAA,cAAM;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACfH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAI,MAAA,YAAI;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAGjBH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAAkB,UAAA,KAAAC,0CAAA,kBAcK;IACPnB,EAAA,CAAAG,YAAA,EAAQ;;;;IAfmBH,EAAA,CAAAK,SAAA,IAAa;IAAbL,EAAA,CAAAW,UAAA,YAAAS,MAAA,CAAAC,UAAA,CAAa;;;ADb9C,OAAM,MAAOC,qBAAqB;EAIhCC,YAAA;IAHS,KAAAF,UAAU,GAAe,EAAE;IAC3B,KAAAG,OAAO,GAAY,KAAK;EAEjB;;;uBAJLF,qBAAqB;IAAA;EAAA;;;YAArBA,qBAAqB;MAAAG,SAAA;MAAAC,MAAA;QAAAL,UAAA;QAAAG,OAAA;MAAA;MAAAG,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCRlChC,EAAA,CAAAC,cAAA,aAA2B;UACzBD,EAAA,CAAAkB,UAAA,IAAAgB,oCAAA,iBAEM;UAENlC,EAAA,CAAAkB,UAAA,IAAAiB,oCAAA,iBAEM;UAENnC,EAAA,CAAAkB,UAAA,IAAAkB,oCAAA,kBA6BM;UACRpC,EAAA,CAAAG,YAAA,EAAM;;;UAtCEH,EAAA,CAAAK,SAAA,GAAa;UAAbL,EAAA,CAAAW,UAAA,SAAAsB,GAAA,CAAAT,OAAA,CAAa;UAIbxB,EAAA,CAAAK,SAAA,GAAyC;UAAzCL,EAAA,CAAAW,UAAA,UAAAsB,GAAA,CAAAT,OAAA,IAAAS,GAAA,CAAAZ,UAAA,CAAAgB,MAAA,OAAyC;UAIzCrC,EAAA,CAAAK,SAAA,GAAuC;UAAvCL,EAAA,CAAAW,UAAA,UAAAsB,GAAA,CAAAT,OAAA,IAAAS,GAAA,CAAAZ,UAAA,CAAAgB,MAAA,KAAuC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}