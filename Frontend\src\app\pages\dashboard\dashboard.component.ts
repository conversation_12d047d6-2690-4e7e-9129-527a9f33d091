import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { Subscription } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../../environments/environment';
import { DeliveryService } from '@core/services/delivery.service';
import { DriverService } from '@core/services/driver.service';
import { AnalyticsService } from '@core/services/analytics.service';
import { RealTimeService } from '@core/services/real-time.service';
import { AuthService } from '@core/services/auth.service';
import { Delivery, DeliveryStatus } from '@core/models/delivery.model';
import { Driver, DriverStatus } from '@core/models/driver.model';
import { PerformanceMetric } from '@core/models/analytics.model';
import { User, UserRole } from '@core/models/user.model';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit, OnDestroy {
  deliveries: Delivery[] = [];
  drivers: Driver[] = [];
  performanceMetrics: PerformanceMetric[] = [];

  // User context
  currentUser: User | null = null;
  isDriverView = false;
  currentDriver: Driver | null = null;

  // API Connection
  apiConnected = false;
  apiUrl = environment.apiUrl;

  // Computed properties for template
  todayDeliveries: Delivery[] = [];
  inTransitDeliveries: Delivery[] = [];
  deliveredDeliveries: Delivery[] = [];

  totalDeliveries = 0;
  activeDrivers = 0;
  deliveredToday = 0;
  delayedDeliveries = 0;
  avgDeliveryTime = 0;
  onTimeRate = 0;

  chartData: any[] = [];
  revenueData: any[] = [];

  loading = true;
  error = '';

  private subscriptions: Subscription[] = [];

  constructor(
    private http: HttpClient,
    private deliveryService: DeliveryService,
    private driverService: DriverService,
    private analyticsService: AnalyticsService,
    private realTimeService: RealTimeService,
    private authService: AuthService
  ) { }

  ngOnInit(): void {
    // Check user role to determine view type
    this.currentUser = this.authService.getCurrentUser();
    this.isDriverView = this.currentUser?.role === UserRole.Driver;

    this.testApiConnection();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  private testApiConnection(): void {
    this.loading = true;
    this.error = '';

    // Test API connection with a simple health check
    this.http.get(`${environment.apiUrl}/health`).subscribe({
      next: (response) => {
        console.log('API connection successful:', response);
        this.apiConnected = true;
        this.loadData();
        this.setupRealTimeUpdates();
      },
      error: (err) => {
        console.error('API connection failed:', err);
        this.apiConnected = false;
        this.error = `Impossible de se connecter à l'API: ${err.message}`;
        this.loading = false;

        // Try to load data anyway in case the health endpoint doesn't exist
        this.loadData();
      }
    });
  }

  private loadData(): void {
    this.loading = true;

    if (this.isDriverView) {
      this.loadDriverData();
    } else {
      this.loadAdminData();
    }
  }

  private loadDriverData(): void {
    // Load current driver profile
    this.subscriptions.push(
      this.driverService.getCurrentDriver().subscribe({
        next: (driver) => {
          this.currentDriver = driver;
          this.apiConnected = true;
        },
        error: (err) => {
          console.error('Error loading driver profile', err);
          this.error = 'Erreur lors du chargement du profil livreur';
        }
      })
    );

    // Load driver's deliveries
    this.subscriptions.push(
      this.driverService.getCurrentDriverDeliveries().subscribe({
        next: (deliveries) => {
          this.deliveries = deliveries;
          this.calculateDeliveryMetrics();
          this.loading = false;
        },
        error: (err) => {
          console.error('Error loading driver deliveries', err);
          this.error = 'Erreur lors du chargement des livraisons';
          this.loading = false;
        }
      })
    );
  }

  private loadAdminData(): void {
    // Load deliveries
    this.subscriptions.push(
      this.deliveryService.getDeliveries().subscribe({
        next: (deliveries) => {
          this.deliveries = deliveries;
          this.calculateDeliveryMetrics();
          this.apiConnected = true;
        },
        error: (err) => {
          console.error('Error loading deliveries', err);
          this.error = 'Erreur lors du chargement des livraisons';
          this.loading = false;
        }
      })
    );

    // Load drivers
    this.subscriptions.push(
      this.driverService.getDrivers().subscribe({
        next: (drivers) => {
          this.drivers = drivers;
          this.calculateDriverMetrics();
        },
        error: (err) => {
          console.error('Error loading drivers', err);
          this.error = 'Erreur lors du chargement des livreurs';
          this.loading = false;
        }
      })
    );

    // Load performance metrics
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 7);

    this.subscriptions.push(
      this.analyticsService.getPerformanceMetrics(startDate).subscribe({
        next: (metrics) => {
          this.performanceMetrics = metrics;
          this.prepareChartData();
          this.loading = false;
        },
        error: (err) => {
          console.error('Error loading performance metrics', err);
          this.error = 'Erreur lors du chargement des métriques de performance';
          this.loading = false;
        }
      })
    );
  }

  private calculateDeliveryMetrics(): void {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    this.todayDeliveries = this.deliveries.filter(d => {
      const deliveryDate = new Date(d.estimatedDeliveryTime);
      deliveryDate.setHours(0, 0, 0, 0);
      return deliveryDate.getTime() === today.getTime();
    });

    this.inTransitDeliveries = this.deliveries.filter(d => d.status === DeliveryStatus.InTransit);
    this.deliveredDeliveries = this.deliveries.filter(d => d.status === DeliveryStatus.Delivered);

    this.totalDeliveries = this.deliveries.length;
    this.deliveredToday = this.deliveredDeliveries.length;
    this.delayedDeliveries = this.deliveries.filter(d => d.status === DeliveryStatus.Delayed).length;
  }

  private calculateDriverMetrics(): void {
    this.activeDrivers = this.drivers.filter(d => d.status === DriverStatus.Available).length;

    if (this.drivers.length > 0) {
      this.avgDeliveryTime = this.drivers.reduce((sum, driver) => sum + driver.avgDeliveryTime, 0) / this.drivers.length;
      this.onTimeRate = this.drivers.reduce((sum, driver) => sum + driver.onTimeRate, 0) / this.drivers.length;
    }
  }

  private prepareChartData(): void {
    this.chartData = this.performanceMetrics.map(item => ({
      date: new Date(item.date),
      value: item.totalDeliveries,
      secondary: item.onTimeDeliveries
    }));

    this.revenueData = this.performanceMetrics.map(item => ({
      date: new Date(item.date),
      value: item.revenue / 1000 // Convert to thousands
    }));
  }

  private setupRealTimeUpdates(): void {
    // Start SignalR connection
    this.realTimeService.startConnection()
      .then(() => {
        console.log('Connected to real-time hub');
        this.realTimeService.joinAdminGroup();

        // Subscribe to delivery updates
        this.subscriptions.push(
          this.realTimeService.deliveryUpdates$.subscribe(delivery => {
            if (delivery) {
              this.updateDelivery(delivery);
            }
          })
        );

        // Subscribe to driver updates
        this.subscriptions.push(
          this.realTimeService.driverUpdates$.subscribe(driver => {
            if (driver) {
              this.updateDriver(driver);
            }
          })
        );
      })
      .catch(err => {
        console.error('Error connecting to real-time hub', err);
      });
  }

  private updateDelivery(updatedDelivery: Delivery): void {
    const index = this.deliveries.findIndex(d => d.id === updatedDelivery.id);

    if (index !== -1) {
      this.deliveries[index] = updatedDelivery;
    } else {
      this.deliveries.push(updatedDelivery);
    }

    this.calculateDeliveryMetrics();
  }

  private updateDriver(updatedDriver: Driver): void {
    const index = this.drivers.findIndex(d => d.id === updatedDriver.id);

    if (index !== -1) {
      this.drivers[index] = updatedDriver;
    } else {
      this.drivers.push(updatedDriver);
    }

    this.calculateDriverMetrics();
  }

  // Helper methods for template
  getActiveDrivers(): Driver[] {
    return this.drivers.filter(d => d.status === DriverStatus.Available);
  }

  getRecentDeliveries(): Delivery[] {
    return this.deliveries.slice(0, 5);
  }

  getDeliveryStatusText(status: DeliveryStatus): string {
    switch (status) {
      case DeliveryStatus.Delivered: return 'Livré';
      case DeliveryStatus.InTransit: return 'En cours';
      case DeliveryStatus.Delayed: return 'Retardé';
      default: return 'En attente';
    }
  }

  getDeliveryStatusVariant(status: DeliveryStatus): string {
    switch (status) {
      case DeliveryStatus.Delivered: return 'success';
      case DeliveryStatus.InTransit: return 'info';
      case DeliveryStatus.Delayed: return 'danger';
      default: return 'warning';
    }
  }

  getDeliveryStatusClass(status: DeliveryStatus): string {
    switch (status) {
      case DeliveryStatus.Delivered: return 'bg-success';
      case DeliveryStatus.InTransit: return 'bg-info';
      case DeliveryStatus.Delayed: return 'bg-danger';
      default: return 'bg-warning';
    }
  }

  getAvgDeliveryTimeFormatted(): string {
    return (this.avgDeliveryTime || 0).toFixed(0) + ' min';
  }

  getOnTimeRateFormatted(): string {
    return (this.onTimeRate || 0).toFixed(0) + '%';
  }

  // Header button actions
  onNewDelivery(): void {
    // TODO: Open new delivery modal or navigate to delivery form
    console.log('New delivery clicked');
    // For now, just show an alert
    alert('Fonctionnalité "Nouvelle livraison" à venir!');
  }

  onRefresh(): void {
    this.testApiConnection();
  }
}
