import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Delivery, DeliveryStatus, DeliveryPriority } from '@core/models/delivery.model';
import { DriverService } from '@core/services/driver.service';
import { Driver } from '@core/models/driver.model';

@Component({
  selector: 'app-delivery-form',
  templateUrl: './delivery-form.component.html',
  styleUrls: ['./delivery-form.component.scss']
})
export class DeliveryFormComponent implements OnInit {
  @Input() delivery: Delivery | null = null;
  @Output() submitted = new EventEmitter<any>();
  @Output() cancelled = new EventEmitter<void>();

  deliveryForm: FormGroup;
  drivers: Driver[] = [];
  loading = false;

  // Enum options for dropdowns
  priorityOptions = [
    { value: DeliveryPriority.Low, label: 'Basse' },
    { value: DeliveryPriority.Medium, label: 'Moyenne' },
    { value: DeliveryPriority.High, label: 'Haute' },
    { value: DeliveryPriority.Urgent, label: 'Urgente' }
  ];

  statusOptions = [
    { value: DeliveryStatus.Pending, label: 'En attente' },
    { value: DeliveryStatus.InTransit, label: 'En cours' },
    { value: DeliveryStatus.Delivered, label: 'Livré' },
    { value: DeliveryStatus.Delayed, label: 'Retardé' },
    { value: DeliveryStatus.Cancelled, label: 'Annulé' }
  ];

  constructor(
    private fb: FormBuilder,
    private driverService: DriverService
  ) {
    this.deliveryForm = this.fb.group({
      orderId: ['', Validators.required],
      customerId: [''],
      customerName: ['', Validators.required],
      address: ['', Validators.required],
      phoneNumber: ['', Validators.required],
      driverId: ['', Validators.required],
      estimatedDeliveryTime: ['', Validators.required],
      priority: [DeliveryPriority.Medium, Validators.required],
      status: [DeliveryStatus.Pending, Validators.required],
      notes: [''],
      latitude: [48.8566, [Validators.required, Validators.min(-90), Validators.max(90)]],
      longitude: [2.3522, [Validators.required, Validators.min(-180), Validators.max(180)]]
    });
  }

  ngOnInit() {
    this.loadDrivers();

    if (this.delivery) {
      this.deliveryForm.patchValue({
        orderId: this.delivery.orderId,
        customerId: this.delivery.customerId || '',
        customerName: this.delivery.customerName,
        address: this.delivery.address,
        phoneNumber: this.delivery.customerFeedback || '', // Using available field
        driverId: this.delivery.driverId,
        estimatedDeliveryTime: this.formatDateForInput(this.delivery.estimatedDeliveryTime),
        priority: this.delivery.priority,
        status: this.delivery.status,
        notes: this.delivery.notes || '',
        latitude: this.delivery.coordinates?.latitude || 48.8566,
        longitude: this.delivery.coordinates?.longitude || 2.3522
      });
    } else {
      // Set default values for new delivery
      this.deliveryForm.patchValue({
        orderId: this.generateOrderId(),
        customerId: 'customer-001', // Default customer ID
        status: DeliveryStatus.Pending,
        priority: DeliveryPriority.Medium
      });
    }
  }

  private loadDrivers(): void {
    this.loading = true;
    this.driverService.getDrivers().subscribe({
      next: (drivers) => {
        this.drivers = drivers;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading drivers:', error);
        this.loading = false;
      }
    });
  }

  private generateOrderId(): string {
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `ORD-${timestamp}${random}`;
  }

  private formatDateForInput(date: Date | string): string {
    const d = new Date(date);
    return d.toISOString().slice(0, 16); // Format for datetime-local input
  }

  onSubmit() {
    if (this.deliveryForm.valid) {
      const formValue = this.deliveryForm.value;

      // Map form data to delivery object expected by backend
      const deliveryData = {
        id: '', // Always empty for new deliveries - backend will generate
        orderId: formValue.orderId,
        customerId: formValue.customerId,
        customerName: formValue.customerName,
        address: formValue.address,
        status: formValue.status,
        driverId: formValue.driverId,
        driverName: this.getDriverName(formValue.driverId),
        estimatedDeliveryTime: new Date(formValue.estimatedDeliveryTime).toISOString(),
        priority: formValue.priority,
        coordinates: {
          latitude: parseFloat(formValue.latitude),
          longitude: parseFloat(formValue.longitude)
        },
        notes: formValue.notes || '',
        createdAt: this.delivery?.createdAt || new Date().toISOString()
      };

      this.submitted.emit(deliveryData);
    }
  }

  private getDriverName(driverId: string): string {
    const driver = this.drivers.find(d => d.id === driverId);
    return driver ? driver.name : '';
  }

  onCancel() {
    this.cancelled.emit();
  }
}
