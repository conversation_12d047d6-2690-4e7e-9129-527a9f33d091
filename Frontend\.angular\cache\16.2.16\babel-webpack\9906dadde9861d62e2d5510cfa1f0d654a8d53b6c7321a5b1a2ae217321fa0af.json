{"ast": null, "code": "import { environment } from '../../../environments/environment';\nimport { DeliveryStatus } from '@core/models/delivery.model';\nimport { DriverStatus } from '@core/models/driver.model';\nimport { UserRole } from '@core/models/user.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@core/services/delivery.service\";\nimport * as i3 from \"@core/services/driver.service\";\nimport * as i4 from \"@core/services/analytics.service\";\nimport * as i5 from \"@core/services/real-time.service\";\nimport * as i6 from \"@core/services/auth.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"../../shared/components/sidebar/sidebar.component\";\nimport * as i9 from \"../../shared/components/header/header.component\";\nfunction DashboardComponent_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.onNewDelivery());\n    });\n    i0.ɵɵelement(1, \"i\", 21);\n    i0.ɵɵtext(2, \" Nouvelle livraison \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \\u2705 Connexion API r\\u00E9ussie - Backend accessible sur \", ctx_r1.apiUrl, \" \");\n  }\n}\nfunction DashboardComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵtext(1, \" \\u274C Connexion API \\u00E9chou\\u00E9e - V\\u00E9rifiez que le backend est d\\u00E9marr\\u00E9 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵtext(1, \" \\uD83D\\uDD04 Test de connexion en cours... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26)(2, \"span\", 27);\n    i0.ɵɵtext(3, \"Chargement...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction DashboardComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_21_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.error = \"\");\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.error, \" \");\n  }\n}\nfunction DashboardComponent_div_22_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35)(2, \"div\", 13)(3, \"h5\", 36);\n    i0.ɵɵtext(4, \"Mes livraisons aujourd'hui\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h2\", 37);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r11.currentDriver.todayDeliveries);\n  }\n}\nfunction DashboardComponent_div_22_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35)(2, \"div\", 13)(3, \"h5\", 36);\n    i0.ɵɵtext(4, \"Taux de ponctualit\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h2\", 38);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r12.currentDriver.onTimeRate.toFixed(1), \"%\");\n  }\n}\nfunction DashboardComponent_div_22_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35)(2, \"div\", 13)(3, \"h5\", 36);\n    i0.ɵɵtext(4, \"Note moyenne\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h2\", 39);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r13.currentDriver.rating.toFixed(1), \"/5\");\n  }\n}\nfunction DashboardComponent_div_22_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35)(2, \"div\", 13)(3, \"h5\", 36);\n    i0.ɵɵtext(4, \"Statut\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h2\", 40);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r14.currentDriver.status);\n  }\n}\nfunction DashboardComponent_div_22_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35)(2, \"div\", 13)(3, \"h5\", 36);\n    i0.ɵɵtext(4, \"Livraisons aujourd'hui\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h2\", 37);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r15.todayDeliveries.length);\n  }\n}\nfunction DashboardComponent_div_22_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35)(2, \"div\", 13)(3, \"h5\", 36);\n    i0.ɵɵtext(4, \"En cours\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h2\", 39);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r16.inTransitDeliveries.length);\n  }\n}\nfunction DashboardComponent_div_22_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35)(2, \"div\", 13)(3, \"h5\", 36);\n    i0.ɵɵtext(4, \"Livr\\u00E9es\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h2\", 38);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r17.deliveredDeliveries.length);\n  }\n}\nfunction DashboardComponent_div_22_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35)(2, \"div\", 13)(3, \"h5\", 36);\n    i0.ɵɵtext(4, \"Livreurs actifs\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h2\", 40);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r18.activeDrivers);\n  }\n}\nfunction DashboardComponent_div_22_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵtext(1, \" Aucune livraison trouv\\u00E9e \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_div_22_div_18_tr_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\")(6, \"span\", 45);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\");\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const delivery_r22 = ctx.$implicit;\n    const ctx_r21 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(delivery_r22.customerName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(delivery_r22.address);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r21.getDeliveryStatusClass(delivery_r22.status));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", delivery_r22.status, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(delivery_r22.driverName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(12, 6, delivery_r22.estimatedDeliveryTime, \"HH:mm\"));\n  }\n}\nfunction DashboardComponent_div_22_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"table\", 43)(2, \"thead\")(3, \"tr\")(4, \"th\");\n    i0.ɵɵtext(5, \"Client\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7, \"Adresse\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"Statut\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Livreur\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\");\n    i0.ɵɵtext(13, \"Heure pr\\u00E9vue\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"tbody\");\n    i0.ɵɵtemplate(15, DashboardComponent_div_22_div_18_tr_15_Template, 13, 9, \"tr\", 44);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r20.deliveries.slice(0, 10));\n  }\n}\nfunction DashboardComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 8);\n    i0.ɵɵtemplate(2, DashboardComponent_div_22_div_2_Template, 7, 1, \"div\", 30);\n    i0.ɵɵtemplate(3, DashboardComponent_div_22_div_3_Template, 7, 1, \"div\", 30);\n    i0.ɵɵtemplate(4, DashboardComponent_div_22_div_4_Template, 7, 1, \"div\", 30);\n    i0.ɵɵtemplate(5, DashboardComponent_div_22_div_5_Template, 7, 1, \"div\", 30);\n    i0.ɵɵtemplate(6, DashboardComponent_div_22_div_6_Template, 7, 1, \"div\", 30);\n    i0.ɵɵtemplate(7, DashboardComponent_div_22_div_7_Template, 7, 1, \"div\", 30);\n    i0.ɵɵtemplate(8, DashboardComponent_div_22_div_8_Template, 7, 1, \"div\", 30);\n    i0.ɵɵtemplate(9, DashboardComponent_div_22_div_9_Template, 7, 1, \"div\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 31)(11, \"div\", 9)(12, \"div\", 10)(13, \"div\", 11)(14, \"h5\", 12);\n    i0.ɵɵtext(15, \"Livraisons r\\u00E9centes\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 13);\n    i0.ɵɵtemplate(17, DashboardComponent_div_22_div_17_Template, 2, 0, \"div\", 32);\n    i0.ɵɵtemplate(18, DashboardComponent_div_22_div_18_Template, 16, 1, \"div\", 33);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.isDriverView && ctx_r6.currentDriver);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.isDriverView && ctx_r6.currentDriver);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.isDriverView && ctx_r6.currentDriver);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.isDriverView && ctx_r6.currentDriver);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r6.isDriverView);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r6.isDriverView);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r6.isDriverView);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r6.isDriverView);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.deliveries.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.deliveries.length > 0);\n  }\n}\nexport let DashboardComponent = /*#__PURE__*/(() => {\n  class DashboardComponent {\n    constructor(http, deliveryService, driverService, analyticsService, realTimeService, authService) {\n      this.http = http;\n      this.deliveryService = deliveryService;\n      this.driverService = driverService;\n      this.analyticsService = analyticsService;\n      this.realTimeService = realTimeService;\n      this.authService = authService;\n      this.deliveries = [];\n      this.drivers = [];\n      this.performanceMetrics = [];\n      // User context\n      this.currentUser = null;\n      this.isDriverView = false;\n      this.currentDriver = null;\n      // API Connection\n      this.apiConnected = false;\n      this.apiUrl = environment.apiUrl;\n      // Computed properties for template\n      this.todayDeliveries = [];\n      this.inTransitDeliveries = [];\n      this.deliveredDeliveries = [];\n      this.totalDeliveries = 0;\n      this.activeDrivers = 0;\n      this.deliveredToday = 0;\n      this.delayedDeliveries = 0;\n      this.avgDeliveryTime = 0;\n      this.onTimeRate = 0;\n      this.chartData = [];\n      this.revenueData = [];\n      this.loading = true;\n      this.error = '';\n      this.subscriptions = [];\n    }\n    ngOnInit() {\n      // Check user role to determine view type\n      this.currentUser = this.authService.getCurrentUser();\n      this.isDriverView = this.currentUser?.role === UserRole.Driver;\n      this.testApiConnection();\n    }\n    ngOnDestroy() {\n      this.subscriptions.forEach(sub => sub.unsubscribe());\n    }\n    testApiConnection() {\n      this.loading = true;\n      this.error = '';\n      // Test API connection with a simple health check\n      this.http.get(`${environment.apiUrl}/health`).subscribe({\n        next: response => {\n          console.log('API connection successful:', response);\n          this.apiConnected = true;\n          this.loadData();\n          this.setupRealTimeUpdates();\n        },\n        error: err => {\n          console.error('API connection failed:', err);\n          this.apiConnected = false;\n          this.error = `Impossible de se connecter à l'API: ${err.message}`;\n          this.loading = false;\n          // Try to load data anyway in case the health endpoint doesn't exist\n          this.loadData();\n        }\n      });\n    }\n    loadData() {\n      this.loading = true;\n      if (this.isDriverView) {\n        this.loadDriverData();\n      } else {\n        this.loadAdminData();\n      }\n    }\n    loadDriverData() {\n      // Load current driver profile\n      this.subscriptions.push(this.driverService.getCurrentDriver().subscribe({\n        next: driver => {\n          this.currentDriver = driver;\n          this.apiConnected = true;\n        },\n        error: err => {\n          console.error('Error loading driver profile', err);\n          this.error = 'Erreur lors du chargement du profil livreur';\n        }\n      }));\n      // Load driver's deliveries\n      this.subscriptions.push(this.driverService.getCurrentDriverDeliveries().subscribe({\n        next: deliveries => {\n          this.deliveries = deliveries;\n          this.calculateDeliveryMetrics();\n          this.loading = false;\n        },\n        error: err => {\n          console.error('Error loading driver deliveries', err);\n          this.error = 'Erreur lors du chargement des livraisons';\n          this.loading = false;\n        }\n      }));\n    }\n    loadAdminData() {\n      // Load deliveries\n      this.subscriptions.push(this.deliveryService.getDeliveries().subscribe({\n        next: deliveries => {\n          this.deliveries = deliveries;\n          this.calculateDeliveryMetrics();\n          this.apiConnected = true;\n        },\n        error: err => {\n          console.error('Error loading deliveries', err);\n          this.error = 'Erreur lors du chargement des livraisons';\n          this.loading = false;\n        }\n      }));\n      // Load drivers\n      this.subscriptions.push(this.driverService.getDrivers().subscribe({\n        next: drivers => {\n          this.drivers = drivers;\n          this.calculateDriverMetrics();\n        },\n        error: err => {\n          console.error('Error loading drivers', err);\n          this.error = 'Erreur lors du chargement des livreurs';\n          this.loading = false;\n        }\n      }));\n      // Load performance metrics\n      const startDate = new Date();\n      startDate.setDate(startDate.getDate() - 7);\n      this.subscriptions.push(this.analyticsService.getPerformanceMetrics(startDate).subscribe({\n        next: metrics => {\n          this.performanceMetrics = metrics;\n          this.prepareChartData();\n          this.loading = false;\n        },\n        error: err => {\n          console.error('Error loading performance metrics', err);\n          this.error = 'Erreur lors du chargement des métriques de performance';\n          this.loading = false;\n        }\n      }));\n    }\n    calculateDeliveryMetrics() {\n      const today = new Date();\n      today.setHours(0, 0, 0, 0);\n      this.todayDeliveries = this.deliveries.filter(d => {\n        const deliveryDate = new Date(d.estimatedDeliveryTime);\n        deliveryDate.setHours(0, 0, 0, 0);\n        return deliveryDate.getTime() === today.getTime();\n      });\n      this.inTransitDeliveries = this.deliveries.filter(d => d.status === DeliveryStatus.InTransit);\n      this.deliveredDeliveries = this.deliveries.filter(d => d.status === DeliveryStatus.Delivered);\n      this.totalDeliveries = this.deliveries.length;\n      this.deliveredToday = this.deliveredDeliveries.length;\n      this.delayedDeliveries = this.deliveries.filter(d => d.status === DeliveryStatus.Delayed).length;\n    }\n    calculateDriverMetrics() {\n      this.activeDrivers = this.drivers.filter(d => d.status === DriverStatus.Available).length;\n      if (this.drivers.length > 0) {\n        this.avgDeliveryTime = this.drivers.reduce((sum, driver) => sum + driver.avgDeliveryTime, 0) / this.drivers.length;\n        this.onTimeRate = this.drivers.reduce((sum, driver) => sum + driver.onTimeRate, 0) / this.drivers.length;\n      }\n    }\n    prepareChartData() {\n      this.chartData = this.performanceMetrics.map(item => ({\n        date: new Date(item.date),\n        value: item.totalDeliveries,\n        secondary: item.onTimeDeliveries\n      }));\n      this.revenueData = this.performanceMetrics.map(item => ({\n        date: new Date(item.date),\n        value: item.revenue / 1000 // Convert to thousands\n      }));\n    }\n\n    setupRealTimeUpdates() {\n      // Start SignalR connection\n      this.realTimeService.startConnection().then(() => {\n        console.log('Connected to real-time hub');\n        this.realTimeService.joinAdminGroup();\n        // Subscribe to delivery updates\n        this.subscriptions.push(this.realTimeService.deliveryUpdates$.subscribe(delivery => {\n          if (delivery) {\n            this.updateDelivery(delivery);\n          }\n        }));\n        // Subscribe to driver updates\n        this.subscriptions.push(this.realTimeService.driverUpdates$.subscribe(driver => {\n          if (driver) {\n            this.updateDriver(driver);\n          }\n        }));\n      }).catch(err => {\n        console.error('Error connecting to real-time hub', err);\n      });\n    }\n    updateDelivery(updatedDelivery) {\n      const index = this.deliveries.findIndex(d => d.id === updatedDelivery.id);\n      if (index !== -1) {\n        this.deliveries[index] = updatedDelivery;\n      } else {\n        this.deliveries.push(updatedDelivery);\n      }\n      this.calculateDeliveryMetrics();\n    }\n    updateDriver(updatedDriver) {\n      const index = this.drivers.findIndex(d => d.id === updatedDriver.id);\n      if (index !== -1) {\n        this.drivers[index] = updatedDriver;\n      } else {\n        this.drivers.push(updatedDriver);\n      }\n      this.calculateDriverMetrics();\n    }\n    // Helper methods for template\n    getActiveDrivers() {\n      return this.drivers.filter(d => d.status === DriverStatus.Available);\n    }\n    getRecentDeliveries() {\n      return this.deliveries.slice(0, 5);\n    }\n    getDeliveryStatusText(status) {\n      switch (status) {\n        case DeliveryStatus.Delivered:\n          return 'Livré';\n        case DeliveryStatus.InTransit:\n          return 'En cours';\n        case DeliveryStatus.Delayed:\n          return 'Retardé';\n        default:\n          return 'En attente';\n      }\n    }\n    getDeliveryStatusVariant(status) {\n      switch (status) {\n        case DeliveryStatus.Delivered:\n          return 'success';\n        case DeliveryStatus.InTransit:\n          return 'info';\n        case DeliveryStatus.Delayed:\n          return 'danger';\n        default:\n          return 'warning';\n      }\n    }\n    getDeliveryStatusClass(status) {\n      switch (status) {\n        case DeliveryStatus.Delivered:\n          return 'bg-success';\n        case DeliveryStatus.InTransit:\n          return 'bg-info';\n        case DeliveryStatus.Delayed:\n          return 'bg-danger';\n        default:\n          return 'bg-warning';\n      }\n    }\n    getAvgDeliveryTimeFormatted() {\n      return (this.avgDeliveryTime || 0).toFixed(0) + ' min';\n    }\n    getOnTimeRateFormatted() {\n      return (this.onTimeRate || 0).toFixed(0) + '%';\n    }\n    // Header button actions\n    onNewDelivery() {\n      // TODO: Open new delivery modal or navigate to delivery form\n      console.log('New delivery clicked');\n      // For now, just show an alert\n      alert('Fonctionnalité \"Nouvelle livraison\" à venir!');\n    }\n    onRefresh() {\n      this.testApiConnection();\n    }\n    static {\n      this.ɵfac = function DashboardComponent_Factory(t) {\n        return new (t || DashboardComponent)(i0.ɵɵdirectiveInject(i1.HttpClient), i0.ɵɵdirectiveInject(i2.DeliveryService), i0.ɵɵdirectiveInject(i3.DriverService), i0.ɵɵdirectiveInject(i4.AnalyticsService), i0.ɵɵdirectiveInject(i5.RealTimeService), i0.ɵɵdirectiveInject(i6.AuthService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: DashboardComponent,\n        selectors: [[\"app-dashboard\"]],\n        decls: 23,\n        vars: 12,\n        consts: [[1, \"dashboard-container\"], [1, \"dashboard-content\"], [3, \"title\", \"subtitle\"], [1, \"d-flex\", \"gap-2\"], [\"class\", \"btn btn-primary\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-secondary\", 3, \"disabled\", \"click\"], [1, \"fa-solid\", \"fa-sync\", \"me-2\"], [1, \"dashboard-body\", \"p-4\"], [1, \"row\", \"mb-4\"], [1, \"col-12\"], [1, \"card\"], [1, \"card-header\"], [1, \"card-title\", \"mb-0\"], [1, \"card-body\"], [\"class\", \"alert alert-success\", 4, \"ngIf\"], [\"class\", \"alert alert-danger\", 4, \"ngIf\"], [\"class\", \"alert alert-info\", 4, \"ngIf\"], [\"class\", \"d-flex justify-content-center align-items-center\", \"style\", \"height: 200px;\", 4, \"ngIf\"], [\"class\", \"alert alert-danger alert-dismissible fade show\", \"role\", \"alert\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"fa-solid\", \"fa-plus\", \"me-2\"], [1, \"alert\", \"alert-success\"], [1, \"alert\", \"alert-danger\"], [1, \"alert\", \"alert-info\"], [1, \"d-flex\", \"justify-content-center\", \"align-items-center\", 2, \"height\", \"200px\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [\"role\", \"alert\", 1, \"alert\", \"alert-danger\", \"alert-dismissible\", \"fade\", \"show\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"btn-close\", 3, \"click\"], [\"class\", \"col-md-3 mb-3\", 4, \"ngIf\"], [1, \"row\"], [\"class\", \"text-center text-muted py-4\", 4, \"ngIf\"], [\"class\", \"table-responsive\", 4, \"ngIf\"], [1, \"col-md-3\", \"mb-3\"], [1, \"card\", \"text-center\"], [1, \"card-title\"], [1, \"text-primary\"], [1, \"text-success\"], [1, \"text-warning\"], [1, \"text-info\"], [1, \"text-center\", \"text-muted\", \"py-4\"], [1, \"table-responsive\"], [1, \"table\", \"table-striped\"], [4, \"ngFor\", \"ngForOf\"], [1, \"badge\", 3, \"ngClass\"]],\n        template: function DashboardComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0);\n            i0.ɵɵelement(1, \"app-sidebar\");\n            i0.ɵɵelementStart(2, \"div\", 1)(3, \"app-header\", 2)(4, \"div\", 3);\n            i0.ɵɵtemplate(5, DashboardComponent_button_5_Template, 3, 0, \"button\", 4);\n            i0.ɵɵelementStart(6, \"button\", 5);\n            i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_6_listener() {\n              return ctx.onRefresh();\n            });\n            i0.ɵɵelement(7, \"i\", 6);\n            i0.ɵɵtext(8, \" Actualiser \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(9, \"div\", 7)(10, \"div\", 8)(11, \"div\", 9)(12, \"div\", 10)(13, \"div\", 11)(14, \"h5\", 12);\n            i0.ɵɵtext(15, \"\\u00C9tat de la connexion API\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(16, \"div\", 13);\n            i0.ɵɵtemplate(17, DashboardComponent_div_17_Template, 2, 1, \"div\", 14);\n            i0.ɵɵtemplate(18, DashboardComponent_div_18_Template, 2, 0, \"div\", 15);\n            i0.ɵɵtemplate(19, DashboardComponent_div_19_Template, 2, 0, \"div\", 16);\n            i0.ɵɵelementEnd()()()();\n            i0.ɵɵtemplate(20, DashboardComponent_div_20_Template, 4, 0, \"div\", 17);\n            i0.ɵɵtemplate(21, DashboardComponent_div_21_Template, 3, 1, \"div\", 18);\n            i0.ɵɵtemplate(22, DashboardComponent_div_22_Template, 19, 10, \"div\", 19);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"title\", ctx.isDriverView ? \"Tableau de bord livreur\" : \"Tableau de bord\")(\"subtitle\", ctx.isDriverView ? \"Vos livraisons et performances\" : \"Vue d'ensemble de vos op\\u00E9rations de livraison\");\n            i0.ɵɵadvance(2);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isDriverView);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"disabled\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵclassProp(\"fa-spin\", ctx.loading);\n            i0.ɵɵadvance(10);\n            i0.ɵɵproperty(\"ngIf\", ctx.apiConnected);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.apiConnected && !ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.apiConnected);\n          }\n        },\n        dependencies: [i7.NgClass, i7.NgForOf, i7.NgIf, i8.SidebarComponent, i9.HeaderComponent, i7.DatePipe],\n        styles: [\".dashboard-container[_ngcontent-%COMP%]{display:flex;height:100vh;overflow:hidden}.dashboard-content[_ngcontent-%COMP%]{flex:1;display:flex;flex-direction:column;overflow:hidden}.dashboard-body[_ngcontent-%COMP%]{flex:1;overflow-y:auto;background-color:var(--gray-50)}\"]\n      });\n    }\n  }\n  return DashboardComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}