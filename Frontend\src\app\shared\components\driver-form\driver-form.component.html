<form [formGroup]="driverForm" (ngSubmit)="onSubmit()">
  <!-- Validation Summary -->
  <div *ngIf="driverForm.invalid && driverForm.touched" class="alert alert-danger mb-3">
    <h6 class="alert-heading">
      <i class="fa-solid fa-exclamation-triangle me-2"></i>
      <PERSON><PERSON><PERSON>z corriger les erreurs suivantes:
    </h6>
    <ul class="mb-0">
      <li *ngIf="driverForm.get('name')?.invalid && driverForm.get('name')?.touched">
        Le nom est requis
      </li>
      <li *ngIf="driverForm.get('email')?.invalid && driverForm.get('email')?.touched">
        Un email valide est requis
      </li>
      <li *ngIf="driverForm.get('phone')?.invalid && driverForm.get('phone')?.touched">
        Le numéro de téléphone est requis
      </li>
      <li *ngIf="driverForm.get('vehicleType')?.invalid && driverForm.get('vehicleType')?.touched">
        Le type de véhicule est requis
      </li>
      <li *ngIf="driverForm.get('maxDeliveriesPerDay')?.invalid && driverForm.get('maxDeliveriesPerDay')?.touched">
        Le nombre de livraisons par jour doit être entre 1 et 50
      </li>
      <li *ngIf="driverForm.get('latitude')?.invalid && driverForm.get('latitude')?.touched">
        La latitude doit être entre -90 et 90
      </li>
      <li *ngIf="driverForm.get('longitude')?.invalid && driverForm.get('longitude')?.touched">
        La longitude doit être entre -180 et 180
      </li>
    </ul>
  </div>

  <div class="row">
    <!-- Left Column -->
    <div class="col-md-6">
      <div class="mb-3">
        <label for="name" class="form-label">Nom complet *</label>
        <input type="text" class="form-control" id="name" formControlName="name"
               [class.is-invalid]="driverForm.get('name')?.invalid && driverForm.get('name')?.touched">
        <div class="invalid-feedback">
          Le nom est requis
        </div>
      </div>

      <div class="mb-3">
        <label for="email" class="form-label">Email *</label>
        <input type="email" class="form-control" id="email" formControlName="email"
               [class.is-invalid]="driverForm.get('email')?.invalid && driverForm.get('email')?.touched">
        <div class="invalid-feedback">
          Un email valide est requis
        </div>
      </div>

      <div class="mb-3">
        <label for="phone" class="form-label">Téléphone *</label>
        <input type="tel" class="form-control" id="phone" formControlName="phone"
               [class.is-invalid]="driverForm.get('phone')?.invalid && driverForm.get('phone')?.touched">
        <div class="invalid-feedback">
          Le numéro de téléphone est requis
        </div>
      </div>

      <div class="mb-3">
        <label for="vehicleType" class="form-label">Type de véhicule *</label>
        <select class="form-select" id="vehicleType" formControlName="vehicleType"
                [class.is-invalid]="driverForm.get('vehicleType')?.invalid && driverForm.get('vehicleType')?.touched">
          <option value="">Sélectionner un type de véhicule</option>
          <option *ngFor="let option of vehicleTypeOptions" [value]="option.value">
            {{ option.label }}
          </option>
        </select>
        <div class="invalid-feedback">
          Le type de véhicule est requis
        </div>
      </div>

      <div class="mb-3">
        <label for="vehicleId" class="form-label">Immatriculation du véhicule</label>
        <input type="text" class="form-control" id="vehicleId" formControlName="vehicleId"
               placeholder="Ex: AB-123-CD">
      </div>

      <div class="mb-3">
        <label for="status" class="form-label">Statut *</label>
        <select class="form-select" id="status" formControlName="status">
          <option *ngFor="let option of statusOptions" [value]="option.value">
            {{ option.label }}
          </option>
        </select>
      </div>
    </div>

    <!-- Right Column -->
    <div class="col-md-6">
      <div class="mb-3">
        <label for="licenseNumber" class="form-label">Numéro de permis</label>
        <input type="text" class="form-control" id="licenseNumber" formControlName="licenseNumber"
               placeholder="Ex: 123456789">
      </div>

      <div class="mb-3">
        <label for="licenseExpiryDate" class="form-label">Date d'expiration du permis</label>
        <input type="date" class="form-control" id="licenseExpiryDate" formControlName="licenseExpiryDate">
      </div>

      <div class="mb-3">
        <label for="maxDeliveriesPerDay" class="form-label">Livraisons max par jour *</label>
        <input type="number" class="form-control" id="maxDeliveriesPerDay" formControlName="maxDeliveriesPerDay"
               min="1" max="50" [class.is-invalid]="driverForm.get('maxDeliveriesPerDay')?.invalid && driverForm.get('maxDeliveriesPerDay')?.touched">
        <div class="invalid-feedback">
          Nombre entre 1 et 50 requis
        </div>
      </div>

      <div class="mb-3">
        <label class="form-label">Position actuelle</label>
        <div class="row">
          <div class="col-6">
            <input type="number" class="form-control" placeholder="Latitude" formControlName="latitude"
                   step="0.000001" [class.is-invalid]="driverForm.get('latitude')?.invalid && driverForm.get('latitude')?.touched">
            <div class="invalid-feedback">
              Latitude invalide
            </div>
          </div>
          <div class="col-6">
            <input type="number" class="form-control" placeholder="Longitude" formControlName="longitude"
                   step="0.000001" [class.is-invalid]="driverForm.get('longitude')?.invalid && driverForm.get('longitude')?.touched">
            <div class="invalid-feedback">
              Longitude invalide
            </div>
          </div>
        </div>
        <small class="form-text text-muted">
          Position par défaut: Paris (48.8566, 2.3522)
        </small>
      </div>

      <div class="mb-3">
        <label for="preferredZones" class="form-label">Zones préférées</label>
        <textarea class="form-control" id="preferredZones" formControlName="preferredZones" rows="2"
                  placeholder="Ex: Centre-ville, Banlieue nord..."></textarea>
      </div>

      <div class="mb-3">
        <div class="form-check">
          <input class="form-check-input" type="checkbox" id="isAvailableForUrgentDeliveries"
                 formControlName="isAvailableForUrgentDeliveries">
          <label class="form-check-label" for="isAvailableForUrgentDeliveries">
            Disponible pour les livraisons urgentes
          </label>
        </div>
      </div>
    </div>
  </div>

  <div class="d-flex gap-2 justify-content-end">
    <button type="button" class="btn btn-secondary" (click)="onCancel()">Annuler</button>
    <button type="submit" class="btn btn-primary" [disabled]="!driverForm.valid">
      <i class="fa-solid fa-save me-2"></i>
      {{ driver ? 'Mettre à jour' : 'Créer le livreur' }}
    </button>
  </div>
</form>
