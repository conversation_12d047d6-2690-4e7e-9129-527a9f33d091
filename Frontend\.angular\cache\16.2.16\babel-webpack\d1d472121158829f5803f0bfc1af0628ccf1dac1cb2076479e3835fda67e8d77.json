{"ast": null, "code": "export var DeliveryStatus = /*#__PURE__*/function (DeliveryStatus) {\n  DeliveryStatus[DeliveryStatus[\"Pending\"] = 0] = \"Pending\";\n  DeliveryStatus[DeliveryStatus[\"InTransit\"] = 1] = \"InTransit\";\n  DeliveryStatus[DeliveryStatus[\"Delivered\"] = 2] = \"Delivered\";\n  DeliveryStatus[DeliveryStatus[\"Delayed\"] = 3] = \"Delayed\";\n  DeliveryStatus[DeliveryStatus[\"Cancelled\"] = 4] = \"Cancelled\";\n  return DeliveryStatus;\n}(DeliveryStatus || {});\nexport var DeliveryPriority = /*#__PURE__*/function (DeliveryPriority) {\n  DeliveryPriority[DeliveryPriority[\"Low\"] = 0] = \"Low\";\n  DeliveryPriority[DeliveryPriority[\"Medium\"] = 1] = \"Medium\";\n  DeliveryPriority[DeliveryPriority[\"High\"] = 2] = \"High\";\n  DeliveryPriority[DeliveryPriority[\"Urgent\"] = 3] = \"Urgent\";\n  return DeliveryPriority;\n}(DeliveryPriority || {});\nexport var WeatherCondition = /*#__PURE__*/function (WeatherCondition) {\n  WeatherCondition[WeatherCondition[\"Clear\"] = 0] = \"Clear\";\n  WeatherCondition[WeatherCondition[\"Cloudy\"] = 1] = \"Cloudy\";\n  WeatherCondition[WeatherCondition[\"Rainy\"] = 2] = \"Rainy\";\n  WeatherCondition[WeatherCondition[\"Snowy\"] = 3] = \"Snowy\";\n  WeatherCondition[WeatherCondition[\"Stormy\"] = 4] = \"Stormy\";\n  return WeatherCondition;\n}(WeatherCondition || {});\nexport var TrafficCondition = /*#__PURE__*/function (TrafficCondition) {\n  TrafficCondition[TrafficCondition[\"Light\"] = 0] = \"Light\";\n  TrafficCondition[TrafficCondition[\"Moderate\"] = 1] = \"Moderate\";\n  TrafficCondition[TrafficCondition[\"Heavy\"] = 2] = \"Heavy\";\n  TrafficCondition[TrafficCondition[\"Severe\"] = 3] = \"Severe\";\n  return TrafficCondition;\n}(TrafficCondition || {});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}