using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using DeliveryDashOptimizer.API.Models;
using DeliveryDashOptimizer.API.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace DeliveryDashOptimizer.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AnalyticsController : ControllerBase
    {
        private readonly IAnalyticsService _analyticsService;
        private readonly ILogger<AnalyticsController> _logger;

        public AnalyticsController(IAnalyticsService analyticsService, ILogger<AnalyticsController> logger)
        {
            _analyticsService = analyticsService;
            _logger = logger;
        }

        [HttpGet("performance")]
        [Authorize(Roles = "Admin,Manager")]
        public async Task<ActionResult<IEnumerable<PerformanceMetric>>> GetPerformanceMetrics([FromQuery] DateTime? startDate, [FromQuery] DateTime? endDate)
        {
            try
            {
                var metrics = await _analyticsService.GetPerformanceMetricsAsync(startDate, endDate);
                return Ok(metrics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving performance metrics");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("drivers/performance")]
        [Authorize(Roles = "Admin,Manager")]
        public async Task<ActionResult<IEnumerable<DriverPerformance>>> GetDriversPerformance([FromQuery] DateTime? startDate, [FromQuery] DateTime? endDate)
        {
            try
            {
                var driversPerformance = await _analyticsService.GetDriversPerformanceAsync(startDate, endDate);
                return Ok(driversPerformance);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving drivers performance");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("deliveries/status")]
        [Authorize(Roles = "Admin,Manager,Dispatcher")]
        public async Task<ActionResult<DeliveryStatusSummary>> GetDeliveryStatusSummary([FromQuery] DateTime? date)
        {
            try
            {
                var summary = await _analyticsService.GetDeliveryStatusSummaryAsync(date);
                return Ok(summary);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving delivery status summary");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("customer-satisfaction")]
        [Authorize(Roles = "Admin,Manager")]
        public async Task<ActionResult<CustomerSatisfactionMetrics>> GetCustomerSatisfactionMetrics([FromQuery] DateTime? startDate, [FromQuery] DateTime? endDate)
        {
            try
            {
                var metrics = await _analyticsService.GetCustomerSatisfactionMetricsAsync(startDate, endDate);
                return Ok(metrics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving customer satisfaction metrics");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("efficiency")]
        [Authorize(Roles = "Admin,Manager")]
        public async Task<ActionResult<EfficiencyMetrics>> GetEfficiencyMetrics([FromQuery] DateTime? startDate, [FromQuery] DateTime? endDate)
        {
            try
            {
                var metrics = await _analyticsService.GetEfficiencyMetricsAsync(startDate, endDate);
                return Ok(metrics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving efficiency metrics");
                return StatusCode(500, "Internal server error");
            }
        }
    }
}
