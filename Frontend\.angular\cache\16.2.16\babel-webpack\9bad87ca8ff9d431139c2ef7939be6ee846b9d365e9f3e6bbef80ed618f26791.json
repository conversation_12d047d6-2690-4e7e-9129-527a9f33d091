{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let UserManagementService = /*#__PURE__*/(() => {\n  class UserManagementService {\n    constructor(http) {\n      this.http = http;\n      this.apiUrl = `${environment.apiUrl}/user-management`;\n    }\n    // User CRUD operations\n    getAllUsers(role) {\n      let params = new HttpParams();\n      if (role !== undefined) {\n        params = params.set('role', role.toString());\n      }\n      return this.http.get(`${this.apiUrl}/users`, {\n        params\n      });\n    }\n    getUserById(id) {\n      return this.http.get(`${this.apiUrl}/users/${id}`);\n    }\n    createUser(request) {\n      return this.http.post(`${this.apiUrl}/users`, request);\n    }\n    updateUser(id, request) {\n      return this.http.put(`${this.apiUrl}/users/${id}`, request);\n    }\n    deleteUser(id) {\n      return this.http.delete(`${this.apiUrl}/users/${id}`);\n    }\n    activateUser(id) {\n      return this.http.put(`${this.apiUrl}/users/${id}/activate`, {});\n    }\n    deactivateUser(id) {\n      return this.http.put(`${this.apiUrl}/users/${id}/deactivate`, {});\n    }\n    // Session management\n    getUserSessions(userId) {\n      return this.http.get(`${this.apiUrl}/users/${userId}/sessions`);\n    }\n    terminateUserSession(userId, sessionId) {\n      return this.http.delete(`${this.apiUrl}/users/${userId}/sessions/${sessionId}`);\n    }\n    // Statistics\n    getUserStats() {\n      return this.http.get(`${this.apiUrl}/stats`);\n    }\n    // Notifications\n    getUserNotifications(userId) {\n      return this.http.get(`${this.apiUrl}/users/${userId}/notifications`);\n    }\n    markNotificationAsRead(userId, notificationId) {\n      return this.http.put(`${this.apiUrl}/users/${userId}/notifications/${notificationId}/read`, {});\n    }\n    static {\n      this.ɵfac = function UserManagementService_Factory(t) {\n        return new (t || UserManagementService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: UserManagementService,\n        factory: UserManagementService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return UserManagementService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}