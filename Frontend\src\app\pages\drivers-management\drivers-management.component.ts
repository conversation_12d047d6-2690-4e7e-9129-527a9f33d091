import { Component, OnInit, OnDestroy } from '@angular/core';
import { Subscription } from 'rxjs';
import { DriverService } from '../../core/services/driver.service';
import { DeliveryService } from '../../core/services/delivery.service';
import { RealTimeService } from '../../core/services/real-time.service';
import { Driver, DriverStatus, DriverPerformance } from '../../core/models/driver.model';
import { Delivery, DeliveryStatus } from '../../core/models/delivery.model';

@Component({
  selector: 'app-drivers-management',
  templateUrl: './drivers-management.component.html',
  styleUrls: ['./drivers-management.component.scss']
})
export class DriversManagementComponent implements OnInit, OnDestroy {
  drivers: Driver[] = [];
  filteredDrivers: Driver[] = [];
  selectedDriver: Driver | null = null;
  driverPerformance: DriverPerformance | null = null;
  driverDeliveries: Delivery[] = [];

  statusFilter: string = 'all';
  searchTerm: string = '';

  loading = true;
  performanceLoading = false;
  deliveriesLoading = false;
  error = '';

  // Modal states
  showDriverForm = false;
  showDeleteConfirm = false;
  editingDriver: Driver | null = null;

  private subscriptions: Subscription[] = [];

  constructor(
    private driverService: DriverService,
    private deliveryService: DeliveryService,
    private realTimeService: RealTimeService
  ) { }

  ngOnInit(): void {
    this.loadDrivers();
    this.setupRealTimeUpdates();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  private loadDrivers(): void {
    this.loading = true;

    this.subscriptions.push(
      this.driverService.getDrivers().subscribe({
        next: (drivers) => {
          this.drivers = drivers;
          this.applyFilters();
          this.loading = false;
        },
        error: (err) => {
          console.error('Error loading drivers', err);
          this.error = 'Erreur lors du chargement des livreurs';
          this.loading = false;
        }
      })
    );
  }

  private setupRealTimeUpdates(): void {
    // Start SignalR connection
    this.realTimeService.startConnection()
      .then(() => {
        console.log('Connected to real-time hub');
        this.realTimeService.joinAdminGroup();

        // Subscribe to driver updates
        this.subscriptions.push(
          this.realTimeService.driverUpdates$.subscribe(driver => {
            if (driver) {
              this.updateDriver(driver);
            }
          })
        );
      })
      .catch(err => {
        console.error('Error connecting to real-time hub', err);
      });
  }

  private updateDriver(updatedDriver: Driver): void {
    const index = this.drivers.findIndex(d => d.id === updatedDriver.id);

    if (index !== -1) {
      this.drivers[index] = updatedDriver;
    } else {
      this.drivers.push(updatedDriver);
    }

    this.applyFilters();

    // Update selected driver if it's the one that was updated
    if (this.selectedDriver && this.selectedDriver.id === updatedDriver.id) {
      this.selectedDriver = updatedDriver;
    }
  }

  applyFilters(): void {
    let filtered = [...this.drivers];

    // Apply status filter
    if (this.statusFilter !== 'all') {
      filtered = filtered.filter(d => d.status === parseInt(this.statusFilter, 10));
    }

    // Apply search filter
    if (this.searchTerm.trim() !== '') {
      const search = this.searchTerm.toLowerCase();
      filtered = filtered.filter(d =>
        d.name.toLowerCase().includes(search) ||
        d.email.toLowerCase().includes(search) ||
        d.phone.toLowerCase().includes(search) ||
        d.vehicleType.toLowerCase().includes(search)
      );
    }

    this.filteredDrivers = filtered;
  }

  selectDriver(driver: Driver): void {
    this.selectedDriver = driver;
    this.loadDriverPerformance(driver.id);
    this.loadDriverDeliveries(driver.id);
  }

  loadDriverPerformance(driverId: string): void {
    this.performanceLoading = true;

    this.subscriptions.push(
      this.driverService.getDriverPerformance(driverId).subscribe({
        next: (performance) => {
          this.driverPerformance = performance;
          this.performanceLoading = false;
        },
        error: (err) => {
          console.error('Error loading driver performance', err);
          this.performanceLoading = false;
        }
      })
    );
  }

  loadDriverDeliveries(driverId: string): void {
    this.deliveriesLoading = true;

    this.subscriptions.push(
      this.deliveryService.getDeliveriesByDriverId(driverId).subscribe({
        next: (deliveries) => {
          this.driverDeliveries = deliveries;
          this.deliveriesLoading = false;
        },
        error: (err) => {
          console.error('Error loading driver deliveries', err);
          this.deliveriesLoading = false;
        }
      })
    );
  }

  getStatusClass(status: DriverStatus): 'success' | 'danger' | 'warning' | 'info' | 'default' {
    switch (status) {
      case DriverStatus.Available:
        return 'success';
      case DriverStatus.Busy:
        return 'info';
      case DriverStatus.OnBreak:
        return 'warning';
      case DriverStatus.Offline:
        return 'default';
      default:
        return 'default';
    }
  }

  getStatusText(status: DriverStatus): string {
    switch (status) {
      case DriverStatus.Available:
        return 'Disponible';
      case DriverStatus.Busy:
        return 'Occupé';
      case DriverStatus.OnBreak:
        return 'En pause';
      case DriverStatus.Offline:
        return 'Hors ligne';
      default:
        return status;
    }
  }

  getDeliveryStatusClass(status: DeliveryStatus): 'success' | 'danger' | 'warning' | 'info' | 'default' {
    switch (status) {
      case DeliveryStatus.Delivered:
        return 'success';
      case DeliveryStatus.InTransit:
        return 'info';
      case DeliveryStatus.Delayed:
        return 'danger';
      case DeliveryStatus.Pending:
        return 'warning';
      case DeliveryStatus.Cancelled:
        return 'default';
      default:
        return 'default';
    }
  }

  getDeliveryStatusText(status: DeliveryStatus): string {
    switch (status) {
      case DeliveryStatus.Delivered:
        return 'Livré';
      case DeliveryStatus.InTransit:
        return 'En cours';
      case DeliveryStatus.Delayed:
        return 'Retardé';
      case DeliveryStatus.Pending:
        return 'En attente';
      case DeliveryStatus.Cancelled:
        return 'Annulé';
      default:
        return String(status);
    }
  }

  formatDate(date: Date | string): string {
    return new Date(date).toLocaleString();
  }

  getDriverMapCenter(): [number, number] | undefined {
    if (!this.selectedDriver) return undefined;
    return [this.selectedDriver.currentLocation.latitude, this.selectedDriver.currentLocation.longitude];
  }

  // CRUD Operations
  onNewDriver(): void {
    this.editingDriver = null;
    this.showDriverForm = true;
  }

  onEditDriver(driver: Driver): void {
    this.editingDriver = driver;
    this.showDriverForm = true;
  }

  onDeleteDriver(driver: Driver): void {
    this.selectedDriver = driver;
    this.showDeleteConfirm = true;
  }

  onDriverFormSubmit(driverData: any): void {
    if (this.editingDriver) {
      // Update existing driver
      const updatedDriver: Driver = {
        ...this.editingDriver,
        ...driverData
      };

      this.subscriptions.push(
        this.driverService.updateDriver(updatedDriver).subscribe({
          next: () => {
            this.loadDrivers();
            this.showDriverForm = false;
            this.editingDriver = null;
          },
          error: (err) => {
            console.error('Error updating driver', err);
            this.error = 'Erreur lors de la mise à jour du livreur';
          }
        })
      );
    } else {
      // Create new driver - use all data from form, only add missing fields
      const newDriver: Driver = {
        ...driverData,
        // Only override fields that are not in the form or need defaults
        rating: 0,
        totalDeliveries: 0,
        onTimeRate: 0,
        todayDeliveries: 0,
        avgDeliveryTime: 0,
        lastActive: new Date(),
        hireDate: new Date()
      };

      this.subscriptions.push(
        this.driverService.createDriver(newDriver).subscribe({
          next: () => {
            this.loadDrivers();
            this.showDriverForm = false;
          },
          error: (err) => {
            console.error('Error creating driver', err);
            this.error = 'Erreur lors de la création du livreur';
          }
        })
      );
    }
  }

  onDriverFormCancel(): void {
    this.showDriverForm = false;
    this.editingDriver = null;
  }

  confirmDelete(): void {
    if (this.selectedDriver) {
      this.subscriptions.push(
        this.driverService.deleteDriver(this.selectedDriver.id).subscribe({
          next: () => {
            this.loadDrivers();
            this.selectedDriver = null;
            this.showDeleteConfirm = false;
          },
          error: (err) => {
            console.error('Error deleting driver', err);
            this.error = 'Erreur lors de la suppression du livreur';
          }
        })
      );
    }
  }

  cancelDelete(): void {
    this.showDeleteConfirm = false;
    this.selectedDriver = null;
  }

  onExport(): void {
    // TODO: Implement export functionality
    alert('Fonctionnalité d\'export à venir!');
  }
}
