{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport { DeliveryStatus, DeliveryPriority } from '@core/models/delivery.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@core/services/driver.service\";\nimport * as i3 from \"@angular/common\";\nfunction DeliveryFormComponent_option_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const driver_r4 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", driver_r4.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", driver_r4.name, \" - \", driver_r4.vehicleType, \" \");\n  }\n}\nfunction DeliveryFormComponent_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵelement(1, \"i\", 37);\n    i0.ɵɵtext(2, \" Chargement des livreurs... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DeliveryFormComponent_option_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r5.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", option_r5.label, \" \");\n  }\n}\nfunction DeliveryFormComponent_option_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const option_r6 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r6.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", option_r6.label, \" \");\n  }\n}\nexport let DeliveryFormComponent = /*#__PURE__*/(() => {\n  class DeliveryFormComponent {\n    constructor(fb, driverService) {\n      this.fb = fb;\n      this.driverService = driverService;\n      this.delivery = null;\n      this.submitted = new EventEmitter();\n      this.cancelled = new EventEmitter();\n      this.drivers = [];\n      this.loading = false;\n      // Enum options for dropdowns\n      this.priorityOptions = [{\n        value: DeliveryPriority.Low,\n        label: 'Basse'\n      }, {\n        value: DeliveryPriority.Medium,\n        label: 'Moyenne'\n      }, {\n        value: DeliveryPriority.High,\n        label: 'Haute'\n      }, {\n        value: DeliveryPriority.Urgent,\n        label: 'Urgente'\n      }];\n      this.statusOptions = [{\n        value: DeliveryStatus.Pending,\n        label: 'En attente'\n      }, {\n        value: DeliveryStatus.InTransit,\n        label: 'En cours'\n      }, {\n        value: DeliveryStatus.Delivered,\n        label: 'Livré'\n      }, {\n        value: DeliveryStatus.Delayed,\n        label: 'Retardé'\n      }, {\n        value: DeliveryStatus.Cancelled,\n        label: 'Annulé'\n      }];\n      this.deliveryForm = this.fb.group({\n        orderId: ['', Validators.required],\n        customerId: [''],\n        customerName: ['', Validators.required],\n        address: ['', Validators.required],\n        phoneNumber: ['', Validators.required],\n        driverId: ['', Validators.required],\n        estimatedDeliveryTime: ['', Validators.required],\n        priority: [DeliveryPriority.Medium, Validators.required],\n        status: [DeliveryStatus.Pending, Validators.required],\n        notes: [''],\n        latitude: [48.8566, [Validators.required, Validators.min(-90), Validators.max(90)]],\n        longitude: [2.3522, [Validators.required, Validators.min(-180), Validators.max(180)]]\n      });\n    }\n    ngOnInit() {\n      this.loadDrivers();\n      if (this.delivery) {\n        this.deliveryForm.patchValue({\n          orderId: this.delivery.orderId,\n          customerId: this.delivery.customerId || '',\n          customerName: this.delivery.customerName,\n          address: this.delivery.address,\n          phoneNumber: this.delivery.customerFeedback || '',\n          driverId: this.delivery.driverId,\n          estimatedDeliveryTime: this.formatDateForInput(this.delivery.estimatedDeliveryTime),\n          priority: this.delivery.priority,\n          status: this.delivery.status,\n          notes: this.delivery.notes || '',\n          latitude: this.delivery.coordinates?.latitude || 48.8566,\n          longitude: this.delivery.coordinates?.longitude || 2.3522\n        });\n      } else {\n        // Set default values for new delivery\n        this.deliveryForm.patchValue({\n          orderId: this.generateOrderId(),\n          customerId: 'customer-001',\n          status: DeliveryStatus.Pending,\n          priority: DeliveryPriority.Medium\n        });\n      }\n    }\n    loadDrivers() {\n      this.loading = true;\n      this.driverService.getDrivers().subscribe({\n        next: drivers => {\n          this.drivers = drivers;\n          this.loading = false;\n        },\n        error: error => {\n          console.error('Error loading drivers:', error);\n          this.loading = false;\n        }\n      });\n    }\n    generateOrderId() {\n      const timestamp = Date.now().toString().slice(-6);\n      const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');\n      return `ORD-${timestamp}${random}`;\n    }\n    formatDateForInput(date) {\n      const d = new Date(date);\n      return d.toISOString().slice(0, 16); // Format for datetime-local input\n    }\n\n    onSubmit() {\n      if (this.deliveryForm.valid) {\n        const formValue = this.deliveryForm.value;\n        // Map form data to delivery object expected by backend\n        const deliveryData = {\n          id: '',\n          orderId: formValue.orderId,\n          customerId: formValue.customerId,\n          customerName: formValue.customerName,\n          address: formValue.address,\n          status: formValue.status,\n          driverId: formValue.driverId,\n          driverName: this.getDriverName(formValue.driverId),\n          estimatedDeliveryTime: new Date(formValue.estimatedDeliveryTime).toISOString(),\n          priority: formValue.priority,\n          coordinates: {\n            latitude: parseFloat(formValue.latitude),\n            longitude: parseFloat(formValue.longitude)\n          },\n          notes: formValue.notes || '',\n          createdAt: this.delivery?.createdAt || new Date().toISOString()\n        };\n        this.submitted.emit(deliveryData);\n      }\n    }\n    getDriverName(driverId) {\n      const driver = this.drivers.find(d => d.id === driverId);\n      return driver ? driver.name : '';\n    }\n    onCancel() {\n      this.cancelled.emit();\n    }\n    static {\n      this.ɵfac = function DeliveryFormComponent_Factory(t) {\n        return new (t || DeliveryFormComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.DriverService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: DeliveryFormComponent,\n        selectors: [[\"app-delivery-form\"]],\n        inputs: {\n          delivery: \"delivery\"\n        },\n        outputs: {\n          submitted: \"submitted\",\n          cancelled: \"cancelled\"\n        },\n        decls: 76,\n        vars: 21,\n        consts: [[3, \"formGroup\", \"ngSubmit\"], [1, \"row\"], [1, \"col-md-6\"], [1, \"mb-3\"], [\"for\", \"orderId\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"orderId\", \"formControlName\", \"orderId\", \"readonly\", \"\", 1, \"form-control\"], [\"for\", \"customerName\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"customerName\", \"formControlName\", \"customerName\", 1, \"form-control\"], [1, \"invalid-feedback\"], [\"for\", \"address\", 1, \"form-label\"], [\"id\", \"address\", \"formControlName\", \"address\", \"rows\", \"2\", 1, \"form-control\"], [\"for\", \"phoneNumber\", 1, \"form-label\"], [\"type\", \"tel\", \"id\", \"phoneNumber\", \"formControlName\", \"phoneNumber\", 1, \"form-control\"], [\"for\", \"estimatedDeliveryTime\", 1, \"form-label\"], [\"type\", \"datetime-local\", \"id\", \"estimatedDeliveryTime\", \"formControlName\", \"estimatedDeliveryTime\", 1, \"form-control\"], [\"for\", \"driverId\", 1, \"form-label\"], [\"id\", \"driverId\", \"formControlName\", \"driverId\", 1, \"form-select\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"form-text\", 4, \"ngIf\"], [\"for\", \"priority\", 1, \"form-label\"], [\"id\", \"priority\", \"formControlName\", \"priority\", 1, \"form-select\"], [\"for\", \"status\", 1, \"form-label\"], [\"id\", \"status\", \"formControlName\", \"status\", 1, \"form-select\"], [1, \"form-label\"], [1, \"col-6\"], [\"type\", \"number\", \"placeholder\", \"Latitude\", \"formControlName\", \"latitude\", \"step\", \"0.000001\", 1, \"form-control\"], [\"type\", \"number\", \"placeholder\", \"Longitude\", \"formControlName\", \"longitude\", \"step\", \"0.000001\", 1, \"form-control\"], [1, \"form-text\", \"text-muted\"], [\"for\", \"notes\", 1, \"form-label\"], [\"id\", \"notes\", \"formControlName\", \"notes\", \"rows\", \"3\", \"placeholder\", \"Instructions sp\\u00E9ciales, commentaires...\", 1, \"form-control\"], [1, \"d-flex\", \"gap-2\", \"justify-content-end\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"disabled\"], [1, \"fa-solid\", \"fa-save\", \"me-2\"], [3, \"value\"], [1, \"form-text\"], [1, \"fa-solid\", \"fa-spinner\", \"fa-spin\", \"me-1\"]],\n        template: function DeliveryFormComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"form\", 0);\n            i0.ɵɵlistener(\"ngSubmit\", function DeliveryFormComponent_Template_form_ngSubmit_0_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"label\", 4);\n            i0.ɵɵtext(5, \"ID Commande\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(6, \"input\", 5);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(7, \"div\", 3)(8, \"label\", 6);\n            i0.ɵɵtext(9, \"Nom du client *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(10, \"input\", 7);\n            i0.ɵɵelementStart(11, \"div\", 8);\n            i0.ɵɵtext(12, \" Le nom du client est requis \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(13, \"div\", 3)(14, \"label\", 9);\n            i0.ɵɵtext(15, \"Adresse de livraison *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(16, \"textarea\", 10);\n            i0.ɵɵelementStart(17, \"div\", 8);\n            i0.ɵɵtext(18, \" L'adresse de livraison est requise \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(19, \"div\", 3)(20, \"label\", 11);\n            i0.ɵɵtext(21, \"T\\u00E9l\\u00E9phone *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(22, \"input\", 12);\n            i0.ɵɵelementStart(23, \"div\", 8);\n            i0.ɵɵtext(24, \" Le num\\u00E9ro de t\\u00E9l\\u00E9phone est requis \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(25, \"div\", 3)(26, \"label\", 13);\n            i0.ɵɵtext(27, \"Date et heure pr\\u00E9vues *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(28, \"input\", 14);\n            i0.ɵɵelementStart(29, \"div\", 8);\n            i0.ɵɵtext(30, \" La date et heure de livraison sont requises \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(31, \"div\", 2)(32, \"div\", 3)(33, \"label\", 15);\n            i0.ɵɵtext(34, \"Livreur assign\\u00E9 *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(35, \"select\", 16)(36, \"option\", 17);\n            i0.ɵɵtext(37, \"S\\u00E9lectionner un livreur\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(38, DeliveryFormComponent_option_38_Template, 2, 3, \"option\", 18);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(39, \"div\", 8);\n            i0.ɵɵtext(40, \" Un livreur doit \\u00EAtre assign\\u00E9 \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(41, DeliveryFormComponent_div_41_Template, 3, 0, \"div\", 19);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(42, \"div\", 3)(43, \"label\", 20);\n            i0.ɵɵtext(44, \"Priorit\\u00E9 *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(45, \"select\", 21);\n            i0.ɵɵtemplate(46, DeliveryFormComponent_option_46_Template, 2, 2, \"option\", 18);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(47, \"div\", 3)(48, \"label\", 22);\n            i0.ɵɵtext(49, \"Statut *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(50, \"select\", 23);\n            i0.ɵɵtemplate(51, DeliveryFormComponent_option_51_Template, 2, 2, \"option\", 18);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(52, \"div\", 3)(53, \"label\", 24);\n            i0.ɵɵtext(54, \"Coordonn\\u00E9es GPS\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(55, \"div\", 1)(56, \"div\", 25);\n            i0.ɵɵelement(57, \"input\", 26);\n            i0.ɵɵelementStart(58, \"div\", 8);\n            i0.ɵɵtext(59, \" Latitude invalide \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(60, \"div\", 25);\n            i0.ɵɵelement(61, \"input\", 27);\n            i0.ɵɵelementStart(62, \"div\", 8);\n            i0.ɵɵtext(63, \" Longitude invalide \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(64, \"small\", 28);\n            i0.ɵɵtext(65, \" Coordonn\\u00E9es par d\\u00E9faut: Paris (48.8566, 2.3522) \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(66, \"div\", 3)(67, \"label\", 29);\n            i0.ɵɵtext(68, \"Notes\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(69, \"textarea\", 30);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(70, \"div\", 31)(71, \"button\", 32);\n            i0.ɵɵlistener(\"click\", function DeliveryFormComponent_Template_button_click_71_listener() {\n              return ctx.onCancel();\n            });\n            i0.ɵɵtext(72, \"Annuler\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(73, \"button\", 33);\n            i0.ɵɵelement(74, \"i\", 34);\n            i0.ɵɵtext(75);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            let tmp_1_0;\n            let tmp_2_0;\n            let tmp_3_0;\n            let tmp_4_0;\n            let tmp_5_0;\n            let tmp_10_0;\n            let tmp_11_0;\n            i0.ɵɵproperty(\"formGroup\", ctx.deliveryForm);\n            i0.ɵɵadvance(10);\n            i0.ɵɵclassProp(\"is-invalid\", ((tmp_1_0 = ctx.deliveryForm.get(\"customerName\")) == null ? null : tmp_1_0.invalid) && ((tmp_1_0 = ctx.deliveryForm.get(\"customerName\")) == null ? null : tmp_1_0.touched));\n            i0.ɵɵadvance(6);\n            i0.ɵɵclassProp(\"is-invalid\", ((tmp_2_0 = ctx.deliveryForm.get(\"address\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.deliveryForm.get(\"address\")) == null ? null : tmp_2_0.touched));\n            i0.ɵɵadvance(6);\n            i0.ɵɵclassProp(\"is-invalid\", ((tmp_3_0 = ctx.deliveryForm.get(\"phoneNumber\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx.deliveryForm.get(\"phoneNumber\")) == null ? null : tmp_3_0.touched));\n            i0.ɵɵadvance(6);\n            i0.ɵɵclassProp(\"is-invalid\", ((tmp_4_0 = ctx.deliveryForm.get(\"estimatedDeliveryTime\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx.deliveryForm.get(\"estimatedDeliveryTime\")) == null ? null : tmp_4_0.touched));\n            i0.ɵɵadvance(7);\n            i0.ɵɵclassProp(\"is-invalid\", ((tmp_5_0 = ctx.deliveryForm.get(\"driverId\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx.deliveryForm.get(\"driverId\")) == null ? null : tmp_5_0.touched));\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngForOf\", ctx.drivers);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngForOf\", ctx.priorityOptions);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"ngForOf\", ctx.statusOptions);\n            i0.ɵɵadvance(6);\n            i0.ɵɵclassProp(\"is-invalid\", ((tmp_10_0 = ctx.deliveryForm.get(\"latitude\")) == null ? null : tmp_10_0.invalid) && ((tmp_10_0 = ctx.deliveryForm.get(\"latitude\")) == null ? null : tmp_10_0.touched));\n            i0.ɵɵadvance(4);\n            i0.ɵɵclassProp(\"is-invalid\", ((tmp_11_0 = ctx.deliveryForm.get(\"longitude\")) == null ? null : tmp_11_0.invalid) && ((tmp_11_0 = ctx.deliveryForm.get(\"longitude\")) == null ? null : tmp_11_0.touched));\n            i0.ɵɵadvance(12);\n            i0.ɵɵproperty(\"disabled\", !ctx.deliveryForm.valid || ctx.loading);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate1(\" \", ctx.delivery ? \"Mettre \\u00E0 jour\" : \"Cr\\u00E9er la livraison\", \" \");\n          }\n        },\n        dependencies: [i3.NgForOf, i3.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName]\n      });\n    }\n  }\n  return DeliveryFormComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}