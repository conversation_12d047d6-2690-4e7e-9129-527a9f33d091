{"ast": null, "code": "import { environment } from '../../../environments/environment';\nimport { DeliveryStatus } from '@core/models/delivery.model';\nimport { DriverStatus } from '@core/models/driver.model';\nimport { UserRole } from '@core/models/user.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@core/services/delivery.service\";\nimport * as i3 from \"@core/services/driver.service\";\nimport * as i4 from \"@core/services/analytics.service\";\nimport * as i5 from \"@core/services/real-time.service\";\nimport * as i6 from \"@core/services/auth.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"../../shared/components/sidebar/sidebar.component\";\nimport * as i9 from \"../../shared/components/header/header.component\";\nfunction DashboardComponent_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_button_5_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.onNewDelivery());\n    });\n    i0.ɵɵelement(1, \"i\", 21);\n    i0.ɵɵtext(2, \" Nouvelle livraison \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \\u2705 Connexion API r\\u00E9ussie - Backend accessible sur \", ctx_r1.apiUrl, \" \");\n  }\n}\nfunction DashboardComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵtext(1, \" \\u274C Connexion API \\u00E9chou\\u00E9e - V\\u00E9rifiez que le backend est d\\u00E9marr\\u00E9 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵtext(1, \" \\uD83D\\uDD04 Test de connexion en cours... \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26)(2, \"span\", 27);\n    i0.ɵɵtext(3, \"Chargement...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction DashboardComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_21_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.error = \"\");\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.error, \" \");\n  }\n}\nfunction DashboardComponent_div_22_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35)(2, \"div\", 13)(3, \"h5\", 36);\n    i0.ɵɵtext(4, \"Mes livraisons aujourd'hui\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h2\", 37);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r11.currentDriver.todayDeliveries);\n  }\n}\nfunction DashboardComponent_div_22_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35)(2, \"div\", 13)(3, \"h5\", 36);\n    i0.ɵɵtext(4, \"Taux de ponctualit\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h2\", 38);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r12.currentDriver.onTimeRate.toFixed(1), \"%\");\n  }\n}\nfunction DashboardComponent_div_22_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35)(2, \"div\", 13)(3, \"h5\", 36);\n    i0.ɵɵtext(4, \"Note moyenne\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h2\", 39);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r13.currentDriver.rating.toFixed(1), \"/5\");\n  }\n}\nfunction DashboardComponent_div_22_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35)(2, \"div\", 13)(3, \"h5\", 36);\n    i0.ɵɵtext(4, \"Statut\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h2\", 40);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r14.currentDriver.status);\n  }\n}\nfunction DashboardComponent_div_22_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35)(2, \"div\", 13)(3, \"h5\", 36);\n    i0.ɵɵtext(4, \"Livraisons aujourd'hui\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h2\", 37);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r15.todayDeliveries.length);\n  }\n}\nfunction DashboardComponent_div_22_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35)(2, \"div\", 13)(3, \"h5\", 36);\n    i0.ɵɵtext(4, \"En cours\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h2\", 39);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r16.inTransitDeliveries.length);\n  }\n}\nfunction DashboardComponent_div_22_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35)(2, \"div\", 13)(3, \"h5\", 36);\n    i0.ɵɵtext(4, \"Livr\\u00E9es\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h2\", 38);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r17.deliveredDeliveries.length);\n  }\n}\nfunction DashboardComponent_div_22_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35)(2, \"div\", 13)(3, \"h5\", 36);\n    i0.ɵɵtext(4, \"Livreurs actifs\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h2\", 40);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r18.activeDrivers);\n  }\n}\nfunction DashboardComponent_div_22_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵtext(1, \" Aucune livraison trouv\\u00E9e \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_div_22_div_18_tr_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\")(6, \"span\", 45);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"td\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\");\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const delivery_r22 = ctx.$implicit;\n    const ctx_r21 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(delivery_r22.customerName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(delivery_r22.address);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r21.getDeliveryStatusClass(delivery_r22.status));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", delivery_r22.status, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(delivery_r22.driverName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(12, 6, delivery_r22.estimatedDeliveryTime, \"HH:mm\"));\n  }\n}\nfunction DashboardComponent_div_22_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"table\", 43)(2, \"thead\")(3, \"tr\")(4, \"th\");\n    i0.ɵɵtext(5, \"Client\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7, \"Adresse\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"Statut\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Livreur\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\");\n    i0.ɵɵtext(13, \"Heure pr\\u00E9vue\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"tbody\");\n    i0.ɵɵtemplate(15, DashboardComponent_div_22_div_18_tr_15_Template, 13, 9, \"tr\", 44);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r20.deliveries.slice(0, 10));\n  }\n}\nfunction DashboardComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 8);\n    i0.ɵɵtemplate(2, DashboardComponent_div_22_div_2_Template, 7, 1, \"div\", 30);\n    i0.ɵɵtemplate(3, DashboardComponent_div_22_div_3_Template, 7, 1, \"div\", 30);\n    i0.ɵɵtemplate(4, DashboardComponent_div_22_div_4_Template, 7, 1, \"div\", 30);\n    i0.ɵɵtemplate(5, DashboardComponent_div_22_div_5_Template, 7, 1, \"div\", 30);\n    i0.ɵɵtemplate(6, DashboardComponent_div_22_div_6_Template, 7, 1, \"div\", 30);\n    i0.ɵɵtemplate(7, DashboardComponent_div_22_div_7_Template, 7, 1, \"div\", 30);\n    i0.ɵɵtemplate(8, DashboardComponent_div_22_div_8_Template, 7, 1, \"div\", 30);\n    i0.ɵɵtemplate(9, DashboardComponent_div_22_div_9_Template, 7, 1, \"div\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 31)(11, \"div\", 9)(12, \"div\", 10)(13, \"div\", 11)(14, \"h5\", 12);\n    i0.ɵɵtext(15, \"Livraisons r\\u00E9centes\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 13);\n    i0.ɵɵtemplate(17, DashboardComponent_div_22_div_17_Template, 2, 0, \"div\", 32);\n    i0.ɵɵtemplate(18, DashboardComponent_div_22_div_18_Template, 16, 1, \"div\", 33);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.isDriverView && ctx_r6.currentDriver);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.isDriverView && ctx_r6.currentDriver);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.isDriverView && ctx_r6.currentDriver);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.isDriverView && ctx_r6.currentDriver);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r6.isDriverView);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r6.isDriverView);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r6.isDriverView);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r6.isDriverView);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.deliveries.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.deliveries.length > 0);\n  }\n}\nexport class DashboardComponent {\n  constructor(http, deliveryService, driverService, analyticsService, realTimeService, authService) {\n    this.http = http;\n    this.deliveryService = deliveryService;\n    this.driverService = driverService;\n    this.analyticsService = analyticsService;\n    this.realTimeService = realTimeService;\n    this.authService = authService;\n    this.deliveries = [];\n    this.drivers = [];\n    this.performanceMetrics = [];\n    // User context\n    this.currentUser = null;\n    this.isDriverView = false;\n    this.currentDriver = null;\n    // API Connection\n    this.apiConnected = false;\n    this.apiUrl = environment.apiUrl;\n    // Computed properties for template\n    this.todayDeliveries = [];\n    this.inTransitDeliveries = [];\n    this.deliveredDeliveries = [];\n    this.totalDeliveries = 0;\n    this.activeDrivers = 0;\n    this.deliveredToday = 0;\n    this.delayedDeliveries = 0;\n    this.avgDeliveryTime = 0;\n    this.onTimeRate = 0;\n    this.chartData = [];\n    this.revenueData = [];\n    this.loading = true;\n    this.error = '';\n    this.subscriptions = [];\n  }\n  ngOnInit() {\n    // Check user role to determine view type\n    this.currentUser = this.authService.getCurrentUser();\n    this.isDriverView = this.currentUser?.role === UserRole.Driver;\n    this.testApiConnection();\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n  testApiConnection() {\n    this.loading = true;\n    this.error = '';\n    // Test API connection with a simple health check\n    this.http.get(`${environment.apiUrl}/health`).subscribe({\n      next: response => {\n        console.log('API connection successful:', response);\n        this.apiConnected = true;\n        this.loadData();\n        this.setupRealTimeUpdates();\n      },\n      error: err => {\n        console.error('API connection failed:', err);\n        this.apiConnected = false;\n        this.error = `Impossible de se connecter à l'API: ${err.message}`;\n        this.loading = false;\n        // Try to load data anyway in case the health endpoint doesn't exist\n        this.loadData();\n      }\n    });\n  }\n  loadData() {\n    this.loading = true;\n    if (this.isDriverView) {\n      this.loadDriverData();\n    } else {\n      this.loadAdminData();\n    }\n  }\n  loadDriverData() {\n    // Load current driver profile\n    this.subscriptions.push(this.driverService.getCurrentDriver().subscribe({\n      next: driver => {\n        this.currentDriver = driver;\n        this.apiConnected = true;\n      },\n      error: err => {\n        console.error('Error loading driver profile', err);\n        this.error = 'Erreur lors du chargement du profil livreur';\n      }\n    }));\n    // Load driver's deliveries\n    this.subscriptions.push(this.driverService.getCurrentDriverDeliveries().subscribe({\n      next: deliveries => {\n        this.deliveries = deliveries;\n        this.calculateDeliveryMetrics();\n        this.loading = false;\n      },\n      error: err => {\n        console.error('Error loading driver deliveries', err);\n        this.error = 'Erreur lors du chargement des livraisons';\n        this.loading = false;\n      }\n    }));\n  }\n  loadAdminData() {\n    // Load deliveries\n    this.subscriptions.push(this.deliveryService.getDeliveries().subscribe({\n      next: deliveries => {\n        this.deliveries = deliveries;\n        this.calculateDeliveryMetrics();\n        this.apiConnected = true;\n      },\n      error: err => {\n        console.error('Error loading deliveries', err);\n        this.error = 'Erreur lors du chargement des livraisons';\n        this.loading = false;\n      }\n    }));\n    // Load drivers\n    this.subscriptions.push(this.driverService.getDrivers().subscribe({\n      next: drivers => {\n        this.drivers = drivers;\n        this.calculateDriverMetrics();\n      },\n      error: err => {\n        console.error('Error loading drivers', err);\n        this.error = 'Erreur lors du chargement des livreurs';\n        this.loading = false;\n      }\n    }));\n    // Load performance metrics\n    const startDate = new Date();\n    startDate.setDate(startDate.getDate() - 7);\n    this.subscriptions.push(this.analyticsService.getPerformanceMetrics(startDate).subscribe({\n      next: metrics => {\n        this.performanceMetrics = metrics;\n        this.prepareChartData();\n        this.loading = false;\n      },\n      error: err => {\n        console.error('Error loading performance metrics', err);\n        this.error = 'Erreur lors du chargement des métriques de performance';\n        this.loading = false;\n      }\n    }));\n  }\n  calculateDeliveryMetrics() {\n    const today = new Date();\n    today.setHours(0, 0, 0, 0);\n    this.todayDeliveries = this.deliveries.filter(d => {\n      const deliveryDate = new Date(d.estimatedDeliveryTime);\n      deliveryDate.setHours(0, 0, 0, 0);\n      return deliveryDate.getTime() === today.getTime();\n    });\n    this.inTransitDeliveries = this.deliveries.filter(d => d.status === DeliveryStatus.InTransit);\n    this.deliveredDeliveries = this.deliveries.filter(d => d.status === DeliveryStatus.Delivered);\n    this.totalDeliveries = this.deliveries.length;\n    this.deliveredToday = this.deliveredDeliveries.length;\n    this.delayedDeliveries = this.deliveries.filter(d => d.status === DeliveryStatus.Delayed).length;\n  }\n  calculateDriverMetrics() {\n    this.activeDrivers = this.drivers.filter(d => d.status === DriverStatus.Available).length;\n    if (this.drivers.length > 0) {\n      this.avgDeliveryTime = this.drivers.reduce((sum, driver) => sum + driver.avgDeliveryTime, 0) / this.drivers.length;\n      this.onTimeRate = this.drivers.reduce((sum, driver) => sum + driver.onTimeRate, 0) / this.drivers.length;\n    }\n  }\n  prepareChartData() {\n    this.chartData = this.performanceMetrics.map(item => ({\n      date: new Date(item.date),\n      value: item.totalDeliveries,\n      secondary: item.onTimeDeliveries\n    }));\n    this.revenueData = this.performanceMetrics.map(item => ({\n      date: new Date(item.date),\n      value: item.revenue / 1000 // Convert to thousands\n    }));\n  }\n\n  setupRealTimeUpdates() {\n    // Start SignalR connection\n    this.realTimeService.startConnection().then(() => {\n      console.log('Connected to real-time hub');\n      this.realTimeService.joinAdminGroup();\n      // Subscribe to delivery updates\n      this.subscriptions.push(this.realTimeService.deliveryUpdates$.subscribe(delivery => {\n        if (delivery) {\n          this.updateDelivery(delivery);\n        }\n      }));\n      // Subscribe to driver updates\n      this.subscriptions.push(this.realTimeService.driverUpdates$.subscribe(driver => {\n        if (driver) {\n          this.updateDriver(driver);\n        }\n      }));\n    }).catch(err => {\n      console.error('Error connecting to real-time hub', err);\n    });\n  }\n  updateDelivery(updatedDelivery) {\n    const index = this.deliveries.findIndex(d => d.id === updatedDelivery.id);\n    if (index !== -1) {\n      this.deliveries[index] = updatedDelivery;\n    } else {\n      this.deliveries.push(updatedDelivery);\n    }\n    this.calculateDeliveryMetrics();\n  }\n  updateDriver(updatedDriver) {\n    const index = this.drivers.findIndex(d => d.id === updatedDriver.id);\n    if (index !== -1) {\n      this.drivers[index] = updatedDriver;\n    } else {\n      this.drivers.push(updatedDriver);\n    }\n    this.calculateDriverMetrics();\n  }\n  // Helper methods for template\n  getActiveDrivers() {\n    return this.drivers.filter(d => d.status === DriverStatus.Available);\n  }\n  getRecentDeliveries() {\n    return this.deliveries.slice(0, 5);\n  }\n  getDeliveryStatusText(status) {\n    switch (status) {\n      case DeliveryStatus.Delivered:\n        return 'Livré';\n      case DeliveryStatus.InTransit:\n        return 'En cours';\n      case DeliveryStatus.Delayed:\n        return 'Retardé';\n      default:\n        return 'En attente';\n    }\n  }\n  getDeliveryStatusVariant(status) {\n    switch (status) {\n      case DeliveryStatus.Delivered:\n        return 'success';\n      case DeliveryStatus.InTransit:\n        return 'info';\n      case DeliveryStatus.Delayed:\n        return 'danger';\n      default:\n        return 'warning';\n    }\n  }\n  getDeliveryStatusClass(status) {\n    switch (status) {\n      case DeliveryStatus.Delivered:\n        return 'bg-success';\n      case DeliveryStatus.InTransit:\n        return 'bg-info';\n      case DeliveryStatus.Delayed:\n        return 'bg-danger';\n      default:\n        return 'bg-warning';\n    }\n  }\n  getAvgDeliveryTimeFormatted() {\n    return (this.avgDeliveryTime || 0).toFixed(0) + ' min';\n  }\n  getOnTimeRateFormatted() {\n    return (this.onTimeRate || 0).toFixed(0) + '%';\n  }\n  // Header button actions\n  onNewDelivery() {\n    // TODO: Open new delivery modal or navigate to delivery form\n    console.log('New delivery clicked');\n    // For now, just show an alert\n    alert('Fonctionnalité \"Nouvelle livraison\" à venir!');\n  }\n  onRefresh() {\n    this.testApiConnection();\n  }\n  static {\n    this.ɵfac = function DashboardComponent_Factory(t) {\n      return new (t || DashboardComponent)(i0.ɵɵdirectiveInject(i1.HttpClient), i0.ɵɵdirectiveInject(i2.DeliveryService), i0.ɵɵdirectiveInject(i3.DriverService), i0.ɵɵdirectiveInject(i4.AnalyticsService), i0.ɵɵdirectiveInject(i5.RealTimeService), i0.ɵɵdirectiveInject(i6.AuthService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardComponent,\n      selectors: [[\"app-dashboard\"]],\n      decls: 23,\n      vars: 12,\n      consts: [[1, \"dashboard-container\"], [1, \"dashboard-content\"], [3, \"title\", \"subtitle\"], [1, \"d-flex\", \"gap-2\"], [\"class\", \"btn btn-primary\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-outline-secondary\", 3, \"disabled\", \"click\"], [1, \"fa-solid\", \"fa-sync\", \"me-2\"], [1, \"dashboard-body\", \"p-4\"], [1, \"row\", \"mb-4\"], [1, \"col-12\"], [1, \"card\"], [1, \"card-header\"], [1, \"card-title\", \"mb-0\"], [1, \"card-body\"], [\"class\", \"alert alert-success\", 4, \"ngIf\"], [\"class\", \"alert alert-danger\", 4, \"ngIf\"], [\"class\", \"alert alert-info\", 4, \"ngIf\"], [\"class\", \"d-flex justify-content-center align-items-center\", \"style\", \"height: 200px;\", 4, \"ngIf\"], [\"class\", \"alert alert-danger alert-dismissible fade show\", \"role\", \"alert\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"fa-solid\", \"fa-plus\", \"me-2\"], [1, \"alert\", \"alert-success\"], [1, \"alert\", \"alert-danger\"], [1, \"alert\", \"alert-info\"], [1, \"d-flex\", \"justify-content-center\", \"align-items-center\", 2, \"height\", \"200px\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [\"role\", \"alert\", 1, \"alert\", \"alert-danger\", \"alert-dismissible\", \"fade\", \"show\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"btn-close\", 3, \"click\"], [\"class\", \"col-md-3 mb-3\", 4, \"ngIf\"], [1, \"row\"], [\"class\", \"text-center text-muted py-4\", 4, \"ngIf\"], [\"class\", \"table-responsive\", 4, \"ngIf\"], [1, \"col-md-3\", \"mb-3\"], [1, \"card\", \"text-center\"], [1, \"card-title\"], [1, \"text-primary\"], [1, \"text-success\"], [1, \"text-warning\"], [1, \"text-info\"], [1, \"text-center\", \"text-muted\", \"py-4\"], [1, \"table-responsive\"], [1, \"table\", \"table-striped\"], [4, \"ngFor\", \"ngForOf\"], [1, \"badge\", 3, \"ngClass\"]],\n      template: function DashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"app-sidebar\");\n          i0.ɵɵelementStart(2, \"div\", 1)(3, \"app-header\", 2)(4, \"div\", 3);\n          i0.ɵɵtemplate(5, DashboardComponent_button_5_Template, 3, 0, \"button\", 4);\n          i0.ɵɵelementStart(6, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_6_listener() {\n            return ctx.onRefresh();\n          });\n          i0.ɵɵelement(7, \"i\", 6);\n          i0.ɵɵtext(8, \" Actualiser \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(9, \"div\", 7)(10, \"div\", 8)(11, \"div\", 9)(12, \"div\", 10)(13, \"div\", 11)(14, \"h5\", 12);\n          i0.ɵɵtext(15, \"\\u00C9tat de la connexion API\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"div\", 13);\n          i0.ɵɵtemplate(17, DashboardComponent_div_17_Template, 2, 1, \"div\", 14);\n          i0.ɵɵtemplate(18, DashboardComponent_div_18_Template, 2, 0, \"div\", 15);\n          i0.ɵɵtemplate(19, DashboardComponent_div_19_Template, 2, 0, \"div\", 16);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(20, DashboardComponent_div_20_Template, 4, 0, \"div\", 17);\n          i0.ɵɵtemplate(21, DashboardComponent_div_21_Template, 3, 1, \"div\", 18);\n          i0.ɵɵtemplate(22, DashboardComponent_div_22_Template, 19, 10, \"div\", 19);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"title\", ctx.isDriverView ? \"Tableau de bord livreur\" : \"Tableau de bord\")(\"subtitle\", ctx.isDriverView ? \"Vos livraisons et performances\" : \"Vue d'ensemble de vos op\\u00E9rations de livraison\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isDriverView);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"disabled\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"fa-spin\", ctx.loading);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"ngIf\", ctx.apiConnected);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.apiConnected && !ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.apiConnected);\n        }\n      },\n      dependencies: [i7.NgClass, i7.NgForOf, i7.NgIf, i8.SidebarComponent, i9.HeaderComponent, i7.DatePipe],\n      styles: [\".dashboard-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  height: 100vh;\\n  overflow: hidden;\\n}\\n\\n.dashboard-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n}\\n\\n.dashboard-body[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  background-color: var(--gray-50);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvZGFzaGJvYXJkL2Rhc2hib2FyZC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGFBQUE7RUFDQSxhQUFBO0VBQ0EsZ0JBQUE7QUFDRjs7QUFFQTtFQUNFLE9BQUE7RUFDQSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxnQkFBQTtBQUNGOztBQUVBO0VBQ0UsT0FBQTtFQUNBLGdCQUFBO0VBQ0EsZ0NBQUE7QUFDRiIsInNvdXJjZXNDb250ZW50IjpbIi5kYXNoYm9hcmQtY29udGFpbmVyIHtcbiAgZGlzcGxheTogZmxleDtcbiAgaGVpZ2h0OiAxMDB2aDtcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcbn1cblxuLmRhc2hib2FyZC1jb250ZW50IHtcbiAgZmxleDogMTtcbiAgZGlzcGxheTogZmxleDtcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcbn1cblxuLmRhc2hib2FyZC1ib2R5IHtcbiAgZmxleDogMTtcbiAgb3ZlcmZsb3cteTogYXV0bztcbiAgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0tZ3JheS01MCk7XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["environment", "DeliveryStatus", "DriverStatus", "UserRole", "i0", "ɵɵelementStart", "ɵɵlistener", "DashboardComponent_button_5_Template_button_click_0_listener", "ɵɵrestoreView", "_r8", "ctx_r7", "ɵɵnextContext", "ɵɵresetView", "onNewDelivery", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "apiUrl", "DashboardComponent_div_21_Template_button_click_2_listener", "_r10", "ctx_r9", "error", "ctx_r5", "ɵɵtextInterpolate", "ctx_r11", "currentDriver", "todayDeliveries", "ctx_r12", "onTimeRate", "toFixed", "ctx_r13", "rating", "ctx_r14", "status", "ctx_r15", "length", "ctx_r16", "inTransitDeliveries", "ctx_r17", "deliveredDeliveries", "ctx_r18", "activeDrivers", "delivery_r22", "customerName", "address", "ɵɵproperty", "ctx_r21", "getDeliveryStatusClass", "<PERSON><PERSON><PERSON>", "ɵɵpipeBind2", "estimatedDeliveryTime", "ɵɵtemplate", "DashboardComponent_div_22_div_18_tr_15_Template", "ctx_r20", "deliveries", "slice", "DashboardComponent_div_22_div_2_Template", "DashboardComponent_div_22_div_3_Template", "DashboardComponent_div_22_div_4_Template", "DashboardComponent_div_22_div_5_Template", "DashboardComponent_div_22_div_6_Template", "DashboardComponent_div_22_div_7_Template", "DashboardComponent_div_22_div_8_Template", "DashboardComponent_div_22_div_9_Template", "DashboardComponent_div_22_div_17_Template", "DashboardComponent_div_22_div_18_Template", "ctx_r6", "isDriverView", "DashboardComponent", "constructor", "http", "deliveryService", "driverService", "analyticsService", "realTimeService", "authService", "drivers", "performanceMetrics", "currentUser", "apiConnected", "totalDeliveries", "deliveredToday", "delayedDeliveries", "avgDeliveryTime", "chartData", "revenueData", "loading", "subscriptions", "ngOnInit", "getCurrentUser", "role", "Driver", "testApiConnection", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "get", "subscribe", "next", "response", "console", "log", "loadData", "setupRealTimeUpdates", "err", "message", "loadDriverData", "loadAdminData", "push", "getCurrentDriver", "driver", "getCurrentDriverDeliveries", "calculateDeliveryMetrics", "getDeliveries", "getDrivers", "calculateDriverMetrics", "startDate", "Date", "setDate", "getDate", "getPerformanceMetrics", "metrics", "prepareChartData", "today", "setHours", "filter", "d", "deliveryDate", "getTime", "InTransit", "Delivered", "Delayed", "Available", "reduce", "sum", "map", "item", "date", "value", "secondary", "onTimeDeliveries", "revenue", "startConnection", "then", "joinAdminGroup", "deliveryUpdates$", "delivery", "updateDelivery", "driverUpdates$", "updateDriver", "catch", "updatedDelivery", "index", "findIndex", "id", "updatedDriver", "getActiveDrivers", "getRecentDeliveries", "getDeliveryStatusText", "getDeliveryStatusVariant", "getAvgDeliveryTimeFormatted", "getOnTimeRateFormatted", "alert", "onRefresh", "ɵɵdirectiveInject", "i1", "HttpClient", "i2", "DeliveryService", "i3", "DriverService", "i4", "AnalyticsService", "i5", "RealTimeService", "i6", "AuthService", "selectors", "decls", "vars", "consts", "template", "DashboardComponent_Template", "rf", "ctx", "DashboardComponent_button_5_Template", "DashboardComponent_Template_button_click_6_listener", "DashboardComponent_div_17_Template", "DashboardComponent_div_18_Template", "DashboardComponent_div_19_Template", "DashboardComponent_div_20_Template", "DashboardComponent_div_21_Template", "DashboardComponent_div_22_Template", "ɵɵclassProp"], "sources": ["C:\\Users\\<USER>\\delivery-dash-optimiser\\frontend\\src\\app\\pages\\dashboard\\dashboard.component.ts", "C:\\Users\\<USER>\\delivery-dash-optimiser\\frontend\\src\\app\\pages\\dashboard\\dashboard.component.html"], "sourcesContent": ["import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';\nimport { Subscription } from 'rxjs';\nimport { HttpClient } from '@angular/common/http';\nimport { environment } from '../../../environments/environment';\nimport { DeliveryService } from '@core/services/delivery.service';\nimport { DriverService } from '@core/services/driver.service';\nimport { AnalyticsService } from '@core/services/analytics.service';\nimport { RealTimeService } from '@core/services/real-time.service';\nimport { AuthService } from '@core/services/auth.service';\nimport { Delivery, DeliveryStatus } from '@core/models/delivery.model';\nimport { Driver, DriverStatus } from '@core/models/driver.model';\nimport { PerformanceMetric } from '@core/models/analytics.model';\nimport { User, UserRole } from '@core/models/user.model';\n\n@Component({\n  selector: 'app-dashboard',\n  templateUrl: './dashboard.component.html',\n  styleUrls: ['./dashboard.component.scss']\n})\nexport class DashboardComponent implements OnInit, OnDestroy {\n  deliveries: Delivery[] = [];\n  drivers: Driver[] = [];\n  performanceMetrics: PerformanceMetric[] = [];\n\n  // User context\n  currentUser: User | null = null;\n  isDriverView = false;\n  currentDriver: Driver | null = null;\n\n  // API Connection\n  apiConnected = false;\n  apiUrl = environment.apiUrl;\n\n  // Computed properties for template\n  todayDeliveries: Delivery[] = [];\n  inTransitDeliveries: Delivery[] = [];\n  deliveredDeliveries: Delivery[] = [];\n\n  totalDeliveries = 0;\n  activeDrivers = 0;\n  deliveredToday = 0;\n  delayedDeliveries = 0;\n  avgDeliveryTime = 0;\n  onTimeRate = 0;\n\n  chartData: any[] = [];\n  revenueData: any[] = [];\n\n  loading = true;\n  error = '';\n\n  private subscriptions: Subscription[] = [];\n\n  constructor(\n    private http: HttpClient,\n    private deliveryService: DeliveryService,\n    private driverService: DriverService,\n    private analyticsService: AnalyticsService,\n    private realTimeService: RealTimeService,\n    private authService: AuthService\n  ) { }\n\n  ngOnInit(): void {\n    // Check user role to determine view type\n    this.currentUser = this.authService.getCurrentUser();\n    this.isDriverView = this.currentUser?.role === UserRole.Driver;\n\n    this.testApiConnection();\n  }\n\n  ngOnDestroy(): void {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n\n  private testApiConnection(): void {\n    this.loading = true;\n    this.error = '';\n\n    // Test API connection with a simple health check\n    this.http.get(`${environment.apiUrl}/health`).subscribe({\n      next: (response) => {\n        console.log('API connection successful:', response);\n        this.apiConnected = true;\n        this.loadData();\n        this.setupRealTimeUpdates();\n      },\n      error: (err) => {\n        console.error('API connection failed:', err);\n        this.apiConnected = false;\n        this.error = `Impossible de se connecter à l'API: ${err.message}`;\n        this.loading = false;\n\n        // Try to load data anyway in case the health endpoint doesn't exist\n        this.loadData();\n      }\n    });\n  }\n\n  private loadData(): void {\n    this.loading = true;\n\n    if (this.isDriverView) {\n      this.loadDriverData();\n    } else {\n      this.loadAdminData();\n    }\n  }\n\n  private loadDriverData(): void {\n    // Load current driver profile\n    this.subscriptions.push(\n      this.driverService.getCurrentDriver().subscribe({\n        next: (driver) => {\n          this.currentDriver = driver;\n          this.apiConnected = true;\n        },\n        error: (err) => {\n          console.error('Error loading driver profile', err);\n          this.error = 'Erreur lors du chargement du profil livreur';\n        }\n      })\n    );\n\n    // Load driver's deliveries\n    this.subscriptions.push(\n      this.driverService.getCurrentDriverDeliveries().subscribe({\n        next: (deliveries) => {\n          this.deliveries = deliveries;\n          this.calculateDeliveryMetrics();\n          this.loading = false;\n        },\n        error: (err) => {\n          console.error('Error loading driver deliveries', err);\n          this.error = 'Erreur lors du chargement des livraisons';\n          this.loading = false;\n        }\n      })\n    );\n  }\n\n  private loadAdminData(): void {\n    // Load deliveries\n    this.subscriptions.push(\n      this.deliveryService.getDeliveries().subscribe({\n        next: (deliveries) => {\n          this.deliveries = deliveries;\n          this.calculateDeliveryMetrics();\n          this.apiConnected = true;\n        },\n        error: (err) => {\n          console.error('Error loading deliveries', err);\n          this.error = 'Erreur lors du chargement des livraisons';\n          this.loading = false;\n        }\n      })\n    );\n\n    // Load drivers\n    this.subscriptions.push(\n      this.driverService.getDrivers().subscribe({\n        next: (drivers) => {\n          this.drivers = drivers;\n          this.calculateDriverMetrics();\n        },\n        error: (err) => {\n          console.error('Error loading drivers', err);\n          this.error = 'Erreur lors du chargement des livreurs';\n          this.loading = false;\n        }\n      })\n    );\n\n    // Load performance metrics\n    const startDate = new Date();\n    startDate.setDate(startDate.getDate() - 7);\n\n    this.subscriptions.push(\n      this.analyticsService.getPerformanceMetrics(startDate).subscribe({\n        next: (metrics) => {\n          this.performanceMetrics = metrics;\n          this.prepareChartData();\n          this.loading = false;\n        },\n        error: (err) => {\n          console.error('Error loading performance metrics', err);\n          this.error = 'Erreur lors du chargement des métriques de performance';\n          this.loading = false;\n        }\n      })\n    );\n  }\n\n  private calculateDeliveryMetrics(): void {\n    const today = new Date();\n    today.setHours(0, 0, 0, 0);\n\n    this.todayDeliveries = this.deliveries.filter(d => {\n      const deliveryDate = new Date(d.estimatedDeliveryTime);\n      deliveryDate.setHours(0, 0, 0, 0);\n      return deliveryDate.getTime() === today.getTime();\n    });\n\n    this.inTransitDeliveries = this.deliveries.filter(d => d.status === DeliveryStatus.InTransit);\n    this.deliveredDeliveries = this.deliveries.filter(d => d.status === DeliveryStatus.Delivered);\n\n    this.totalDeliveries = this.deliveries.length;\n    this.deliveredToday = this.deliveredDeliveries.length;\n    this.delayedDeliveries = this.deliveries.filter(d => d.status === DeliveryStatus.Delayed).length;\n  }\n\n  private calculateDriverMetrics(): void {\n    this.activeDrivers = this.drivers.filter(d => d.status === DriverStatus.Available).length;\n\n    if (this.drivers.length > 0) {\n      this.avgDeliveryTime = this.drivers.reduce((sum, driver) => sum + driver.avgDeliveryTime, 0) / this.drivers.length;\n      this.onTimeRate = this.drivers.reduce((sum, driver) => sum + driver.onTimeRate, 0) / this.drivers.length;\n    }\n  }\n\n  private prepareChartData(): void {\n    this.chartData = this.performanceMetrics.map(item => ({\n      date: new Date(item.date),\n      value: item.totalDeliveries,\n      secondary: item.onTimeDeliveries\n    }));\n\n    this.revenueData = this.performanceMetrics.map(item => ({\n      date: new Date(item.date),\n      value: item.revenue / 1000 // Convert to thousands\n    }));\n  }\n\n  private setupRealTimeUpdates(): void {\n    // Start SignalR connection\n    this.realTimeService.startConnection()\n      .then(() => {\n        console.log('Connected to real-time hub');\n        this.realTimeService.joinAdminGroup();\n\n        // Subscribe to delivery updates\n        this.subscriptions.push(\n          this.realTimeService.deliveryUpdates$.subscribe(delivery => {\n            if (delivery) {\n              this.updateDelivery(delivery);\n            }\n          })\n        );\n\n        // Subscribe to driver updates\n        this.subscriptions.push(\n          this.realTimeService.driverUpdates$.subscribe(driver => {\n            if (driver) {\n              this.updateDriver(driver);\n            }\n          })\n        );\n      })\n      .catch(err => {\n        console.error('Error connecting to real-time hub', err);\n      });\n  }\n\n  private updateDelivery(updatedDelivery: Delivery): void {\n    const index = this.deliveries.findIndex(d => d.id === updatedDelivery.id);\n\n    if (index !== -1) {\n      this.deliveries[index] = updatedDelivery;\n    } else {\n      this.deliveries.push(updatedDelivery);\n    }\n\n    this.calculateDeliveryMetrics();\n  }\n\n  private updateDriver(updatedDriver: Driver): void {\n    const index = this.drivers.findIndex(d => d.id === updatedDriver.id);\n\n    if (index !== -1) {\n      this.drivers[index] = updatedDriver;\n    } else {\n      this.drivers.push(updatedDriver);\n    }\n\n    this.calculateDriverMetrics();\n  }\n\n  // Helper methods for template\n  getActiveDrivers(): Driver[] {\n    return this.drivers.filter(d => d.status === DriverStatus.Available);\n  }\n\n  getRecentDeliveries(): Delivery[] {\n    return this.deliveries.slice(0, 5);\n  }\n\n  getDeliveryStatusText(status: DeliveryStatus): string {\n    switch (status) {\n      case DeliveryStatus.Delivered: return 'Livré';\n      case DeliveryStatus.InTransit: return 'En cours';\n      case DeliveryStatus.Delayed: return 'Retardé';\n      default: return 'En attente';\n    }\n  }\n\n  getDeliveryStatusVariant(status: DeliveryStatus): string {\n    switch (status) {\n      case DeliveryStatus.Delivered: return 'success';\n      case DeliveryStatus.InTransit: return 'info';\n      case DeliveryStatus.Delayed: return 'danger';\n      default: return 'warning';\n    }\n  }\n\n  getDeliveryStatusClass(status: DeliveryStatus): string {\n    switch (status) {\n      case DeliveryStatus.Delivered: return 'bg-success';\n      case DeliveryStatus.InTransit: return 'bg-info';\n      case DeliveryStatus.Delayed: return 'bg-danger';\n      default: return 'bg-warning';\n    }\n  }\n\n  getAvgDeliveryTimeFormatted(): string {\n    return (this.avgDeliveryTime || 0).toFixed(0) + ' min';\n  }\n\n  getOnTimeRateFormatted(): string {\n    return (this.onTimeRate || 0).toFixed(0) + '%';\n  }\n\n  // Header button actions\n  onNewDelivery(): void {\n    // TODO: Open new delivery modal or navigate to delivery form\n    console.log('New delivery clicked');\n    // For now, just show an alert\n    alert('Fonctionnalité \"Nouvelle livraison\" à venir!');\n  }\n\n  onRefresh(): void {\n    this.testApiConnection();\n  }\n}\n", "<div class=\"dashboard-container\">\n  <app-sidebar></app-sidebar>\n\n  <div class=\"dashboard-content\">\n    <app-header\n      [title]=\"isDriverView ? 'Tableau de bord livreur' : 'Tableau de bord'\"\n      [subtitle]=\"isDriverView ? 'Vos livraisons et performances' : 'Vue d\\'ensemble de vos opérations de livraison'\"\n    >\n      <div class=\"d-flex gap-2\">\n        <button *ngIf=\"!isDriverView\" class=\"btn btn-primary\" (click)=\"onNewDelivery()\">\n          <i class=\"fa-solid fa-plus me-2\"></i>\n          Nouvelle livraison\n        </button>\n        <button class=\"btn btn-outline-secondary\" (click)=\"onRefresh()\" [disabled]=\"loading\">\n          <i class=\"fa-solid fa-sync me-2\" [class.fa-spin]=\"loading\"></i>\n          Actualiser\n        </button>\n      </div>\n    </app-header>\n\n    <div class=\"dashboard-body p-4\">\n\n  <!-- API Connection Status -->\n  <div class=\"row mb-4\">\n    <div class=\"col-12\">\n      <div class=\"card\">\n        <div class=\"card-header\">\n          <h5 class=\"card-title mb-0\">État de la connexion API</h5>\n        </div>\n        <div class=\"card-body\">\n          <div *ngIf=\"apiConnected\" class=\"alert alert-success\">\n            ✅ Connexion API réussie - Backend accessible sur {{ apiUrl }}\n          </div>\n          <div *ngIf=\"!apiConnected && !loading\" class=\"alert alert-danger\">\n            ❌ Connexion API échouée - Vérifiez que le backend est démarré\n          </div>\n          <div *ngIf=\"loading\" class=\"alert alert-info\">\n            🔄 Test de connexion en cours...\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Loading State -->\n  <div *ngIf=\"loading\" class=\"d-flex justify-content-center align-items-center\" style=\"height: 200px;\">\n    <div class=\"spinner-border text-primary\" role=\"status\">\n      <span class=\"visually-hidden\">Chargement...</span>\n    </div>\n  </div>\n\n  <!-- Error State -->\n  <div *ngIf=\"error\" class=\"alert alert-danger alert-dismissible fade show\" role=\"alert\">\n    {{ error }}\n    <button type=\"button\" class=\"btn-close\" (click)=\"error = ''\" aria-label=\"Close\"></button>\n  </div>\n\n  <!-- Dashboard Content -->\n  <div *ngIf=\"!loading && apiConnected\">\n    <!-- Key Metrics Row -->\n    <div class=\"row mb-4\">\n      <!-- Driver View Metrics -->\n      <div *ngIf=\"isDriverView && currentDriver\" class=\"col-md-3 mb-3\">\n        <div class=\"card text-center\">\n          <div class=\"card-body\">\n            <h5 class=\"card-title\">Mes livraisons aujourd'hui</h5>\n            <h2 class=\"text-primary\">{{ currentDriver.todayDeliveries }}</h2>\n          </div>\n        </div>\n      </div>\n      <div *ngIf=\"isDriverView && currentDriver\" class=\"col-md-3 mb-3\">\n        <div class=\"card text-center\">\n          <div class=\"card-body\">\n            <h5 class=\"card-title\">Taux de ponctualité</h5>\n            <h2 class=\"text-success\">{{ currentDriver.onTimeRate.toFixed(1) }}%</h2>\n          </div>\n        </div>\n      </div>\n      <div *ngIf=\"isDriverView && currentDriver\" class=\"col-md-3 mb-3\">\n        <div class=\"card text-center\">\n          <div class=\"card-body\">\n            <h5 class=\"card-title\">Note moyenne</h5>\n            <h2 class=\"text-warning\">{{ currentDriver.rating.toFixed(1) }}/5</h2>\n          </div>\n        </div>\n      </div>\n      <div *ngIf=\"isDriverView && currentDriver\" class=\"col-md-3 mb-3\">\n        <div class=\"card text-center\">\n          <div class=\"card-body\">\n            <h5 class=\"card-title\">Statut</h5>\n            <h2 class=\"text-info\">{{ currentDriver.status }}</h2>\n          </div>\n        </div>\n      </div>\n\n      <!-- Admin View Metrics -->\n      <div *ngIf=\"!isDriverView\" class=\"col-md-3 mb-3\">\n        <div class=\"card text-center\">\n          <div class=\"card-body\">\n            <h5 class=\"card-title\">Livraisons aujourd'hui</h5>\n            <h2 class=\"text-primary\">{{ todayDeliveries.length }}</h2>\n          </div>\n        </div>\n      </div>\n      <div *ngIf=\"!isDriverView\" class=\"col-md-3 mb-3\">\n        <div class=\"card text-center\">\n          <div class=\"card-body\">\n            <h5 class=\"card-title\">En cours</h5>\n            <h2 class=\"text-warning\">{{ inTransitDeliveries.length }}</h2>\n          </div>\n        </div>\n      </div>\n      <div *ngIf=\"!isDriverView\" class=\"col-md-3 mb-3\">\n        <div class=\"card text-center\">\n          <div class=\"card-body\">\n            <h5 class=\"card-title\">Livrées</h5>\n            <h2 class=\"text-success\">{{ deliveredDeliveries.length }}</h2>\n          </div>\n        </div>\n      </div>\n      <div *ngIf=\"!isDriverView\" class=\"col-md-3 mb-3\">\n        <div class=\"card text-center\">\n          <div class=\"card-body\">\n            <h5 class=\"card-title\">Livreurs actifs</h5>\n            <h2 class=\"text-info\">{{ activeDrivers }}</h2>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Recent Deliveries -->\n    <div class=\"row\">\n      <div class=\"col-12\">\n        <div class=\"card\">\n          <div class=\"card-header\">\n            <h5 class=\"card-title mb-0\">Livraisons récentes</h5>\n          </div>\n          <div class=\"card-body\">\n            <div *ngIf=\"deliveries.length === 0\" class=\"text-center text-muted py-4\">\n              Aucune livraison trouvée\n            </div>\n            <div *ngIf=\"deliveries.length > 0\" class=\"table-responsive\">\n              <table class=\"table table-striped\">\n                <thead>\n                  <tr>\n                    <th>Client</th>\n                    <th>Adresse</th>\n                    <th>Statut</th>\n                    <th>Livreur</th>\n                    <th>Heure prévue</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  <tr *ngFor=\"let delivery of deliveries.slice(0, 10)\">\n                    <td>{{ delivery.customerName }}</td>\n                    <td>{{ delivery.address }}</td>\n                    <td>\n                      <span class=\"badge\" [ngClass]=\"getDeliveryStatusClass(delivery.status)\">\n                        {{ delivery.status }}\n                      </span>\n                    </td>\n                    <td>{{ delivery.driverName }}</td>\n                    <td>{{ delivery.estimatedDeliveryTime | date:'HH:mm' }}</td>\n                  </tr>\n                </tbody>\n              </table>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAGA,SAASA,WAAW,QAAQ,mCAAmC;AAM/D,SAAmBC,cAAc,QAAQ,6BAA6B;AACtE,SAAiBC,YAAY,QAAQ,2BAA2B;AAEhE,SAAeC,QAAQ,QAAQ,yBAAyB;;;;;;;;;;;;;;ICHhDC,EAAA,CAAAC,cAAA,iBAAgF;IAA1BD,EAAA,CAAAE,UAAA,mBAAAC,6DAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAF,MAAA,CAAAG,aAAA,EAAe;IAAA,EAAC;IAC7ET,EAAA,CAAAU,SAAA,YAAqC;IACrCV,EAAA,CAAAW,MAAA,2BACF;IAAAX,EAAA,CAAAY,YAAA,EAAS;;;;;IAkBPZ,EAAA,CAAAC,cAAA,cAAsD;IACpDD,EAAA,CAAAW,MAAA,GACF;IAAAX,EAAA,CAAAY,YAAA,EAAM;;;;IADJZ,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,iEAAAC,MAAA,CAAAC,MAAA,MACF;;;;;IACAhB,EAAA,CAAAC,cAAA,cAAkE;IAChED,EAAA,CAAAW,MAAA,oGACF;IAAAX,EAAA,CAAAY,YAAA,EAAM;;;;;IACNZ,EAAA,CAAAC,cAAA,cAA8C;IAC5CD,EAAA,CAAAW,MAAA,mDACF;IAAAX,EAAA,CAAAY,YAAA,EAAM;;;;;IAOdZ,EAAA,CAAAC,cAAA,cAAqG;IAEnED,EAAA,CAAAW,MAAA,oBAAa;IAAAX,EAAA,CAAAY,YAAA,EAAO;;;;;;IAKtDZ,EAAA,CAAAC,cAAA,cAAuF;IACrFD,EAAA,CAAAW,MAAA,GACA;IAAAX,EAAA,CAAAC,cAAA,iBAAgF;IAAxCD,EAAA,CAAAE,UAAA,mBAAAe,2DAAA;MAAAjB,EAAA,CAAAI,aAAA,CAAAc,IAAA;MAAA,MAAAC,MAAA,GAAAnB,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAAW,MAAA,CAAAC,KAAA,GAAiB,EAAE;IAAA,EAAC;IAAoBpB,EAAA,CAAAY,YAAA,EAAS;;;;IADzFZ,EAAA,CAAAa,SAAA,GACA;IADAb,EAAA,CAAAc,kBAAA,MAAAO,MAAA,CAAAD,KAAA,MACA;;;;;IAQEpB,EAAA,CAAAC,cAAA,cAAiE;IAGpCD,EAAA,CAAAW,MAAA,iCAA0B;IAAAX,EAAA,CAAAY,YAAA,EAAK;IACtDZ,EAAA,CAAAC,cAAA,aAAyB;IAAAD,EAAA,CAAAW,MAAA,GAAmC;IAAAX,EAAA,CAAAY,YAAA,EAAK;;;;IAAxCZ,EAAA,CAAAa,SAAA,GAAmC;IAAnCb,EAAA,CAAAsB,iBAAA,CAAAC,OAAA,CAAAC,aAAA,CAAAC,eAAA,CAAmC;;;;;IAIlEzB,EAAA,CAAAC,cAAA,cAAiE;IAGpCD,EAAA,CAAAW,MAAA,+BAAmB;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAC/CZ,EAAA,CAAAC,cAAA,aAAyB;IAAAD,EAAA,CAAAW,MAAA,GAA0C;IAAAX,EAAA,CAAAY,YAAA,EAAK;;;;IAA/CZ,EAAA,CAAAa,SAAA,GAA0C;IAA1Cb,EAAA,CAAAc,kBAAA,KAAAY,OAAA,CAAAF,aAAA,CAAAG,UAAA,CAAAC,OAAA,SAA0C;;;;;IAIzE5B,EAAA,CAAAC,cAAA,cAAiE;IAGpCD,EAAA,CAAAW,MAAA,mBAAY;IAAAX,EAAA,CAAAY,YAAA,EAAK;IACxCZ,EAAA,CAAAC,cAAA,aAAyB;IAAAD,EAAA,CAAAW,MAAA,GAAuC;IAAAX,EAAA,CAAAY,YAAA,EAAK;;;;IAA5CZ,EAAA,CAAAa,SAAA,GAAuC;IAAvCb,EAAA,CAAAc,kBAAA,KAAAe,OAAA,CAAAL,aAAA,CAAAM,MAAA,CAAAF,OAAA,UAAuC;;;;;IAItE5B,EAAA,CAAAC,cAAA,cAAiE;IAGpCD,EAAA,CAAAW,MAAA,aAAM;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAClCZ,EAAA,CAAAC,cAAA,aAAsB;IAAAD,EAAA,CAAAW,MAAA,GAA0B;IAAAX,EAAA,CAAAY,YAAA,EAAK;;;;IAA/BZ,EAAA,CAAAa,SAAA,GAA0B;IAA1Bb,EAAA,CAAAsB,iBAAA,CAAAS,OAAA,CAAAP,aAAA,CAAAQ,MAAA,CAA0B;;;;;IAMtDhC,EAAA,CAAAC,cAAA,cAAiD;IAGpBD,EAAA,CAAAW,MAAA,6BAAsB;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAClDZ,EAAA,CAAAC,cAAA,aAAyB;IAAAD,EAAA,CAAAW,MAAA,GAA4B;IAAAX,EAAA,CAAAY,YAAA,EAAK;;;;IAAjCZ,EAAA,CAAAa,SAAA,GAA4B;IAA5Bb,EAAA,CAAAsB,iBAAA,CAAAW,OAAA,CAAAR,eAAA,CAAAS,MAAA,CAA4B;;;;;IAI3DlC,EAAA,CAAAC,cAAA,cAAiD;IAGpBD,EAAA,CAAAW,MAAA,eAAQ;IAAAX,EAAA,CAAAY,YAAA,EAAK;IACpCZ,EAAA,CAAAC,cAAA,aAAyB;IAAAD,EAAA,CAAAW,MAAA,GAAgC;IAAAX,EAAA,CAAAY,YAAA,EAAK;;;;IAArCZ,EAAA,CAAAa,SAAA,GAAgC;IAAhCb,EAAA,CAAAsB,iBAAA,CAAAa,OAAA,CAAAC,mBAAA,CAAAF,MAAA,CAAgC;;;;;IAI/DlC,EAAA,CAAAC,cAAA,cAAiD;IAGpBD,EAAA,CAAAW,MAAA,mBAAO;IAAAX,EAAA,CAAAY,YAAA,EAAK;IACnCZ,EAAA,CAAAC,cAAA,aAAyB;IAAAD,EAAA,CAAAW,MAAA,GAAgC;IAAAX,EAAA,CAAAY,YAAA,EAAK;;;;IAArCZ,EAAA,CAAAa,SAAA,GAAgC;IAAhCb,EAAA,CAAAsB,iBAAA,CAAAe,OAAA,CAAAC,mBAAA,CAAAJ,MAAA,CAAgC;;;;;IAI/DlC,EAAA,CAAAC,cAAA,cAAiD;IAGpBD,EAAA,CAAAW,MAAA,sBAAe;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAC3CZ,EAAA,CAAAC,cAAA,aAAsB;IAAAD,EAAA,CAAAW,MAAA,GAAmB;IAAAX,EAAA,CAAAY,YAAA,EAAK;;;;IAAxBZ,EAAA,CAAAa,SAAA,GAAmB;IAAnBb,EAAA,CAAAsB,iBAAA,CAAAiB,OAAA,CAAAC,aAAA,CAAmB;;;;;IAczCxC,EAAA,CAAAC,cAAA,cAAyE;IACvED,EAAA,CAAAW,MAAA,sCACF;IAAAX,EAAA,CAAAY,YAAA,EAAM;;;;;IAaAZ,EAAA,CAAAC,cAAA,SAAqD;IAC/CD,EAAA,CAAAW,MAAA,GAA2B;IAAAX,EAAA,CAAAY,YAAA,EAAK;IACpCZ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAW,MAAA,GAAsB;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAC/BZ,EAAA,CAAAC,cAAA,SAAI;IAEAD,EAAA,CAAAW,MAAA,GACF;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAETZ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAW,MAAA,GAAyB;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAClCZ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAW,MAAA,IAAmD;;IAAAX,EAAA,CAAAY,YAAA,EAAK;;;;;IARxDZ,EAAA,CAAAa,SAAA,GAA2B;IAA3Bb,EAAA,CAAAsB,iBAAA,CAAAmB,YAAA,CAAAC,YAAA,CAA2B;IAC3B1C,EAAA,CAAAa,SAAA,GAAsB;IAAtBb,EAAA,CAAAsB,iBAAA,CAAAmB,YAAA,CAAAE,OAAA,CAAsB;IAEJ3C,EAAA,CAAAa,SAAA,GAAmD;IAAnDb,EAAA,CAAA4C,UAAA,YAAAC,OAAA,CAAAC,sBAAA,CAAAL,YAAA,CAAAT,MAAA,EAAmD;IACrEhC,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAc,kBAAA,MAAA2B,YAAA,CAAAT,MAAA,MACF;IAEEhC,EAAA,CAAAa,SAAA,GAAyB;IAAzBb,EAAA,CAAAsB,iBAAA,CAAAmB,YAAA,CAAAM,UAAA,CAAyB;IACzB/C,EAAA,CAAAa,SAAA,GAAmD;IAAnDb,EAAA,CAAAsB,iBAAA,CAAAtB,EAAA,CAAAgD,WAAA,QAAAP,YAAA,CAAAQ,qBAAA,WAAmD;;;;;IArB/DjD,EAAA,CAAAC,cAAA,cAA4D;IAIhDD,EAAA,CAAAW,MAAA,aAAM;IAAAX,EAAA,CAAAY,YAAA,EAAK;IACfZ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAW,MAAA,cAAO;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAChBZ,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAW,MAAA,aAAM;IAAAX,EAAA,CAAAY,YAAA,EAAK;IACfZ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAW,MAAA,eAAO;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAChBZ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAW,MAAA,yBAAY;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAGzBZ,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAAkD,UAAA,KAAAC,+CAAA,kBAUK;IACPnD,EAAA,CAAAY,YAAA,EAAQ;;;;IAXmBZ,EAAA,CAAAa,SAAA,IAA0B;IAA1Bb,EAAA,CAAA4C,UAAA,YAAAQ,OAAA,CAAAC,UAAA,CAAAC,KAAA,QAA0B;;;;;IA/FnEtD,EAAA,CAAAC,cAAA,UAAsC;IAIlCD,EAAA,CAAAkD,UAAA,IAAAK,wCAAA,kBAOM;IACNvD,EAAA,CAAAkD,UAAA,IAAAM,wCAAA,kBAOM;IACNxD,EAAA,CAAAkD,UAAA,IAAAO,wCAAA,kBAOM;IACNzD,EAAA,CAAAkD,UAAA,IAAAQ,wCAAA,kBAOM;IAGN1D,EAAA,CAAAkD,UAAA,IAAAS,wCAAA,kBAOM;IACN3D,EAAA,CAAAkD,UAAA,IAAAU,wCAAA,kBAOM;IACN5D,EAAA,CAAAkD,UAAA,IAAAW,wCAAA,kBAOM;IACN7D,EAAA,CAAAkD,UAAA,IAAAY,wCAAA,kBAOM;IACR9D,EAAA,CAAAY,YAAA,EAAM;IAGNZ,EAAA,CAAAC,cAAA,eAAiB;IAImBD,EAAA,CAAAW,MAAA,gCAAmB;IAAAX,EAAA,CAAAY,YAAA,EAAK;IAEtDZ,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAkD,UAAA,KAAAa,yCAAA,kBAEM;IACN/D,EAAA,CAAAkD,UAAA,KAAAc,yCAAA,mBAyBM;IACRhE,EAAA,CAAAY,YAAA,EAAM;;;;IAzGJZ,EAAA,CAAAa,SAAA,GAAmC;IAAnCb,EAAA,CAAA4C,UAAA,SAAAqB,MAAA,CAAAC,YAAA,IAAAD,MAAA,CAAAzC,aAAA,CAAmC;IAQnCxB,EAAA,CAAAa,SAAA,GAAmC;IAAnCb,EAAA,CAAA4C,UAAA,SAAAqB,MAAA,CAAAC,YAAA,IAAAD,MAAA,CAAAzC,aAAA,CAAmC;IAQnCxB,EAAA,CAAAa,SAAA,GAAmC;IAAnCb,EAAA,CAAA4C,UAAA,SAAAqB,MAAA,CAAAC,YAAA,IAAAD,MAAA,CAAAzC,aAAA,CAAmC;IAQnCxB,EAAA,CAAAa,SAAA,GAAmC;IAAnCb,EAAA,CAAA4C,UAAA,SAAAqB,MAAA,CAAAC,YAAA,IAAAD,MAAA,CAAAzC,aAAA,CAAmC;IAUnCxB,EAAA,CAAAa,SAAA,GAAmB;IAAnBb,EAAA,CAAA4C,UAAA,UAAAqB,MAAA,CAAAC,YAAA,CAAmB;IAQnBlE,EAAA,CAAAa,SAAA,GAAmB;IAAnBb,EAAA,CAAA4C,UAAA,UAAAqB,MAAA,CAAAC,YAAA,CAAmB;IAQnBlE,EAAA,CAAAa,SAAA,GAAmB;IAAnBb,EAAA,CAAA4C,UAAA,UAAAqB,MAAA,CAAAC,YAAA,CAAmB;IAQnBlE,EAAA,CAAAa,SAAA,GAAmB;IAAnBb,EAAA,CAAA4C,UAAA,UAAAqB,MAAA,CAAAC,YAAA,CAAmB;IAkBblE,EAAA,CAAAa,SAAA,GAA6B;IAA7Bb,EAAA,CAAA4C,UAAA,SAAAqB,MAAA,CAAAZ,UAAA,CAAAnB,MAAA,OAA6B;IAG7BlC,EAAA,CAAAa,SAAA,GAA2B;IAA3Bb,EAAA,CAAA4C,UAAA,SAAAqB,MAAA,CAAAZ,UAAA,CAAAnB,MAAA,KAA2B;;;AD1H7C,OAAM,MAAOiC,kBAAkB;EAkC7BC,YACUC,IAAgB,EAChBC,eAAgC,EAChCC,aAA4B,EAC5BC,gBAAkC,EAClCC,eAAgC,EAChCC,WAAwB;IALxB,KAAAL,IAAI,GAAJA,IAAI;IACJ,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,WAAW,GAAXA,WAAW;IAvCrB,KAAArB,UAAU,GAAe,EAAE;IAC3B,KAAAsB,OAAO,GAAa,EAAE;IACtB,KAAAC,kBAAkB,GAAwB,EAAE;IAE5C;IACA,KAAAC,WAAW,GAAgB,IAAI;IAC/B,KAAAX,YAAY,GAAG,KAAK;IACpB,KAAA1C,aAAa,GAAkB,IAAI;IAEnC;IACA,KAAAsD,YAAY,GAAG,KAAK;IACpB,KAAA9D,MAAM,GAAGpB,WAAW,CAACoB,MAAM;IAE3B;IACA,KAAAS,eAAe,GAAe,EAAE;IAChC,KAAAW,mBAAmB,GAAe,EAAE;IACpC,KAAAE,mBAAmB,GAAe,EAAE;IAEpC,KAAAyC,eAAe,GAAG,CAAC;IACnB,KAAAvC,aAAa,GAAG,CAAC;IACjB,KAAAwC,cAAc,GAAG,CAAC;IAClB,KAAAC,iBAAiB,GAAG,CAAC;IACrB,KAAAC,eAAe,GAAG,CAAC;IACnB,KAAAvD,UAAU,GAAG,CAAC;IAEd,KAAAwD,SAAS,GAAU,EAAE;IACrB,KAAAC,WAAW,GAAU,EAAE;IAEvB,KAAAC,OAAO,GAAG,IAAI;IACd,KAAAjE,KAAK,GAAG,EAAE;IAEF,KAAAkE,aAAa,GAAmB,EAAE;EAStC;EAEJC,QAAQA,CAAA;IACN;IACA,IAAI,CAACV,WAAW,GAAG,IAAI,CAACH,WAAW,CAACc,cAAc,EAAE;IACpD,IAAI,CAACtB,YAAY,GAAG,IAAI,CAACW,WAAW,EAAEY,IAAI,KAAK1F,QAAQ,CAAC2F,MAAM;IAE9D,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACN,aAAa,CAACO,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;EACtD;EAEQJ,iBAAiBA,CAAA;IACvB,IAAI,CAACN,OAAO,GAAG,IAAI;IACnB,IAAI,CAACjE,KAAK,GAAG,EAAE;IAEf;IACA,IAAI,CAACiD,IAAI,CAAC2B,GAAG,CAAC,GAAGpG,WAAW,CAACoB,MAAM,SAAS,CAAC,CAACiF,SAAS,CAAC;MACtDC,IAAI,EAAGC,QAAQ,IAAI;QACjBC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEF,QAAQ,CAAC;QACnD,IAAI,CAACrB,YAAY,GAAG,IAAI;QACxB,IAAI,CAACwB,QAAQ,EAAE;QACf,IAAI,CAACC,oBAAoB,EAAE;MAC7B,CAAC;MACDnF,KAAK,EAAGoF,GAAG,IAAI;QACbJ,OAAO,CAAChF,KAAK,CAAC,wBAAwB,EAAEoF,GAAG,CAAC;QAC5C,IAAI,CAAC1B,YAAY,GAAG,KAAK;QACzB,IAAI,CAAC1D,KAAK,GAAG,uCAAuCoF,GAAG,CAACC,OAAO,EAAE;QACjE,IAAI,CAACpB,OAAO,GAAG,KAAK;QAEpB;QACA,IAAI,CAACiB,QAAQ,EAAE;MACjB;KACD,CAAC;EACJ;EAEQA,QAAQA,CAAA;IACd,IAAI,CAACjB,OAAO,GAAG,IAAI;IAEnB,IAAI,IAAI,CAACnB,YAAY,EAAE;MACrB,IAAI,CAACwC,cAAc,EAAE;KACtB,MAAM;MACL,IAAI,CAACC,aAAa,EAAE;;EAExB;EAEQD,cAAcA,CAAA;IACpB;IACA,IAAI,CAACpB,aAAa,CAACsB,IAAI,CACrB,IAAI,CAACrC,aAAa,CAACsC,gBAAgB,EAAE,CAACZ,SAAS,CAAC;MAC9CC,IAAI,EAAGY,MAAM,IAAI;QACf,IAAI,CAACtF,aAAa,GAAGsF,MAAM;QAC3B,IAAI,CAAChC,YAAY,GAAG,IAAI;MAC1B,CAAC;MACD1D,KAAK,EAAGoF,GAAG,IAAI;QACbJ,OAAO,CAAChF,KAAK,CAAC,8BAA8B,EAAEoF,GAAG,CAAC;QAClD,IAAI,CAACpF,KAAK,GAAG,6CAA6C;MAC5D;KACD,CAAC,CACH;IAED;IACA,IAAI,CAACkE,aAAa,CAACsB,IAAI,CACrB,IAAI,CAACrC,aAAa,CAACwC,0BAA0B,EAAE,CAACd,SAAS,CAAC;MACxDC,IAAI,EAAG7C,UAAU,IAAI;QACnB,IAAI,CAACA,UAAU,GAAGA,UAAU;QAC5B,IAAI,CAAC2D,wBAAwB,EAAE;QAC/B,IAAI,CAAC3B,OAAO,GAAG,KAAK;MACtB,CAAC;MACDjE,KAAK,EAAGoF,GAAG,IAAI;QACbJ,OAAO,CAAChF,KAAK,CAAC,iCAAiC,EAAEoF,GAAG,CAAC;QACrD,IAAI,CAACpF,KAAK,GAAG,0CAA0C;QACvD,IAAI,CAACiE,OAAO,GAAG,KAAK;MACtB;KACD,CAAC,CACH;EACH;EAEQsB,aAAaA,CAAA;IACnB;IACA,IAAI,CAACrB,aAAa,CAACsB,IAAI,CACrB,IAAI,CAACtC,eAAe,CAAC2C,aAAa,EAAE,CAAChB,SAAS,CAAC;MAC7CC,IAAI,EAAG7C,UAAU,IAAI;QACnB,IAAI,CAACA,UAAU,GAAGA,UAAU;QAC5B,IAAI,CAAC2D,wBAAwB,EAAE;QAC/B,IAAI,CAAClC,YAAY,GAAG,IAAI;MAC1B,CAAC;MACD1D,KAAK,EAAGoF,GAAG,IAAI;QACbJ,OAAO,CAAChF,KAAK,CAAC,0BAA0B,EAAEoF,GAAG,CAAC;QAC9C,IAAI,CAACpF,KAAK,GAAG,0CAA0C;QACvD,IAAI,CAACiE,OAAO,GAAG,KAAK;MACtB;KACD,CAAC,CACH;IAED;IACA,IAAI,CAACC,aAAa,CAACsB,IAAI,CACrB,IAAI,CAACrC,aAAa,CAAC2C,UAAU,EAAE,CAACjB,SAAS,CAAC;MACxCC,IAAI,EAAGvB,OAAO,IAAI;QAChB,IAAI,CAACA,OAAO,GAAGA,OAAO;QACtB,IAAI,CAACwC,sBAAsB,EAAE;MAC/B,CAAC;MACD/F,KAAK,EAAGoF,GAAG,IAAI;QACbJ,OAAO,CAAChF,KAAK,CAAC,uBAAuB,EAAEoF,GAAG,CAAC;QAC3C,IAAI,CAACpF,KAAK,GAAG,wCAAwC;QACrD,IAAI,CAACiE,OAAO,GAAG,KAAK;MACtB;KACD,CAAC,CACH;IAED;IACA,MAAM+B,SAAS,GAAG,IAAIC,IAAI,EAAE;IAC5BD,SAAS,CAACE,OAAO,CAACF,SAAS,CAACG,OAAO,EAAE,GAAG,CAAC,CAAC;IAE1C,IAAI,CAACjC,aAAa,CAACsB,IAAI,CACrB,IAAI,CAACpC,gBAAgB,CAACgD,qBAAqB,CAACJ,SAAS,CAAC,CAACnB,SAAS,CAAC;MAC/DC,IAAI,EAAGuB,OAAO,IAAI;QAChB,IAAI,CAAC7C,kBAAkB,GAAG6C,OAAO;QACjC,IAAI,CAACC,gBAAgB,EAAE;QACvB,IAAI,CAACrC,OAAO,GAAG,KAAK;MACtB,CAAC;MACDjE,KAAK,EAAGoF,GAAG,IAAI;QACbJ,OAAO,CAAChF,KAAK,CAAC,mCAAmC,EAAEoF,GAAG,CAAC;QACvD,IAAI,CAACpF,KAAK,GAAG,wDAAwD;QACrE,IAAI,CAACiE,OAAO,GAAG,KAAK;MACtB;KACD,CAAC,CACH;EACH;EAEQ2B,wBAAwBA,CAAA;IAC9B,MAAMW,KAAK,GAAG,IAAIN,IAAI,EAAE;IACxBM,KAAK,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAE1B,IAAI,CAACnG,eAAe,GAAG,IAAI,CAAC4B,UAAU,CAACwE,MAAM,CAACC,CAAC,IAAG;MAChD,MAAMC,YAAY,GAAG,IAAIV,IAAI,CAACS,CAAC,CAAC7E,qBAAqB,CAAC;MACtD8E,YAAY,CAACH,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACjC,OAAOG,YAAY,CAACC,OAAO,EAAE,KAAKL,KAAK,CAACK,OAAO,EAAE;IACnD,CAAC,CAAC;IAEF,IAAI,CAAC5F,mBAAmB,GAAG,IAAI,CAACiB,UAAU,CAACwE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC9F,MAAM,KAAKnC,cAAc,CAACoI,SAAS,CAAC;IAC7F,IAAI,CAAC3F,mBAAmB,GAAG,IAAI,CAACe,UAAU,CAACwE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC9F,MAAM,KAAKnC,cAAc,CAACqI,SAAS,CAAC;IAE7F,IAAI,CAACnD,eAAe,GAAG,IAAI,CAAC1B,UAAU,CAACnB,MAAM;IAC7C,IAAI,CAAC8C,cAAc,GAAG,IAAI,CAAC1C,mBAAmB,CAACJ,MAAM;IACrD,IAAI,CAAC+C,iBAAiB,GAAG,IAAI,CAAC5B,UAAU,CAACwE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC9F,MAAM,KAAKnC,cAAc,CAACsI,OAAO,CAAC,CAACjG,MAAM;EAClG;EAEQiF,sBAAsBA,CAAA;IAC5B,IAAI,CAAC3E,aAAa,GAAG,IAAI,CAACmC,OAAO,CAACkD,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC9F,MAAM,KAAKlC,YAAY,CAACsI,SAAS,CAAC,CAAClG,MAAM;IAEzF,IAAI,IAAI,CAACyC,OAAO,CAACzC,MAAM,GAAG,CAAC,EAAE;MAC3B,IAAI,CAACgD,eAAe,GAAG,IAAI,CAACP,OAAO,CAAC0D,MAAM,CAAC,CAACC,GAAG,EAAExB,MAAM,KAAKwB,GAAG,GAAGxB,MAAM,CAAC5B,eAAe,EAAE,CAAC,CAAC,GAAG,IAAI,CAACP,OAAO,CAACzC,MAAM;MAClH,IAAI,CAACP,UAAU,GAAG,IAAI,CAACgD,OAAO,CAAC0D,MAAM,CAAC,CAACC,GAAG,EAAExB,MAAM,KAAKwB,GAAG,GAAGxB,MAAM,CAACnF,UAAU,EAAE,CAAC,CAAC,GAAG,IAAI,CAACgD,OAAO,CAACzC,MAAM;;EAE5G;EAEQwF,gBAAgBA,CAAA;IACtB,IAAI,CAACvC,SAAS,GAAG,IAAI,CAACP,kBAAkB,CAAC2D,GAAG,CAACC,IAAI,KAAK;MACpDC,IAAI,EAAE,IAAIpB,IAAI,CAACmB,IAAI,CAACC,IAAI,CAAC;MACzBC,KAAK,EAAEF,IAAI,CAACzD,eAAe;MAC3B4D,SAAS,EAAEH,IAAI,CAACI;KACjB,CAAC,CAAC;IAEH,IAAI,CAACxD,WAAW,GAAG,IAAI,CAACR,kBAAkB,CAAC2D,GAAG,CAACC,IAAI,KAAK;MACtDC,IAAI,EAAE,IAAIpB,IAAI,CAACmB,IAAI,CAACC,IAAI,CAAC;MACzBC,KAAK,EAAEF,IAAI,CAACK,OAAO,GAAG,IAAI,CAAC;KAC5B,CAAC,CAAC;EACL;;EAEQtC,oBAAoBA,CAAA;IAC1B;IACA,IAAI,CAAC9B,eAAe,CAACqE,eAAe,EAAE,CACnCC,IAAI,CAAC,MAAK;MACT3C,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;MACzC,IAAI,CAAC5B,eAAe,CAACuE,cAAc,EAAE;MAErC;MACA,IAAI,CAAC1D,aAAa,CAACsB,IAAI,CACrB,IAAI,CAACnC,eAAe,CAACwE,gBAAgB,CAAChD,SAAS,CAACiD,QAAQ,IAAG;QACzD,IAAIA,QAAQ,EAAE;UACZ,IAAI,CAACC,cAAc,CAACD,QAAQ,CAAC;;MAEjC,CAAC,CAAC,CACH;MAED;MACA,IAAI,CAAC5D,aAAa,CAACsB,IAAI,CACrB,IAAI,CAACnC,eAAe,CAAC2E,cAAc,CAACnD,SAAS,CAACa,MAAM,IAAG;QACrD,IAAIA,MAAM,EAAE;UACV,IAAI,CAACuC,YAAY,CAACvC,MAAM,CAAC;;MAE7B,CAAC,CAAC,CACH;IACH,CAAC,CAAC,CACDwC,KAAK,CAAC9C,GAAG,IAAG;MACXJ,OAAO,CAAChF,KAAK,CAAC,mCAAmC,EAAEoF,GAAG,CAAC;IACzD,CAAC,CAAC;EACN;EAEQ2C,cAAcA,CAACI,eAAyB;IAC9C,MAAMC,KAAK,GAAG,IAAI,CAACnG,UAAU,CAACoG,SAAS,CAAC3B,CAAC,IAAIA,CAAC,CAAC4B,EAAE,KAAKH,eAAe,CAACG,EAAE,CAAC;IAEzE,IAAIF,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,IAAI,CAACnG,UAAU,CAACmG,KAAK,CAAC,GAAGD,eAAe;KACzC,MAAM;MACL,IAAI,CAAClG,UAAU,CAACuD,IAAI,CAAC2C,eAAe,CAAC;;IAGvC,IAAI,CAACvC,wBAAwB,EAAE;EACjC;EAEQqC,YAAYA,CAACM,aAAqB;IACxC,MAAMH,KAAK,GAAG,IAAI,CAAC7E,OAAO,CAAC8E,SAAS,CAAC3B,CAAC,IAAIA,CAAC,CAAC4B,EAAE,KAAKC,aAAa,CAACD,EAAE,CAAC;IAEpE,IAAIF,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,IAAI,CAAC7E,OAAO,CAAC6E,KAAK,CAAC,GAAGG,aAAa;KACpC,MAAM;MACL,IAAI,CAAChF,OAAO,CAACiC,IAAI,CAAC+C,aAAa,CAAC;;IAGlC,IAAI,CAACxC,sBAAsB,EAAE;EAC/B;EAEA;EACAyC,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACjF,OAAO,CAACkD,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC9F,MAAM,KAAKlC,YAAY,CAACsI,SAAS,CAAC;EACtE;EAEAyB,mBAAmBA,CAAA;IACjB,OAAO,IAAI,CAACxG,UAAU,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EACpC;EAEAwG,qBAAqBA,CAAC9H,MAAsB;IAC1C,QAAQA,MAAM;MACZ,KAAKnC,cAAc,CAACqI,SAAS;QAAE,OAAO,OAAO;MAC7C,KAAKrI,cAAc,CAACoI,SAAS;QAAE,OAAO,UAAU;MAChD,KAAKpI,cAAc,CAACsI,OAAO;QAAE,OAAO,SAAS;MAC7C;QAAS,OAAO,YAAY;;EAEhC;EAEA4B,wBAAwBA,CAAC/H,MAAsB;IAC7C,QAAQA,MAAM;MACZ,KAAKnC,cAAc,CAACqI,SAAS;QAAE,OAAO,SAAS;MAC/C,KAAKrI,cAAc,CAACoI,SAAS;QAAE,OAAO,MAAM;MAC5C,KAAKpI,cAAc,CAACsI,OAAO;QAAE,OAAO,QAAQ;MAC5C;QAAS,OAAO,SAAS;;EAE7B;EAEArF,sBAAsBA,CAACd,MAAsB;IAC3C,QAAQA,MAAM;MACZ,KAAKnC,cAAc,CAACqI,SAAS;QAAE,OAAO,YAAY;MAClD,KAAKrI,cAAc,CAACoI,SAAS;QAAE,OAAO,SAAS;MAC/C,KAAKpI,cAAc,CAACsI,OAAO;QAAE,OAAO,WAAW;MAC/C;QAAS,OAAO,YAAY;;EAEhC;EAEA6B,2BAA2BA,CAAA;IACzB,OAAO,CAAC,IAAI,CAAC9E,eAAe,IAAI,CAAC,EAAEtD,OAAO,CAAC,CAAC,CAAC,GAAG,MAAM;EACxD;EAEAqI,sBAAsBA,CAAA;IACpB,OAAO,CAAC,IAAI,CAACtI,UAAU,IAAI,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;EAChD;EAEA;EACAnB,aAAaA,CAAA;IACX;IACA2F,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;IACnC;IACA6D,KAAK,CAAC,8CAA8C,CAAC;EACvD;EAEAC,SAASA,CAAA;IACP,IAAI,CAACxE,iBAAiB,EAAE;EAC1B;;;uBAjUWxB,kBAAkB,EAAAnE,EAAA,CAAAoK,iBAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAtK,EAAA,CAAAoK,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAxK,EAAA,CAAAoK,iBAAA,CAAAK,EAAA,CAAAC,aAAA,GAAA1K,EAAA,CAAAoK,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAA5K,EAAA,CAAAoK,iBAAA,CAAAS,EAAA,CAAAC,eAAA,GAAA9K,EAAA,CAAAoK,iBAAA,CAAAW,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAlB7G,kBAAkB;MAAA8G,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCnB/BvL,EAAA,CAAAC,cAAA,aAAiC;UAC/BD,EAAA,CAAAU,SAAA,kBAA2B;UAE3BV,EAAA,CAAAC,cAAA,aAA+B;UAMzBD,EAAA,CAAAkD,UAAA,IAAAuI,oCAAA,oBAGS;UACTzL,EAAA,CAAAC,cAAA,gBAAqF;UAA3CD,EAAA,CAAAE,UAAA,mBAAAwL,oDAAA;YAAA,OAASF,GAAA,CAAArB,SAAA,EAAW;UAAA,EAAC;UAC7DnK,EAAA,CAAAU,SAAA,WAA+D;UAC/DV,EAAA,CAAAW,MAAA,mBACF;UAAAX,EAAA,CAAAY,YAAA,EAAS;UAIbZ,EAAA,CAAAC,cAAA,aAAgC;UAOED,EAAA,CAAAW,MAAA,qCAAwB;UAAAX,EAAA,CAAAY,YAAA,EAAK;UAE3DZ,EAAA,CAAAC,cAAA,eAAuB;UACrBD,EAAA,CAAAkD,UAAA,KAAAyI,kCAAA,kBAEM;UACN3L,EAAA,CAAAkD,UAAA,KAAA0I,kCAAA,kBAEM;UACN5L,EAAA,CAAAkD,UAAA,KAAA2I,kCAAA,kBAEM;UACR7L,EAAA,CAAAY,YAAA,EAAM;UAMZZ,EAAA,CAAAkD,UAAA,KAAA4I,kCAAA,kBAIM;UAGN9L,EAAA,CAAAkD,UAAA,KAAA6I,kCAAA,kBAGM;UAGN/L,EAAA,CAAAkD,UAAA,KAAA8I,kCAAA,oBAiHQ;UACRhM,EAAA,CAAAY,YAAA,EAAM;;;UAvKFZ,EAAA,CAAAa,SAAA,GAAsE;UAAtEb,EAAA,CAAA4C,UAAA,UAAA4I,GAAA,CAAAtH,YAAA,iDAAsE,aAAAsH,GAAA,CAAAtH,YAAA;UAI3DlE,EAAA,CAAAa,SAAA,GAAmB;UAAnBb,EAAA,CAAA4C,UAAA,UAAA4I,GAAA,CAAAtH,YAAA,CAAmB;UAIoClE,EAAA,CAAAa,SAAA,GAAoB;UAApBb,EAAA,CAAA4C,UAAA,aAAA4I,GAAA,CAAAnG,OAAA,CAAoB;UACjDrF,EAAA,CAAAa,SAAA,GAAyB;UAAzBb,EAAA,CAAAiM,WAAA,YAAAT,GAAA,CAAAnG,OAAA,CAAyB;UAgBpDrF,EAAA,CAAAa,SAAA,IAAkB;UAAlBb,EAAA,CAAA4C,UAAA,SAAA4I,GAAA,CAAA1G,YAAA,CAAkB;UAGlB9E,EAAA,CAAAa,SAAA,GAA+B;UAA/Bb,EAAA,CAAA4C,UAAA,UAAA4I,GAAA,CAAA1G,YAAA,KAAA0G,GAAA,CAAAnG,OAAA,CAA+B;UAG/BrF,EAAA,CAAAa,SAAA,GAAa;UAAbb,EAAA,CAAA4C,UAAA,SAAA4I,GAAA,CAAAnG,OAAA,CAAa;UASrBrF,EAAA,CAAAa,SAAA,GAAa;UAAbb,EAAA,CAAA4C,UAAA,SAAA4I,GAAA,CAAAnG,OAAA,CAAa;UAObrF,EAAA,CAAAa,SAAA,GAAW;UAAXb,EAAA,CAAA4C,UAAA,SAAA4I,GAAA,CAAApK,KAAA,CAAW;UAMXpB,EAAA,CAAAa,SAAA,GAA8B;UAA9Bb,EAAA,CAAA4C,UAAA,UAAA4I,GAAA,CAAAnG,OAAA,IAAAmG,GAAA,CAAA1G,YAAA,CAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}