{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let LoadingSpinnerComponent = /*#__PURE__*/(() => {\n  class LoadingSpinnerComponent {\n    constructor() {}\n    static {\n      this.ɵfac = function LoadingSpinnerComponent_Factory(t) {\n        return new (t || LoadingSpinnerComponent)();\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: LoadingSpinnerComponent,\n        selectors: [[\"app-loading-spinner\"]],\n        decls: 4,\n        vars: 0,\n        consts: [[1, \"spinner-container\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"]],\n        template: function LoadingSpinnerComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"span\", 2);\n            i0.ɵɵtext(3, \"Chargement...\");\n            i0.ɵɵelementEnd()()();\n          }\n        },\n        styles: [\".spinner-container[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;padding:2rem}\"]\n      });\n    }\n  }\n  return LoadingSpinnerComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}