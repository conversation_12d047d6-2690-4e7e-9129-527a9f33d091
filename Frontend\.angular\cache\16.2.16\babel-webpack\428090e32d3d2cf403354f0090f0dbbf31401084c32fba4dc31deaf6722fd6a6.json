{"ast": null, "code": "import { UserRole } from '../../core/models/user.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../core/services/user-management.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"../../shared/components/sidebar/sidebar.component\";\nimport * as i5 from \"../../shared/components/header/header.component\";\nimport * as i6 from \"../../shared/components/metric-card/metric-card.component\";\nimport * as i7 from \"../../shared/components/status-badge/status-badge.component\";\nimport * as i8 from \"../../shared/components/loading-spinner/loading-spinner.component\";\nimport * as i9 from \"../../shared/components/user-form/user-form.component\";\nfunction UserManagementComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵelement(1, \"app-loading-spinner\");\n    i0.ɵɵelementStart(2, \"p\", 27);\n    i0.ɵɵtext(3, \"Chargement des utilisateurs...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction UserManagementComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵelement(1, \"i\", 22);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction UserManagementComponent_ng_container_14_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"div\", 35);\n    i0.ɵɵelement(2, \"app-metric-card\", 61);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 35);\n    i0.ɵɵelement(4, \"app-metric-card\", 62);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 35);\n    i0.ɵɵelement(6, \"app-metric-card\", 63);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 35);\n    i0.ɵɵelement(8, \"app-metric-card\", 64);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", ctx_r3.stats.totalUsers.toString());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", ctx_r3.stats.activeUsers.toString());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", ctx_r3.stats.onlineUsers.toString());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", ctx_r3.stats.driverUsers.toString());\n  }\n}\nfunction UserManagementComponent_ng_container_14_option_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 65);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const role_r9 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", role_r9.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(role_r9.label);\n  }\n}\nfunction UserManagementComponent_ng_container_14_tr_55_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 66);\n    i0.ɵɵlistener(\"click\", function UserManagementComponent_ng_container_14_tr_55_Template_tr_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r12);\n      const user_r10 = restoredCtx.$implicit;\n      const ctx_r11 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r11.selectUser(user_r10));\n    });\n    i0.ɵɵelementStart(1, \"td\")(2, \"div\", 67)(3, \"div\", 68)(4, \"div\", 69);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\")(7, \"h6\", 70);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"small\", 71);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(11, \"td\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\")(14, \"span\", 72);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"td\");\n    i0.ɵɵelement(17, \"app-status-badge\", 73);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\");\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"td\")(22, \"div\", 74)(23, \"button\", 75);\n    i0.ɵɵlistener(\"click\", function UserManagementComponent_ng_container_14_tr_55_Template_button_click_23_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r12);\n      const user_r10 = restoredCtx.$implicit;\n      const ctx_r13 = i0.ɵɵnextContext(2);\n      ctx_r13.onEditUser(user_r10);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(24, \"i\", 76);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function UserManagementComponent_ng_container_14_tr_55_Template_button_click_25_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r12);\n      const user_r10 = restoredCtx.$implicit;\n      const ctx_r14 = i0.ɵɵnextContext(2);\n      ctx_r14.onToggleUserStatus(user_r10);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(26, \"i\", 78);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"button\", 79);\n    i0.ɵɵlistener(\"click\", function UserManagementComponent_ng_container_14_tr_55_Template_button_click_27_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r12);\n      const user_r10 = restoredCtx.$implicit;\n      const ctx_r15 = i0.ɵɵnextContext(2);\n      ctx_r15.onDeleteUser(user_r10);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelement(28, \"i\", 80);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const user_r10 = ctx.$implicit;\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"table-active\", (ctx_r5.selectedUser == null ? null : ctx_r5.selectedUser.id) === user_r10.id);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\" \", user_r10.firstName.charAt(0), \"\", user_r10.lastName.charAt(0), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", user_r10.firstName, \" \", user_r10.lastName, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"@\", user_r10.username, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(user_r10.email);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r5.getRoleLabel(user_r10.role));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"status\", ctx_r5.getStatusText(user_r10.isActive))(\"variant\", ctx_r5.getStatusClass(user_r10.isActive));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(20, 16, user_r10.createdAt, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"fa-pause\", user_r10.isActive)(\"fa-play\", !user_r10.isActive);\n  }\n}\nfunction UserManagementComponent_ng_container_14_div_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵelement(1, \"i\", 81);\n    i0.ɵɵelementStart(2, \"h5\");\n    i0.ɵɵtext(3, \"Aucun utilisateur trouv\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 71);\n    i0.ɵɵtext(5, \"Aucun utilisateur ne correspond aux crit\\u00E8res de recherche\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction UserManagementComponent_ng_container_14_div_58_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"div\", 82)(2, \"h5\", 52);\n    i0.ɵɵtext(3, \"D\\u00E9tails de l'utilisateur\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 33)(5, \"div\", 83)(6, \"div\", 84)(7, \"div\", 85);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"h5\", 86);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"p\", 87);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\", 88);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"dl\", 89)(16, \"dt\", 90);\n    i0.ɵɵtext(17, \"Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"dd\", 91);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"dt\", 90);\n    i0.ɵɵtext(21, \"T\\u00E9l\\u00E9phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"dd\", 91);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"dt\", 90);\n    i0.ɵɵtext(25, \"Statut\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"dd\", 91);\n    i0.ɵɵelement(27, \"app-status-badge\", 73);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"dt\", 90);\n    i0.ɵɵtext(29, \"Cr\\u00E9\\u00E9 le\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"dd\", 91);\n    i0.ɵɵtext(31);\n    i0.ɵɵpipe(32, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"dt\", 90);\n    i0.ɵɵtext(34, \"Derni\\u00E8re connexion\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"dd\", 91);\n    i0.ɵɵtext(36);\n    i0.ɵɵpipe(37, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(38, \"div\", 92)(39, \"button\", 93);\n    i0.ɵɵlistener(\"click\", function UserManagementComponent_ng_container_14_div_58_Template_button_click_39_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r16 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r16.onEditUser(ctx_r16.selectedUser));\n    });\n    i0.ɵɵelement(40, \"i\", 94);\n    i0.ɵɵtext(41, \" Modifier \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"button\", 95);\n    i0.ɵɵlistener(\"click\", function UserManagementComponent_ng_container_14_div_58_Template_button_click_42_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r18 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r18.onToggleUserStatus(ctx_r18.selectedUser));\n    });\n    i0.ɵɵelement(43, \"i\", 78);\n    i0.ɵɵtext(44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"button\", 96);\n    i0.ɵɵlistener(\"click\", function UserManagementComponent_ng_container_14_div_58_Template_button_click_45_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r19 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r19.onDeleteUser(ctx_r19.selectedUser));\n    });\n    i0.ɵɵelement(46, \"i\", 97);\n    i0.ɵɵtext(47, \" Supprimer \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r7.selectedUser.firstName.charAt(0), \"\", ctx_r7.selectedUser.lastName.charAt(0), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r7.selectedUser.firstName, \" \", ctx_r7.selectedUser.lastName, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"@\", ctx_r7.selectedUser.username, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r7.getRoleLabel(ctx_r7.selectedUser.role));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r7.selectedUser.email);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r7.selectedUser.phoneNumber || \"Non renseign\\u00E9\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"status\", ctx_r7.getStatusText(ctx_r7.selectedUser.isActive))(\"variant\", ctx_r7.getStatusClass(ctx_r7.selectedUser.isActive));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(32, 17, ctx_r7.selectedUser.createdAt, \"dd/MM/yyyy \\u00E0 HH:mm\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r7.selectedUser.lastLoginAt ? i0.ɵɵpipeBind2(37, 20, ctx_r7.selectedUser.lastLoginAt, \"dd/MM/yyyy \\u00E0 HH:mm\") : \"Jamais\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵclassProp(\"fa-pause\", ctx_r7.selectedUser.isActive)(\"fa-play\", !ctx_r7.selectedUser.isActive);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r7.selectedUser.isActive ? \"D\\u00E9sactiver\" : \"Activer\", \" \");\n  }\n}\nfunction UserManagementComponent_ng_container_14_div_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"div\", 98);\n    i0.ɵɵelement(2, \"i\", 99);\n    i0.ɵɵelementStart(3, \"h5\");\n    i0.ɵɵtext(4, \"Aucun utilisateur s\\u00E9lectionn\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 71);\n    i0.ɵɵtext(6, \"S\\u00E9lectionnez un utilisateur dans la liste pour voir les d\\u00E9tails\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction UserManagementComponent_ng_container_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, UserManagementComponent_ng_container_14_div_1_Template, 9, 4, \"div\", 29);\n    i0.ɵɵelementStart(2, \"div\", 30)(3, \"div\", 31)(4, \"div\", 32)(5, \"div\", 33)(6, \"div\", 34)(7, \"div\", 35)(8, \"label\", 36);\n    i0.ɵɵtext(9, \"R\\u00F4le\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"select\", 37);\n    i0.ɵɵlistener(\"ngModelChange\", function UserManagementComponent_ng_container_14_Template_select_ngModelChange_10_listener($event) {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.roleFilter = $event);\n    })(\"change\", function UserManagementComponent_ng_container_14_Template_select_change_10_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.applyFilters());\n    });\n    i0.ɵɵelementStart(11, \"option\", 38);\n    i0.ɵɵtext(12, \"Tous les r\\u00F4les\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, UserManagementComponent_ng_container_14_option_13_Template, 2, 2, \"option\", 39);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 35)(15, \"label\", 40);\n    i0.ɵɵtext(16, \"Statut\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"select\", 41);\n    i0.ɵɵlistener(\"ngModelChange\", function UserManagementComponent_ng_container_14_Template_select_ngModelChange_17_listener($event) {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.statusFilter = $event);\n    })(\"change\", function UserManagementComponent_ng_container_14_Template_select_change_17_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r24 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r24.applyFilters());\n    });\n    i0.ɵɵelementStart(18, \"option\", 38);\n    i0.ɵɵtext(19, \"Tous les statuts\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"option\", 42);\n    i0.ɵɵtext(21, \"Actifs\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"option\", 43);\n    i0.ɵɵtext(23, \"Inactifs\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"div\", 44)(25, \"label\", 45);\n    i0.ɵɵtext(26, \"Recherche\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 46)(28, \"input\", 47);\n    i0.ɵɵlistener(\"ngModelChange\", function UserManagementComponent_ng_container_14_Template_input_ngModelChange_28_listener($event) {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r25 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r25.searchTerm = $event);\n    })(\"input\", function UserManagementComponent_ng_container_14_Template_input_input_28_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.applyFilters());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"span\", 48);\n    i0.ɵɵelement(30, \"i\", 49);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(31, \"div\", 50)(32, \"div\", 51)(33, \"h5\", 52);\n    i0.ɵɵtext(34, \"Utilisateurs\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"span\", 53);\n    i0.ɵɵtext(36);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(37, \"div\", 54)(38, \"div\", 55)(39, \"table\", 56)(40, \"thead\")(41, \"tr\")(42, \"th\");\n    i0.ɵɵtext(43, \"Utilisateur\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"th\");\n    i0.ɵɵtext(45, \"Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"th\");\n    i0.ɵɵtext(47, \"R\\u00F4le\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"th\");\n    i0.ɵɵtext(49, \"Statut\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"th\");\n    i0.ɵɵtext(51, \"Date cr\\u00E9ation\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"th\");\n    i0.ɵɵtext(53, \"Actions\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(54, \"tbody\");\n    i0.ɵɵtemplate(55, UserManagementComponent_ng_container_14_tr_55_Template, 29, 19, \"tr\", 57);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(56, UserManagementComponent_ng_container_14_div_56_Template, 6, 0, \"div\", 9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(57, \"div\", 58);\n    i0.ɵɵtemplate(58, UserManagementComponent_ng_container_14_div_58_Template, 48, 23, \"div\", 59);\n    i0.ɵɵtemplate(59, UserManagementComponent_ng_container_14_div_59_Template, 7, 0, \"div\", 59);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.stats);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.roleFilter);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.userRoles);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.statusFilter);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.searchTerm);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r2.filteredUsers.length);\n    i0.ɵɵadvance(19);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.filteredUsers);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.filteredUsers.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedUser);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.selectedUser);\n  }\n}\nexport let UserManagementComponent = /*#__PURE__*/(() => {\n  class UserManagementComponent {\n    constructor(userManagementService) {\n      this.userManagementService = userManagementService;\n      this.users = [];\n      this.filteredUsers = [];\n      this.selectedUser = null;\n      this.stats = null;\n      this.roleFilter = 'all';\n      this.statusFilter = 'all';\n      this.searchTerm = '';\n      this.loading = true;\n      this.error = '';\n      // Modal states\n      this.showUserForm = false;\n      this.showDeleteConfirm = false;\n      this.editingUser = null;\n      this.deletingUser = null;\n      // User roles for dropdown\n      this.userRoles = [{\n        value: UserRole.Admin,\n        label: 'Administrateur'\n      }, {\n        value: UserRole.Manager,\n        label: 'Gestionnaire'\n      }, {\n        value: UserRole.Dispatcher,\n        label: 'Répartiteur'\n      }, {\n        value: UserRole.Driver,\n        label: 'Livreur'\n      }, {\n        value: UserRole.Customer,\n        label: 'Client'\n      }];\n      this.subscriptions = [];\n    }\n    ngOnInit() {\n      this.loadUsers();\n      this.loadStats();\n    }\n    ngOnDestroy() {\n      this.subscriptions.forEach(sub => sub.unsubscribe());\n    }\n    loadUsers() {\n      this.loading = true;\n      this.subscriptions.push(this.userManagementService.getAllUsers().subscribe({\n        next: users => {\n          this.users = users;\n          this.applyFilters();\n          this.loading = false;\n        },\n        error: err => {\n          console.error('Error loading users', err);\n          this.error = 'Erreur lors du chargement des utilisateurs';\n          this.loading = false;\n        }\n      }));\n    }\n    loadStats() {\n      this.subscriptions.push(this.userManagementService.getUserStats().subscribe({\n        next: stats => {\n          this.stats = stats;\n        },\n        error: err => {\n          console.error('Error loading user stats', err);\n        }\n      }));\n    }\n    applyFilters() {\n      let filtered = [...this.users];\n      // Apply role filter\n      if (this.roleFilter !== 'all') {\n        const roleValue = parseInt(this.roleFilter);\n        filtered = filtered.filter(u => u.role === roleValue);\n      }\n      // Apply status filter\n      if (this.statusFilter !== 'all') {\n        const isActive = this.statusFilter === 'active';\n        filtered = filtered.filter(u => u.isActive === isActive);\n      }\n      // Apply search filter\n      if (this.searchTerm.trim() !== '') {\n        const search = this.searchTerm.toLowerCase();\n        filtered = filtered.filter(u => u.firstName.toLowerCase().includes(search) || u.lastName.toLowerCase().includes(search) || u.email.toLowerCase().includes(search) || u.username.toLowerCase().includes(search));\n      }\n      this.filteredUsers = filtered;\n    }\n    selectUser(user) {\n      this.selectedUser = user;\n    }\n    // CRUD Operations\n    onNewUser() {\n      this.editingUser = null;\n      this.showUserForm = true;\n    }\n    onEditUser(user) {\n      this.editingUser = {\n        ...user\n      };\n      this.showUserForm = true;\n    }\n    onDeleteUser(user) {\n      this.deletingUser = user;\n      this.showDeleteConfirm = true;\n    }\n    onToggleUserStatus(user) {\n      const action = user.isActive ? 'deactivateUser' : 'activateUser';\n      this.subscriptions.push(this.userManagementService[action](user.id).subscribe({\n        next: () => {\n          user.isActive = !user.isActive;\n          this.applyFilters();\n          this.loadStats();\n        },\n        error: err => {\n          console.error(`Error ${action} user`, err);\n          this.error = `Erreur lors de la ${user.isActive ? 'désactivation' : 'activation'} de l'utilisateur`;\n        }\n      }));\n    }\n    // Form handlers\n    onUserFormSubmit(userData) {\n      if (this.editingUser) {\n        // Update existing user\n        const updateRequest = {\n          username: userData.username,\n          email: userData.email,\n          firstName: userData.firstName,\n          lastName: userData.lastName,\n          phoneNumber: userData.phoneNumber\n        };\n        this.subscriptions.push(this.userManagementService.updateUser(this.editingUser.id, updateRequest).subscribe({\n          next: () => {\n            this.loadUsers();\n            this.loadStats();\n            this.showUserForm = false;\n            this.editingUser = null;\n          },\n          error: err => {\n            console.error('Error updating user', err);\n            this.error = 'Erreur lors de la mise à jour de l\\'utilisateur';\n          }\n        }));\n      } else {\n        // Create new user\n        const createRequest = {\n          username: userData.username,\n          email: userData.email,\n          password: userData.password,\n          firstName: userData.firstName,\n          lastName: userData.lastName,\n          role: userData.role,\n          phoneNumber: userData.phoneNumber\n        };\n        this.subscriptions.push(this.userManagementService.createUser(createRequest).subscribe({\n          next: () => {\n            this.loadUsers();\n            this.loadStats();\n            this.showUserForm = false;\n          },\n          error: err => {\n            console.error('Error creating user', err);\n            this.error = 'Erreur lors de la création de l\\'utilisateur';\n          }\n        }));\n      }\n    }\n    onUserFormCancel() {\n      this.showUserForm = false;\n      this.editingUser = null;\n    }\n    // Delete handlers\n    onDeleteConfirm() {\n      if (this.deletingUser) {\n        this.subscriptions.push(this.userManagementService.deleteUser(this.deletingUser.id).subscribe({\n          next: () => {\n            this.loadUsers();\n            this.loadStats();\n            if (this.selectedUser?.id === this.deletingUser.id) {\n              this.selectedUser = null;\n            }\n            this.showDeleteConfirm = false;\n            this.deletingUser = null;\n          },\n          error: err => {\n            console.error('Error deleting user', err);\n            this.error = 'Erreur lors de la suppression de l\\'utilisateur';\n          }\n        }));\n      }\n    }\n    onDeleteCancel() {\n      this.showDeleteConfirm = false;\n      this.deletingUser = null;\n    }\n    // Export functionality\n    onExport() {\n      const csvData = this.convertToCSV(this.filteredUsers);\n      const blob = new Blob([csvData], {\n        type: 'text/csv;charset=utf-8;'\n      });\n      const link = document.createElement('a');\n      const url = URL.createObjectURL(blob);\n      link.setAttribute('href', url);\n      link.setAttribute('download', `utilisateurs_${new Date().toISOString().split('T')[0]}.csv`);\n      link.style.visibility = 'hidden';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n    }\n    convertToCSV(users) {\n      const headers = ['ID', 'Nom d\\'utilisateur', 'Email', 'Prénom', 'Nom', 'Rôle', 'Téléphone', 'Statut', 'Date création'];\n      const csvContent = [headers.join(','), ...users.map(u => [u.id, `\"${u.username}\"`, `\"${u.email}\"`, `\"${u.firstName}\"`, `\"${u.lastName}\"`, this.getRoleLabel(u.role), `\"${u.phoneNumber || ''}\"`, u.isActive ? 'Actif' : 'Inactif', new Date(u.createdAt).toLocaleDateString()].join(','))].join('\\n');\n      return csvContent;\n    }\n    getRoleLabel(role) {\n      const roleItem = this.userRoles.find(r => r.value === role);\n      return roleItem ? roleItem.label : 'Inconnu';\n    }\n    getStatusClass(isActive) {\n      return isActive ? 'success' : 'danger';\n    }\n    getStatusText(isActive) {\n      return isActive ? 'Actif' : 'Inactif';\n    }\n    updateUser(updatedUser) {\n      const index = this.users.findIndex(u => u.id === updatedUser.id);\n      if (index !== -1) {\n        this.users[index] = updatedUser;\n        this.applyFilters();\n        if (this.selectedUser?.id === updatedUser.id) {\n          this.selectedUser = updatedUser;\n        }\n      }\n    }\n    static {\n      this.ɵfac = function UserManagementComponent_Factory(t) {\n        return new (t || UserManagementComponent)(i0.ɵɵdirectiveInject(i1.UserManagementService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: UserManagementComponent,\n        selectors: [[\"app-user-management\"]],\n        decls: 45,\n        vars: 16,\n        consts: [[1, \"user-management-container\"], [1, \"user-management-content\"], [\"title\", \"Gestion des utilisateurs\", \"subtitle\", \"G\\u00E9rez les comptes utilisateurs et leurs permissions\"], [1, \"d-flex\", \"gap-2\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"fa-solid\", \"fa-plus\", \"me-2\"], [1, \"btn\", \"btn-outline-secondary\", 3, \"click\"], [1, \"fa-solid\", \"fa-file-export\", \"me-2\"], [1, \"user-management-body\", \"p-4\"], [\"class\", \"text-center py-5\", 4, \"ngIf\"], [\"class\", \"alert alert-danger\", \"role\", \"alert\", 4, \"ngIf\"], [4, \"ngIf\"], [\"tabindex\", \"-1\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\", \"modal-lg\"], [1, \"modal-content\"], [1, \"modal-header\"], [1, \"modal-title\"], [\"type\", \"button\", 1, \"btn-close\", 3, \"click\"], [1, \"modal-body\"], [3, \"user\", \"submitted\", \"cancelled\"], [1, \"modal-dialog\"], [1, \"alert\", \"alert-warning\"], [1, \"fa-solid\", \"fa-exclamation-triangle\", \"me-2\"], [1, \"modal-footer\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-danger\", 3, \"click\"], [1, \"text-center\", \"py-5\"], [1, \"mt-3\", \"text-muted\"], [\"role\", \"alert\", 1, \"alert\", \"alert-danger\"], [\"class\", \"row g-4 mb-4\", 4, \"ngIf\"], [1, \"row\", \"g-4\"], [1, \"col-lg-8\"], [1, \"card\", \"mb-4\"], [1, \"card-body\"], [1, \"row\", \"g-3\"], [1, \"col-md-3\"], [\"for\", \"roleFilter\", 1, \"form-label\"], [\"id\", \"roleFilter\", 1, \"form-select\", 3, \"ngModel\", \"ngModelChange\", \"change\"], [\"value\", \"all\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"statusFilter\", 1, \"form-label\"], [\"id\", \"statusFilter\", 1, \"form-select\", 3, \"ngModel\", \"ngModelChange\", \"change\"], [\"value\", \"active\"], [\"value\", \"inactive\"], [1, \"col-md-6\"], [\"for\", \"searchFilter\", 1, \"form-label\"], [1, \"input-group\"], [\"type\", \"text\", \"id\", \"searchFilter\", \"placeholder\", \"Rechercher par nom, email...\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\", \"input\"], [1, \"input-group-text\"], [1, \"fa-solid\", \"fa-search\"], [1, \"card\"], [1, \"card-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"card-title\", \"mb-0\"], [1, \"badge\", \"bg-primary\"], [1, \"card-body\", \"p-0\"], [1, \"table-responsive\"], [1, \"table\", \"table-hover\", \"mb-0\"], [\"style\", \"cursor: pointer;\", 3, \"table-active\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-lg-4\"], [\"class\", \"card\", 4, \"ngIf\"], [1, \"row\", \"g-4\", \"mb-4\"], [\"title\", \"Total utilisateurs\", \"subtitle\", \"Tous les comptes\", \"icon\", \"fa-solid fa-users\", \"iconBgClass\", \"bg-primary-light\", \"iconTextClass\", \"text-primary\", 3, \"value\"], [\"title\", \"Utilisateurs actifs\", \"subtitle\", \"Comptes activ\\u00E9s\", \"icon\", \"fa-solid fa-user-check\", \"iconBgClass\", \"bg-success-light\", \"iconTextClass\", \"text-success\", 3, \"value\"], [\"title\", \"En ligne\", \"subtitle\", \"Connect\\u00E9s maintenant\", \"icon\", \"fa-solid fa-circle\", \"iconBgClass\", \"bg-info-light\", \"iconTextClass\", \"text-info\", 3, \"value\"], [\"title\", \"Livreurs\", \"subtitle\", \"Comptes livreurs\", \"icon\", \"fa-solid fa-truck\", \"iconBgClass\", \"bg-warning-light\", \"iconTextClass\", \"text-warning\", 3, \"value\"], [3, \"value\"], [2, \"cursor\", \"pointer\", 3, \"click\"], [1, \"d-flex\", \"align-items-center\"], [1, \"avatar-sm\", \"me-3\"], [1, \"avatar-title\", \"bg-light\", \"text-primary\", \"rounded-circle\"], [1, \"mb-0\"], [1, \"text-muted\"], [1, \"badge\", \"bg-secondary\"], [3, \"status\", \"variant\"], [1, \"btn-group\", \"btn-group-sm\"], [1, \"btn\", \"btn-outline-primary\", 3, \"click\"], [1, \"fa-solid\", \"fa-pen\"], [1, \"btn\", \"btn-outline-warning\", 3, \"click\"], [1, \"fa-solid\"], [1, \"btn\", \"btn-outline-danger\", 3, \"click\"], [1, \"fa-solid\", \"fa-trash\"], [1, \"fa-solid\", \"fa-users\", \"fa-3x\", \"text-muted\", \"mb-3\"], [1, \"card-header\"], [1, \"text-center\", \"mb-4\"], [1, \"avatar-lg\", \"mx-auto\", \"mb-3\"], [1, \"avatar-title\", \"bg-primary\", \"text-white\", \"rounded-circle\", \"fs-2\"], [1, \"mb-1\"], [1, \"text-muted\", \"mb-0\"], [1, \"badge\", \"bg-secondary\", \"mt-2\"], [1, \"row\", \"mb-0\"], [1, \"col-sm-5\"], [1, \"col-sm-7\"], [1, \"mt-4\", \"d-flex\", \"gap-2\"], [1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\"], [1, \"fa-solid\", \"fa-pen-to-square\", \"me-2\"], [1, \"btn\", \"btn-outline-warning\", \"btn-sm\", 3, \"click\"], [1, \"btn\", \"btn-outline-danger\", \"btn-sm\", 3, \"click\"], [1, \"fa-solid\", \"fa-trash\", \"me-2\"], [1, \"card-body\", \"text-center\", \"p-5\"], [1, \"fa-solid\", \"fa-user\", \"fa-3x\", \"text-muted\", \"mb-3\"]],\n        template: function UserManagementComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0);\n            i0.ɵɵelement(1, \"app-sidebar\");\n            i0.ɵɵelementStart(2, \"div\", 1)(3, \"app-header\", 2)(4, \"div\", 3)(5, \"button\", 4);\n            i0.ɵɵlistener(\"click\", function UserManagementComponent_Template_button_click_5_listener() {\n              return ctx.onNewUser();\n            });\n            i0.ɵɵelement(6, \"i\", 5);\n            i0.ɵɵtext(7, \" Nouvel utilisateur \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(8, \"button\", 6);\n            i0.ɵɵlistener(\"click\", function UserManagementComponent_Template_button_click_8_listener() {\n              return ctx.onExport();\n            });\n            i0.ɵɵelement(9, \"i\", 7);\n            i0.ɵɵtext(10, \" Exporter \");\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(11, \"div\", 8);\n            i0.ɵɵtemplate(12, UserManagementComponent_div_12_Template, 4, 0, \"div\", 9);\n            i0.ɵɵtemplate(13, UserManagementComponent_div_13_Template, 3, 1, \"div\", 10);\n            i0.ɵɵtemplate(14, UserManagementComponent_ng_container_14_Template, 60, 10, \"ng-container\", 11);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(15, \"div\", 12)(16, \"div\", 13)(17, \"div\", 14)(18, \"div\", 15)(19, \"h5\", 16);\n            i0.ɵɵtext(20);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(21, \"button\", 17);\n            i0.ɵɵlistener(\"click\", function UserManagementComponent_Template_button_click_21_listener() {\n              return ctx.onUserFormCancel();\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(22, \"div\", 18)(23, \"app-user-form\", 19);\n            i0.ɵɵlistener(\"submitted\", function UserManagementComponent_Template_app_user_form_submitted_23_listener($event) {\n              return ctx.onUserFormSubmit($event);\n            })(\"cancelled\", function UserManagementComponent_Template_app_user_form_cancelled_23_listener() {\n              return ctx.onUserFormCancel();\n            });\n            i0.ɵɵelementEnd()()()()();\n            i0.ɵɵelementStart(24, \"div\", 12)(25, \"div\", 20)(26, \"div\", 14)(27, \"div\", 15)(28, \"h5\", 16);\n            i0.ɵɵtext(29, \"Confirmer la suppression\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(30, \"button\", 17);\n            i0.ɵɵlistener(\"click\", function UserManagementComponent_Template_button_click_30_listener() {\n              return ctx.onDeleteCancel();\n            });\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(31, \"div\", 18)(32, \"p\");\n            i0.ɵɵtext(33, \"\\u00CAtes-vous s\\u00FBr de vouloir supprimer cet utilisateur ?\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(34, \"p\")(35, \"strong\");\n            i0.ɵɵtext(36);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(37, \"div\", 21);\n            i0.ɵɵelement(38, \"i\", 22);\n            i0.ɵɵtext(39, \" Cette action est irr\\u00E9versible et supprimera d\\u00E9finitivement le compte utilisateur. \");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(40, \"div\", 23)(41, \"button\", 24);\n            i0.ɵɵlistener(\"click\", function UserManagementComponent_Template_button_click_41_listener() {\n              return ctx.onDeleteCancel();\n            });\n            i0.ɵɵtext(42, \"Annuler\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(43, \"button\", 25);\n            i0.ɵɵlistener(\"click\", function UserManagementComponent_Template_button_click_43_listener() {\n              return ctx.onDeleteConfirm();\n            });\n            i0.ɵɵtext(44, \"Supprimer\");\n            i0.ɵɵelementEnd()()()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵadvance(12);\n            i0.ɵɵproperty(\"ngIf\", ctx.loading);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.error);\n            i0.ɵɵadvance(1);\n            i0.ɵɵstyleProp(\"display\", ctx.showUserForm ? \"block\" : \"none\");\n            i0.ɵɵclassProp(\"show\", ctx.showUserForm);\n            i0.ɵɵadvance(5);\n            i0.ɵɵtextInterpolate1(\" \", ctx.editingUser ? \"Modifier l'utilisateur\" : \"Nouvel utilisateur\", \" \");\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"user\", ctx.editingUser);\n            i0.ɵɵadvance(1);\n            i0.ɵɵstyleProp(\"display\", ctx.showDeleteConfirm ? \"block\" : \"none\");\n            i0.ɵɵclassProp(\"show\", ctx.showDeleteConfirm);\n            i0.ɵɵadvance(12);\n            i0.ɵɵtextInterpolate3(\"\", ctx.deletingUser == null ? null : ctx.deletingUser.firstName, \" \", ctx.deletingUser == null ? null : ctx.deletingUser.lastName, \" (\", ctx.deletingUser == null ? null : ctx.deletingUser.email, \")\");\n          }\n        },\n        dependencies: [i2.NgForOf, i2.NgIf, i3.NgSelectOption, i3.ɵNgSelectMultipleOption, i3.DefaultValueAccessor, i3.SelectControlValueAccessor, i3.NgControlStatus, i3.NgModel, i4.SidebarComponent, i5.HeaderComponent, i6.MetricCardComponent, i7.StatusBadgeComponent, i8.LoadingSpinnerComponent, i9.UserFormComponent, i2.DatePipe],\n        styles: [\".user-management-container[_ngcontent-%COMP%]{display:flex;min-height:100vh;background-color:#f8f9fa}.user-management-content[_ngcontent-%COMP%]{flex:1;display:flex;flex-direction:column}.user-management-body[_ngcontent-%COMP%]{flex:1;overflow-y:auto}.avatar-sm[_ngcontent-%COMP%]{width:40px;height:40px}.avatar-lg[_ngcontent-%COMP%]{width:80px;height:80px}.avatar-title[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;width:100%;height:100%;font-weight:600}.table-responsive[_ngcontent-%COMP%]{max-height:600px;overflow-y:auto}.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]{transition:background-color .2s ease}.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover{background-color:#007bff0d}.btn-group-sm[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{padding:.25rem .5rem;font-size:.875rem}.card[_ngcontent-%COMP%]{border:none;box-shadow:0 .125rem .25rem #00000013;transition:box-shadow .15s ease-in-out}.card[_ngcontent-%COMP%]:hover{box-shadow:0 .5rem 1rem #00000026}.bg-primary-light[_ngcontent-%COMP%]{background-color:#0d6efd1a!important}.bg-success-light[_ngcontent-%COMP%]{background-color:#1987541a!important}.bg-info-light[_ngcontent-%COMP%]{background-color:#0dcaf01a!important}.bg-warning-light[_ngcontent-%COMP%]{background-color:#ffc1071a!important}.text-primary[_ngcontent-%COMP%]{color:#0d6efd!important}.text-success[_ngcontent-%COMP%]{color:#198754!important}.text-info[_ngcontent-%COMP%]{color:#0dcaf0!important}.text-warning[_ngcontent-%COMP%]{color:#ffc107!important}@media (max-width: 768px){.user-management-container[_ngcontent-%COMP%]{flex-direction:column}.table-responsive[_ngcontent-%COMP%]{font-size:.875rem}.btn-group-sm[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{padding:.125rem .25rem;font-size:.75rem}.avatar-sm[_ngcontent-%COMP%]{width:32px;height:32px}.avatar-lg[_ngcontent-%COMP%]{width:60px;height:60px}}\"]\n      });\n    }\n  }\n  return UserManagementComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}