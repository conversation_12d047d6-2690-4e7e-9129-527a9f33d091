{"ast": null, "code": "export var DeliveryStatus;\n(function (DeliveryStatus) {\n  DeliveryStatus[DeliveryStatus[\"Pending\"] = 0] = \"Pending\";\n  DeliveryStatus[DeliveryStatus[\"InTransit\"] = 1] = \"InTransit\";\n  DeliveryStatus[DeliveryStatus[\"Delivered\"] = 2] = \"Delivered\";\n  DeliveryStatus[DeliveryStatus[\"Delayed\"] = 3] = \"Delayed\";\n  DeliveryStatus[DeliveryStatus[\"Cancelled\"] = 4] = \"Cancelled\";\n})(DeliveryStatus || (DeliveryStatus = {}));\nexport var DeliveryPriority;\n(function (DeliveryPriority) {\n  DeliveryPriority[DeliveryPriority[\"Low\"] = 0] = \"Low\";\n  DeliveryPriority[DeliveryPriority[\"Medium\"] = 1] = \"Medium\";\n  DeliveryPriority[DeliveryPriority[\"High\"] = 2] = \"High\";\n  DeliveryPriority[DeliveryPriority[\"Urgent\"] = 3] = \"Urgent\";\n})(DeliveryPriority || (DeliveryPriority = {}));\nexport var WeatherCondition;\n(function (WeatherCondition) {\n  WeatherCondition[WeatherCondition[\"Clear\"] = 0] = \"Clear\";\n  WeatherCondition[WeatherCondition[\"Cloudy\"] = 1] = \"Cloudy\";\n  WeatherCondition[WeatherCondition[\"Rainy\"] = 2] = \"Rainy\";\n  WeatherCondition[WeatherCondition[\"Snowy\"] = 3] = \"Snowy\";\n  WeatherCondition[WeatherCondition[\"Stormy\"] = 4] = \"Stormy\";\n})(WeatherCondition || (WeatherCondition = {}));\nexport var TrafficCondition;\n(function (TrafficCondition) {\n  TrafficCondition[TrafficCondition[\"Light\"] = 0] = \"Light\";\n  TrafficCondition[TrafficCondition[\"Moderate\"] = 1] = \"Moderate\";\n  TrafficCondition[TrafficCondition[\"Heavy\"] = 2] = \"Heavy\";\n  TrafficCondition[TrafficCondition[\"Severe\"] = 3] = \"Severe\";\n})(TrafficCondition || (TrafficCondition = {}));", "map": {"version": 3, "names": ["DeliveryStatus", "DeliveryPriority", "WeatherCondition", "TrafficCondition"], "sources": ["C:\\Users\\<USER>\\delivery-dash-optimiser\\frontend\\src\\app\\core\\models\\delivery.model.ts"], "sourcesContent": ["export interface Delivery {\n  id: string;\n  orderId: string;\n  customerId: string;\n  customerName: string;\n  address: string;\n  status: DeliveryStatus;\n  driverId: string;\n  driverName: string;\n  estimatedDeliveryTime: Date;\n  actualDeliveryTime?: Date;\n  priority: DeliveryPriority;\n  coordinates: GeoCoordinates;\n  createdAt: Date;\n  pickupTime?: Date;\n  notes?: string;\n  distance?: number;\n  customerRating?: number;\n  customerFeedback?: string;\n  weatherCondition?: WeatherCondition;\n  trafficCondition?: TrafficCondition;\n}\n\nexport enum DeliveryStatus {\n  Pending = 0,\n  InTransit = 1,\n  Delivered = 2,\n  Delayed = 3,\n  Cancelled = 4\n}\n\nexport enum DeliveryPriority {\n  Low = 0,\n  Medium = 1,\n  High = 2,\n  Urgent = 3\n}\n\nexport enum WeatherCondition {\n  Clear = 0,\n  Cloudy = 1,\n  Rainy = 2,\n  Snowy = 3,\n  Stormy = 4\n}\n\nexport enum TrafficCondition {\n  Light = 0,\n  Moderate = 1,\n  Heavy = 2,\n  Severe = 3\n}\n\nexport interface GeoCoordinates {\n  latitude: number;\n  longitude: number;\n}\n"], "mappings": "AAuBA,WAAYA,cAMX;AAND,WAAYA,cAAc;EACxBA,cAAA,CAAAA,cAAA,4BAAW;EACXA,cAAA,CAAAA,cAAA,gCAAa;EACbA,cAAA,CAAAA,cAAA,gCAAa;EACbA,cAAA,CAAAA,cAAA,4BAAW;EACXA,cAAA,CAAAA,cAAA,gCAAa;AACf,CAAC,EANWA,cAAc,KAAdA,cAAc;AAQ1B,WAAYC,gBAKX;AALD,WAAYA,gBAAgB;EAC1BA,gBAAA,CAAAA,gBAAA,oBAAO;EACPA,gBAAA,CAAAA,gBAAA,0BAAU;EACVA,gBAAA,CAAAA,gBAAA,sBAAQ;EACRA,gBAAA,CAAAA,gBAAA,0BAAU;AACZ,CAAC,EALWA,gBAAgB,KAAhBA,gBAAgB;AAO5B,WAAYC,gBAMX;AAND,WAAYA,gBAAgB;EAC1BA,gBAAA,CAAAA,gBAAA,wBAAS;EACTA,gBAAA,CAAAA,gBAAA,0BAAU;EACVA,gBAAA,CAAAA,gBAAA,wBAAS;EACTA,gBAAA,CAAAA,gBAAA,wBAAS;EACTA,gBAAA,CAAAA,gBAAA,0BAAU;AACZ,CAAC,EANWA,gBAAgB,KAAhBA,gBAAgB;AAQ5B,WAAYC,gBAKX;AALD,WAAYA,gBAAgB;EAC1BA,gBAAA,CAAAA,gBAAA,wBAAS;EACTA,gBAAA,CAAAA,gBAAA,8BAAY;EACZA,gBAAA,CAAAA,gBAAA,wBAAS;EACTA,gBAAA,CAAAA,gBAAA,0BAAU;AACZ,CAAC,EALWA,gBAAgB,KAAhBA,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}