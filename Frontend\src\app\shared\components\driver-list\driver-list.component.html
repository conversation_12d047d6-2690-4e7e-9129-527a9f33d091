<div class="driver-list">
  <div *ngIf="loading" class="text-center">
    <app-loading-spinner></app-loading-spinner>
  </div>

  <div *ngIf="!loading && drivers.length === 0" class="text-center text-muted">
    <p><PERSON>cun chauffeur trouvé</p>
  </div>

  <div *ngIf="!loading && drivers.length > 0" class="table-responsive">
    <table class="table table-striped">
      <thead>
        <tr>
          <th>ID</th>
          <th>Nom</th>
          <th>Téléphone</th>
          <th>Statut</th>
          <th>Véhicule</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let driver of drivers">
          <td>{{ driver.id }}</td>
          <td>{{ driver.name }}</td>
          <td>{{ driver.phone }}</td>
          <td>
            <span class="badge" [ngClass]="{
              'bg-success': driver.status === 0,
              'bg-warning': driver.status === 1,
              'bg-secondary': driver.status === 3
            }">
              {{ driver.status }}
            </span>
          </td>
          <td>{{ driver.vehicleType }}</td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
