{"ast": null, "code": "import { DriverStatus } from '../../core/models/driver.model';\nimport { DeliveryStatus } from '../../core/models/delivery.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../core/services/driver.service\";\nimport * as i2 from \"../../core/services/delivery.service\";\nimport * as i3 from \"../../core/services/real-time.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"../../shared/components/sidebar/sidebar.component\";\nimport * as i7 from \"../../shared/components/header/header.component\";\nimport * as i8 from \"../../shared/components/status-badge/status-badge.component\";\nimport * as i9 from \"../../shared/components/map-view/map-view.component\";\nimport * as i10 from \"../../shared/components/driver-form/driver-form.component\";\nfunction DriversManagementComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"div\", 27)(2, \"span\", 28);\n    i0.ɵɵtext(3, \"Chargement...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 29);\n    i0.ɵɵtext(5, \"Chargement des donn\\u00E9es...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DriversManagementComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction DriversManagementComponent_ng_container_14_button_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function DriversManagementComponent_ng_container_14_button_35_Template_button_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r10);\n      const driver_r8 = restoredCtx.$implicit;\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.selectDriver(driver_r8));\n    });\n    i0.ɵɵelementStart(1, \"div\", 61)(2, \"div\", 62);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 63)(5, \"h6\", 64);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 65);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 66);\n    i0.ɵɵelement(10, \"app-status-badge\", 67);\n    i0.ɵɵelementStart(11, \"p\", 68);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const driver_r8 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", (ctx_r4.selectedDriver == null ? null : ctx_r4.selectedDriver.id) === driver_r8.id);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", driver_r8.name.charAt(0), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(driver_r8.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(driver_r8.vehicleType);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"status\", ctx_r4.getStatusText(driver_r8.status))(\"variant\", ctx_r4.getStatusClass(driver_r8.status));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", driver_r8.todayDeliveries, \" livraisons \");\n  }\n}\nfunction DriversManagementComponent_ng_container_14_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69);\n    i0.ɵɵelement(1, \"i\", 70);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Aucun livreur trouv\\u00E9\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DriversManagementComponent_ng_container_14_div_38_span_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 99);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\"(\", ctx_r11.selectedDriver.vehicleId, \")\");\n  }\n}\nfunction DriversManagementComponent_ng_container_14_div_38_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 100)(1, \"div\", 101)(2, \"span\", 28);\n    i0.ɵɵtext(3, \"Chargement...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction DriversManagementComponent_ng_container_14_div_38_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"div\", 102)(2, \"div\", 103)(3, \"div\", 104);\n    i0.ɵɵtext(4, \"Livraisons totales\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 105);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"div\", 102)(8, \"div\", 103)(9, \"div\", 104);\n    i0.ɵɵtext(10, \"Taux de ponctualit\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 105);\n    i0.ɵɵtext(12);\n    i0.ɵɵpipe(13, \"number\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"div\", 102)(15, \"div\", 103)(16, \"div\", 104);\n    i0.ɵɵtext(17, \"Temps moyen\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 105);\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"number\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(21, \"div\", 102)(22, \"div\", 103)(23, \"div\", 104);\n    i0.ɵɵtext(24, \"Satisfaction client\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 105);\n    i0.ɵɵtext(26);\n    i0.ɵɵpipe(27, \"number\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(28, \"div\", 102)(29, \"div\", 103)(30, \"div\", 104);\n    i0.ɵɵtext(31, \"Vitesse moyenne\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 105);\n    i0.ɵɵtext(33);\n    i0.ɵɵpipe(34, \"number\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(35, \"div\", 102)(36, \"div\", 103)(37, \"div\", 104);\n    i0.ɵɵtext(38, \"Livraisons par heure\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"div\", 105);\n    i0.ɵɵtext(40);\n    i0.ɵɵpipe(41, \"number\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r13.driverPerformance.totalDeliveries);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(13, 6, ctx_r13.driverPerformance.onTimeRate, \"1.0-0\"), \"%\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(20, 9, ctx_r13.driverPerformance.avgDeliveryTime, \"1.0-0\"), \" min\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(27, 12, ctx_r13.driverPerformance.customerSatisfaction, \"1.1-1\"), \"/5\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(34, 15, ctx_r13.driverPerformance.avgSpeed, \"1.0-0\"), \" km/h\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(41, 18, ctx_r13.driverPerformance.deliveriesPerHour, \"1.1-1\"));\n  }\n}\nfunction DriversManagementComponent_ng_container_14_div_38_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 106);\n    i0.ɵɵelement(1, \"i\", 107);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Aucune donn\\u00E9e de performance disponible\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DriversManagementComponent_ng_container_14_div_38_div_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 100)(1, \"div\", 101)(2, \"span\", 28);\n    i0.ɵɵtext(3, \"Chargement...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction DriversManagementComponent_ng_container_14_div_38_div_62_tr_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\");\n    i0.ɵɵelement(8, \"app-status-badge\", 67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\");\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const delivery_r19 = ctx.$implicit;\n    const ctx_r18 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(delivery_r19.orderId);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(delivery_r19.customerName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(delivery_r19.address);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"status\", ctx_r18.getDeliveryStatusText(delivery_r19.status))(\"variant\", ctx_r18.getDeliveryStatusClass(delivery_r19.status));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(11, 7, delivery_r19.estimatedDeliveryTime, \"HH:mm\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(delivery_r19.actualDeliveryTime ? i0.ɵɵpipeBind2(14, 10, delivery_r19.actualDeliveryTime, \"HH:mm\") : \"-\");\n  }\n}\nfunction DriversManagementComponent_ng_container_14_div_38_div_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 108)(1, \"table\", 109)(2, \"thead\")(3, \"tr\")(4, \"th\");\n    i0.ɵɵtext(5, \"ID\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7, \"Client\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"Adresse\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Statut\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\");\n    i0.ɵɵtext(13, \"Heure estim\\u00E9e\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"th\");\n    i0.ɵɵtext(15, \"Heure r\\u00E9elle\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"tbody\");\n    i0.ɵɵtemplate(17, DriversManagementComponent_ng_container_14_div_38_div_62_tr_17_Template, 15, 13, \"tr\", 110);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(17);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r16.driverDeliveries);\n  }\n}\nfunction DriversManagementComponent_ng_container_14_div_38_div_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 106);\n    i0.ɵɵelement(1, \"i\", 111);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Aucune livraison trouv\\u00E9e pour ce livreur\");\n    i0.ɵɵelementEnd()();\n  }\n}\nconst _c0 = function (a0) {\n  return [a0];\n};\nfunction DriversManagementComponent_ng_container_14_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"div\", 71)(2, \"div\", 49)(3, \"div\", 34)(4, \"div\", 72)(5, \"div\", 73);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 63)(8, \"div\", 74)(9, \"h4\", 75);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(11, \"app-status-badge\", 67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 76)(13, \"div\", 36)(14, \"p\", 64);\n    i0.ɵɵelement(15, \"i\", 77);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"p\", 64);\n    i0.ɵɵelement(18, \"i\", 78);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"p\", 75);\n    i0.ɵɵelement(21, \"i\", 79);\n    i0.ɵɵtext(22);\n    i0.ɵɵtemplate(23, DriversManagementComponent_ng_container_14_div_38_span_23_Template, 2, 1, \"span\", 80);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 36)(25, \"p\", 64);\n    i0.ɵɵelement(26, \"i\", 81);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"p\", 64);\n    i0.ɵɵelement(29, \"i\", 82);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"p\", 75);\n    i0.ɵɵelement(32, \"i\", 83);\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd()()()()()()()();\n    i0.ɵɵelementStart(34, \"div\", 36)(35, \"div\", 84)(36, \"div\", 85)(37, \"h5\", 51);\n    i0.ɵɵelement(38, \"i\", 86);\n    i0.ɵɵtext(39, \" M\\u00E9triques de performance \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"div\", 34);\n    i0.ɵɵtemplate(41, DriversManagementComponent_ng_container_14_div_38_div_41_Template, 4, 0, \"div\", 87);\n    i0.ɵɵtemplate(42, DriversManagementComponent_ng_container_14_div_38_div_42_Template, 42, 21, \"div\", 88);\n    i0.ɵɵtemplate(43, DriversManagementComponent_ng_container_14_div_38_div_43_Template, 4, 0, \"div\", 89);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(44, \"div\", 36)(45, \"div\", 84)(46, \"div\", 85)(47, \"h5\", 51);\n    i0.ɵɵelement(48, \"i\", 90);\n    i0.ɵɵtext(49, \" Position actuelle \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(50, \"div\", 53);\n    i0.ɵɵelement(51, \"app-map-view\", 91);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(52, \"div\", 71)(53, \"div\", 49)(54, \"div\", 50)(55, \"h5\", 51);\n    i0.ɵɵelement(56, \"i\", 92);\n    i0.ɵɵtext(57, \" Livraisons r\\u00E9centes \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"span\", 52);\n    i0.ɵɵtext(59);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(60, \"div\", 53);\n    i0.ɵɵtemplate(61, DriversManagementComponent_ng_container_14_div_38_div_61_Template, 4, 0, \"div\", 87);\n    i0.ɵɵtemplate(62, DriversManagementComponent_ng_container_14_div_38_div_62_Template, 18, 1, \"div\", 93);\n    i0.ɵɵtemplate(63, DriversManagementComponent_ng_container_14_div_38_div_63_Template, 4, 0, \"div\", 89);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(64, \"div\", 71)(65, \"div\", 3)(66, \"button\", 4);\n    i0.ɵɵlistener(\"click\", function DriversManagementComponent_ng_container_14_div_38_Template_button_click_66_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r20.onEditDriver(ctx_r20.selectedDriver));\n    });\n    i0.ɵɵelement(67, \"i\", 94);\n    i0.ɵɵtext(68, \" Modifier \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(69, \"button\", 95);\n    i0.ɵɵelement(70, \"i\", 96);\n    i0.ɵɵtext(71, \" Optimiser l'itin\\u00E9raire \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(72, \"button\", 97);\n    i0.ɵɵlistener(\"click\", function DriversManagementComponent_ng_container_14_div_38_Template_button_click_72_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r22 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r22.onDeleteDriver(ctx_r22.selectedDriver));\n    });\n    i0.ɵɵelement(73, \"i\", 98);\n    i0.ɵɵtext(74, \" Supprimer \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.selectedDriver.name.charAt(0), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r6.selectedDriver.name);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"status\", ctx_r6.getStatusText(ctx_r6.selectedDriver.status))(\"variant\", ctx_r6.getStatusClass(ctx_r6.selectedDriver.status));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.selectedDriver.email, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.selectedDriver.phone, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.selectedDriver.vehicleType, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.selectedDriver.vehicleId);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" Embauch\\u00E9 le \", ctx_r6.formatDate(ctx_r6.selectedDriver.hireDate), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" Permis: \", ctx_r6.selectedDriver.licenseNumber || \"N/A\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" Derni\\u00E8re activit\\u00E9: \", ctx_r6.formatDate(ctx_r6.selectedDriver.lastActive), \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.performanceLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r6.performanceLoading && ctx_r6.driverPerformance);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r6.performanceLoading && !ctx_r6.driverPerformance);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"drivers\", i0.ɵɵpureFunction1(22, _c0, ctx_r6.selectedDriver))(\"height\", 250)(\"center\", ctx_r6.getDriverMapCenter())(\"zoom\", 14);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r6.driverDeliveries.length);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.deliveriesLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r6.deliveriesLoading && ctx_r6.driverDeliveries.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r6.deliveriesLoading && ctx_r6.driverDeliveries.length === 0);\n  }\n}\nfunction DriversManagementComponent_ng_container_14_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"div\", 112);\n    i0.ɵɵelement(2, \"i\", 113);\n    i0.ɵɵelementStart(3, \"h5\");\n    i0.ɵɵtext(4, \"Aucun livreur s\\u00E9lectionn\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 21);\n    i0.ɵɵtext(6, \"S\\u00E9lectionnez un livreur dans la liste pour voir les d\\u00E9tails\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction DriversManagementComponent_ng_container_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 31)(2, \"div\", 32)(3, \"div\", 33)(4, \"div\", 34)(5, \"div\", 35)(6, \"div\", 36)(7, \"label\", 37);\n    i0.ɵɵtext(8, \"Statut\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"select\", 38);\n    i0.ɵɵlistener(\"ngModelChange\", function DriversManagementComponent_ng_container_14_Template_select_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.statusFilter = $event);\n    })(\"change\", function DriversManagementComponent_ng_container_14_Template_select_change_9_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r25 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r25.applyFilters());\n    });\n    i0.ɵɵelementStart(10, \"option\", 39);\n    i0.ɵɵtext(11, \"Tous les statuts\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"option\", 40);\n    i0.ɵɵtext(13, \"Actif\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"option\", 41);\n    i0.ɵɵtext(15, \"Inactif\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"option\", 42);\n    i0.ɵɵtext(17, \"En pause\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"option\", 43);\n    i0.ɵɵtext(19, \"Hors ligne\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(20, \"div\", 36)(21, \"label\", 44);\n    i0.ɵɵtext(22, \"Recherche\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 45)(24, \"input\", 46);\n    i0.ɵɵlistener(\"ngModelChange\", function DriversManagementComponent_ng_container_14_Template_input_ngModelChange_24_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.searchTerm = $event);\n    })(\"input\", function DriversManagementComponent_ng_container_14_Template_input_input_24_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r27 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r27.applyFilters());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"span\", 47);\n    i0.ɵɵelement(26, \"i\", 48);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(27, \"div\", 49)(28, \"div\", 50)(29, \"h5\", 51);\n    i0.ɵɵtext(30, \"Livreurs\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"span\", 52);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"div\", 53)(34, \"div\", 54);\n    i0.ɵɵtemplate(35, DriversManagementComponent_ng_container_14_button_35_Template, 13, 8, \"button\", 55);\n    i0.ɵɵtemplate(36, DriversManagementComponent_ng_container_14_div_36_Template, 4, 0, \"div\", 56);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(37, \"div\", 57);\n    i0.ɵɵtemplate(38, DriversManagementComponent_ng_container_14_div_38_Template, 75, 24, \"div\", 58);\n    i0.ɵɵtemplate(39, DriversManagementComponent_ng_container_14_div_39_Template, 7, 0, \"div\", 59);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.statusFilter);\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.searchTerm);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r2.filteredDrivers.length);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.filteredDrivers);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.filteredDrivers.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedDriver);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.selectedDriver);\n  }\n}\nfunction DriversManagementComponent_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 114);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"show\", ctx_r3.showDriverForm || ctx_r3.showDeleteConfirm);\n  }\n}\nexport class DriversManagementComponent {\n  constructor(driverService, deliveryService, realTimeService) {\n    this.driverService = driverService;\n    this.deliveryService = deliveryService;\n    this.realTimeService = realTimeService;\n    this.drivers = [];\n    this.filteredDrivers = [];\n    this.selectedDriver = null;\n    this.driverPerformance = null;\n    this.driverDeliveries = [];\n    this.statusFilter = 'all';\n    this.searchTerm = '';\n    this.loading = true;\n    this.performanceLoading = false;\n    this.deliveriesLoading = false;\n    this.error = '';\n    // Modal states\n    this.showDriverForm = false;\n    this.showDeleteConfirm = false;\n    this.editingDriver = null;\n    this.subscriptions = [];\n  }\n  ngOnInit() {\n    this.loadDrivers();\n    this.setupRealTimeUpdates();\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n  loadDrivers() {\n    this.loading = true;\n    this.subscriptions.push(this.driverService.getDrivers().subscribe({\n      next: drivers => {\n        this.drivers = drivers;\n        this.applyFilters();\n        this.loading = false;\n      },\n      error: err => {\n        console.error('Error loading drivers', err);\n        this.error = 'Erreur lors du chargement des livreurs';\n        this.loading = false;\n      }\n    }));\n  }\n  setupRealTimeUpdates() {\n    // Start SignalR connection\n    this.realTimeService.startConnection().then(() => {\n      console.log('Connected to real-time hub');\n      this.realTimeService.joinAdminGroup();\n      // Subscribe to driver updates\n      this.subscriptions.push(this.realTimeService.driverUpdates$.subscribe(driver => {\n        if (driver) {\n          this.updateDriver(driver);\n        }\n      }));\n    }).catch(err => {\n      console.error('Error connecting to real-time hub', err);\n    });\n  }\n  updateDriver(updatedDriver) {\n    const index = this.drivers.findIndex(d => d.id === updatedDriver.id);\n    if (index !== -1) {\n      this.drivers[index] = updatedDriver;\n    } else {\n      this.drivers.push(updatedDriver);\n    }\n    this.applyFilters();\n    // Update selected driver if it's the one that was updated\n    if (this.selectedDriver && this.selectedDriver.id === updatedDriver.id) {\n      this.selectedDriver = updatedDriver;\n    }\n  }\n  applyFilters() {\n    let filtered = [...this.drivers];\n    // Apply status filter\n    if (this.statusFilter !== 'all') {\n      filtered = filtered.filter(d => d.status === parseInt(this.statusFilter, 10));\n    }\n    // Apply search filter\n    if (this.searchTerm.trim() !== '') {\n      const search = this.searchTerm.toLowerCase();\n      filtered = filtered.filter(d => d.name.toLowerCase().includes(search) || d.email.toLowerCase().includes(search) || d.phone.toLowerCase().includes(search) || d.vehicleType.toLowerCase().includes(search));\n    }\n    this.filteredDrivers = filtered;\n  }\n  selectDriver(driver) {\n    this.selectedDriver = driver;\n    this.loadDriverPerformance(driver.id);\n    this.loadDriverDeliveries(driver.id);\n  }\n  loadDriverPerformance(driverId) {\n    this.performanceLoading = true;\n    this.subscriptions.push(this.driverService.getDriverPerformance(driverId).subscribe({\n      next: performance => {\n        this.driverPerformance = performance;\n        this.performanceLoading = false;\n      },\n      error: err => {\n        console.error('Error loading driver performance', err);\n        this.performanceLoading = false;\n      }\n    }));\n  }\n  loadDriverDeliveries(driverId) {\n    this.deliveriesLoading = true;\n    this.subscriptions.push(this.deliveryService.getDeliveriesByDriverId(driverId).subscribe({\n      next: deliveries => {\n        this.driverDeliveries = deliveries;\n        this.deliveriesLoading = false;\n      },\n      error: err => {\n        console.error('Error loading driver deliveries', err);\n        this.deliveriesLoading = false;\n      }\n    }));\n  }\n  getStatusClass(status) {\n    switch (status) {\n      case DriverStatus.Available:\n        return 'success';\n      case DriverStatus.Busy:\n        return 'info';\n      case DriverStatus.OnBreak:\n        return 'warning';\n      case DriverStatus.Offline:\n        return 'default';\n      default:\n        return 'default';\n    }\n  }\n  getStatusText(status) {\n    switch (status) {\n      case DriverStatus.Available:\n        return 'Disponible';\n      case DriverStatus.Busy:\n        return 'Occupé';\n      case DriverStatus.OnBreak:\n        return 'En pause';\n      case DriverStatus.Offline:\n        return 'Hors ligne';\n      default:\n        return status;\n    }\n  }\n  getDeliveryStatusClass(status) {\n    switch (status) {\n      case DeliveryStatus.Delivered:\n        return 'success';\n      case DeliveryStatus.InTransit:\n        return 'info';\n      case DeliveryStatus.Delayed:\n        return 'danger';\n      case DeliveryStatus.Pending:\n        return 'warning';\n      case DeliveryStatus.Cancelled:\n        return 'default';\n      default:\n        return 'default';\n    }\n  }\n  getDeliveryStatusText(status) {\n    switch (status) {\n      case DeliveryStatus.Delivered:\n        return 'Livré';\n      case DeliveryStatus.InTransit:\n        return 'En cours';\n      case DeliveryStatus.Delayed:\n        return 'Retardé';\n      case DeliveryStatus.Pending:\n        return 'En attente';\n      case DeliveryStatus.Cancelled:\n        return 'Annulé';\n      default:\n        return String(status);\n    }\n  }\n  formatDate(date) {\n    return new Date(date).toLocaleString();\n  }\n  getDriverMapCenter() {\n    if (!this.selectedDriver) return undefined;\n    return [this.selectedDriver.currentLocation.latitude, this.selectedDriver.currentLocation.longitude];\n  }\n  // CRUD Operations\n  onNewDriver() {\n    this.editingDriver = null;\n    this.showDriverForm = true;\n  }\n  onEditDriver(driver) {\n    this.editingDriver = driver;\n    this.showDriverForm = true;\n  }\n  onDeleteDriver(driver) {\n    this.selectedDriver = driver;\n    this.showDeleteConfirm = true;\n  }\n  onDriverFormSubmit(driverData) {\n    if (this.editingDriver) {\n      // Update existing driver\n      const updatedDriver = {\n        ...this.editingDriver,\n        ...driverData\n      };\n      this.subscriptions.push(this.driverService.updateDriver(updatedDriver).subscribe({\n        next: () => {\n          this.loadDrivers();\n          this.showDriverForm = false;\n          this.editingDriver = null;\n        },\n        error: err => {\n          console.error('Error updating driver', err);\n          this.error = 'Erreur lors de la mise à jour du livreur';\n        }\n      }));\n    } else {\n      // Create new driver - send the form data directly as CreateDriverRequest\n      this.subscriptions.push(this.driverService.createDriver(driverData).subscribe({\n        next: () => {\n          this.loadDrivers();\n          this.showDriverForm = false;\n          this.error = ''; // Clear any previous errors\n        },\n\n        error: err => {\n          console.error('Error creating driver', err);\n          // Show detailed error message\n          if (err.error && err.error.errors) {\n            // Model validation errors\n            const errorMessages = [];\n            for (const field in err.error.errors) {\n              errorMessages.push(`${field}: ${err.error.errors[field].join(', ')}`);\n            }\n            this.error = `Erreurs de validation: ${errorMessages.join('; ')}`;\n          } else if (err.error && err.error.title) {\n            this.error = err.error.title;\n          } else {\n            this.error = 'Erreur lors de la création du livreur';\n          }\n        }\n      }));\n    }\n  }\n  onDriverFormCancel() {\n    this.showDriverForm = false;\n    this.editingDriver = null;\n  }\n  confirmDelete() {\n    if (this.selectedDriver) {\n      this.subscriptions.push(this.driverService.deleteDriver(this.selectedDriver.id).subscribe({\n        next: () => {\n          this.loadDrivers();\n          this.selectedDriver = null;\n          this.showDeleteConfirm = false;\n        },\n        error: err => {\n          console.error('Error deleting driver', err);\n          this.error = 'Erreur lors de la suppression du livreur';\n        }\n      }));\n    }\n  }\n  cancelDelete() {\n    this.showDeleteConfirm = false;\n    this.selectedDriver = null;\n  }\n  onExport() {\n    // TODO: Implement export functionality\n    alert('Fonctionnalité d\\'export à venir!');\n  }\n  static {\n    this.ɵfac = function DriversManagementComponent_Factory(t) {\n      return new (t || DriversManagementComponent)(i0.ɵɵdirectiveInject(i1.DriverService), i0.ɵɵdirectiveInject(i2.DeliveryService), i0.ɵɵdirectiveInject(i3.RealTimeService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DriversManagementComponent,\n      selectors: [[\"app-drivers-management\"]],\n      decls: 45,\n      vars: 15,\n      consts: [[1, \"drivers-container\"], [1, \"drivers-content\"], [\"title\", \"Gestion des livreurs\", \"subtitle\", \"G\\u00E9rez et suivez les performances de vos livreurs\"], [1, \"d-flex\", \"gap-2\"], [1, \"btn\", \"btn-primary\", 3, \"click\"], [1, \"fa-solid\", \"fa-plus\", \"me-2\"], [1, \"btn\", \"btn-outline-secondary\", 3, \"click\"], [1, \"fa-solid\", \"fa-file-export\", \"me-2\"], [1, \"drivers-body\", \"p-4\"], [\"class\", \"text-center py-5\", 4, \"ngIf\"], [\"class\", \"alert alert-danger\", 4, \"ngIf\"], [4, \"ngIf\"], [\"tabindex\", \"-1\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\", \"modal-lg\"], [1, \"modal-content\"], [1, \"modal-header\"], [1, \"modal-title\"], [\"type\", \"button\", 1, \"btn-close\", 3, \"click\"], [1, \"modal-body\"], [3, \"driver\", \"submitted\", \"cancelled\"], [1, \"modal-dialog\"], [1, \"text-muted\"], [1, \"modal-footer\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-danger\", 3, \"click\"], [\"class\", \"modal-backdrop fade\", 3, \"show\", 4, \"ngIf\"], [1, \"text-center\", \"py-5\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"mt-2\"], [1, \"alert\", \"alert-danger\"], [1, \"row\", \"g-4\"], [1, \"col-lg-4\"], [1, \"card\", \"mb-4\"], [1, \"card-body\"], [1, \"row\", \"g-3\"], [1, \"col-md-6\"], [\"for\", \"statusFilter\", 1, \"form-label\"], [\"id\", \"statusFilter\", 1, \"form-select\", 3, \"ngModel\", \"ngModelChange\", \"change\"], [\"value\", \"all\"], [\"value\", \"Active\"], [\"value\", \"Inactive\"], [\"value\", \"OnBreak\"], [\"value\", \"Offline\"], [\"for\", \"searchFilter\", 1, \"form-label\"], [1, \"input-group\"], [\"type\", \"text\", \"id\", \"searchFilter\", \"placeholder\", \"Rechercher...\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\", \"input\"], [1, \"input-group-text\"], [1, \"fa-solid\", \"fa-search\"], [1, \"card\"], [1, \"card-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"card-title\", \"mb-0\"], [1, \"badge\", \"bg-primary\"], [1, \"card-body\", \"p-0\"], [1, \"list-group\", \"list-group-flush\", \"drivers-list\"], [\"class\", \"list-group-item list-group-item-action p-3\", 3, \"active\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"p-4 text-center text-muted\", 4, \"ngIf\"], [1, \"col-lg-8\"], [\"class\", \"row g-4\", 4, \"ngIf\"], [\"class\", \"card\", 4, \"ngIf\"], [1, \"list-group-item\", \"list-group-item-action\", \"p-3\", 3, \"click\"], [1, \"d-flex\", \"align-items-center\"], [1, \"avatar\", \"me-3\"], [1, \"flex-grow-1\"], [1, \"mb-1\"], [1, \"text-muted\", \"small\", \"mb-0\"], [1, \"text-end\"], [3, \"status\", \"variant\"], [1, \"text-muted\", \"small\", \"mt-1\"], [1, \"p-4\", \"text-center\", \"text-muted\"], [1, \"fa-solid\", \"fa-users\", \"fa-2x\", \"mb-3\"], [1, \"col-12\"], [1, \"d-flex\"], [1, \"avatar-lg\", \"me-4\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\", \"mb-2\"], [1, \"mb-0\"], [1, \"row\"], [1, \"fa-solid\", \"fa-envelope\", \"text-muted\", \"me-2\"], [1, \"fa-solid\", \"fa-phone\", \"text-muted\", \"me-2\"], [1, \"fa-solid\", \"fa-car\", \"text-muted\", \"me-2\"], [\"class\", \"text-muted ms-1\", 4, \"ngIf\"], [1, \"fa-solid\", \"fa-calendar\", \"text-muted\", \"me-2\"], [1, \"fa-solid\", \"fa-id-card\", \"text-muted\", \"me-2\"], [1, \"fa-solid\", \"fa-clock\", \"text-muted\", \"me-2\"], [1, \"card\", \"h-100\"], [1, \"card-header\"], [1, \"fa-solid\", \"fa-chart-line\", \"me-2\", \"text-primary\"], [\"class\", \"text-center py-4\", 4, \"ngIf\"], [\"class\", \"row g-3\", 4, \"ngIf\"], [\"class\", \"text-center py-4 text-muted\", 4, \"ngIf\"], [1, \"fa-solid\", \"fa-location-dot\", \"me-2\", \"text-primary\"], [3, \"drivers\", \"height\", \"center\", \"zoom\"], [1, \"fa-solid\", \"fa-box\", \"me-2\", \"text-primary\"], [\"class\", \"table-responsive\", 4, \"ngIf\"], [1, \"fa-solid\", \"fa-pen-to-square\", \"me-2\"], [1, \"btn\", \"btn-success\"], [1, \"fa-solid\", \"fa-route\", \"me-2\"], [1, \"btn\", \"btn-outline-danger\", 3, \"click\"], [1, \"fa-solid\", \"fa-trash\", \"me-2\"], [1, \"text-muted\", \"ms-1\"], [1, \"text-center\", \"py-4\"], [\"role\", \"status\", 1, \"spinner-border\", \"spinner-border-sm\", \"text-primary\"], [1, \"col-6\"], [1, \"metric-mini-card\"], [1, \"metric-mini-title\"], [1, \"metric-mini-value\"], [1, \"text-center\", \"py-4\", \"text-muted\"], [1, \"fa-solid\", \"fa-chart-simple\", \"fa-2x\", \"mb-3\"], [1, \"table-responsive\"], [1, \"table\", \"table-hover\", \"mb-0\"], [4, \"ngFor\", \"ngForOf\"], [1, \"fa-solid\", \"fa-box-open\", \"fa-2x\", \"mb-3\"], [1, \"card-body\", \"text-center\", \"p-5\"], [1, \"fa-solid\", \"fa-user\", \"fa-3x\", \"text-muted\", \"mb-3\"], [1, \"modal-backdrop\", \"fade\"]],\n      template: function DriversManagementComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"app-sidebar\");\n          i0.ɵɵelementStart(2, \"div\", 1)(3, \"app-header\", 2)(4, \"div\", 3)(5, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function DriversManagementComponent_Template_button_click_5_listener() {\n            return ctx.onNewDriver();\n          });\n          i0.ɵɵelement(6, \"i\", 5);\n          i0.ɵɵtext(7, \" Nouveau livreur \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function DriversManagementComponent_Template_button_click_8_listener() {\n            return ctx.onExport();\n          });\n          i0.ɵɵelement(9, \"i\", 7);\n          i0.ɵɵtext(10, \" Exporter \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(11, \"div\", 8);\n          i0.ɵɵtemplate(12, DriversManagementComponent_div_12_Template, 6, 0, \"div\", 9);\n          i0.ɵɵtemplate(13, DriversManagementComponent_div_13_Template, 2, 1, \"div\", 10);\n          i0.ɵɵtemplate(14, DriversManagementComponent_ng_container_14_Template, 40, 7, \"ng-container\", 11);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(15, \"div\", 12)(16, \"div\", 13)(17, \"div\", 14)(18, \"div\", 15)(19, \"h5\", 16);\n          i0.ɵɵtext(20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function DriversManagementComponent_Template_button_click_21_listener() {\n            return ctx.onDriverFormCancel();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(22, \"div\", 18)(23, \"app-driver-form\", 19);\n          i0.ɵɵlistener(\"submitted\", function DriversManagementComponent_Template_app_driver_form_submitted_23_listener($event) {\n            return ctx.onDriverFormSubmit($event);\n          })(\"cancelled\", function DriversManagementComponent_Template_app_driver_form_cancelled_23_listener() {\n            return ctx.onDriverFormCancel();\n          });\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(24, \"div\", 12)(25, \"div\", 20)(26, \"div\", 14)(27, \"div\", 15)(28, \"h5\", 16);\n          i0.ɵɵtext(29, \"Confirmer la suppression\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function DriversManagementComponent_Template_button_click_30_listener() {\n            return ctx.cancelDelete();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(31, \"div\", 18)(32, \"p\");\n          i0.ɵɵtext(33, \"\\u00CAtes-vous s\\u00FBr de vouloir supprimer le livreur \");\n          i0.ɵɵelementStart(34, \"strong\");\n          i0.ɵɵtext(35);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(36, \" ?\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"p\", 21);\n          i0.ɵɵtext(38, \"Cette action est irr\\u00E9versible.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(39, \"div\", 22)(40, \"button\", 23);\n          i0.ɵɵlistener(\"click\", function DriversManagementComponent_Template_button_click_40_listener() {\n            return ctx.cancelDelete();\n          });\n          i0.ɵɵtext(41, \"Annuler\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"button\", 24);\n          i0.ɵɵlistener(\"click\", function DriversManagementComponent_Template_button_click_42_listener() {\n            return ctx.confirmDelete();\n          });\n          i0.ɵɵtext(43, \"Supprimer\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵtemplate(44, DriversManagementComponent_div_44_Template, 1, 2, \"div\", 25);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵstyleProp(\"display\", ctx.showDriverForm ? \"block\" : \"none\");\n          i0.ɵɵclassProp(\"show\", ctx.showDriverForm);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" \", ctx.editingDriver ? \"Modifier le livreur\" : \"Nouveau livreur\", \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"driver\", ctx.editingDriver);\n          i0.ɵɵadvance(1);\n          i0.ɵɵstyleProp(\"display\", ctx.showDeleteConfirm ? \"block\" : \"none\");\n          i0.ɵɵclassProp(\"show\", ctx.showDeleteConfirm);\n          i0.ɵɵadvance(11);\n          i0.ɵɵtextInterpolate(ctx.selectedDriver == null ? null : ctx.selectedDriver.name);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.showDriverForm || ctx.showDeleteConfirm);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i5.NgSelectOption, i5.ɵNgSelectMultipleOption, i5.DefaultValueAccessor, i5.SelectControlValueAccessor, i5.NgControlStatus, i5.NgModel, i6.SidebarComponent, i7.HeaderComponent, i8.StatusBadgeComponent, i9.MapViewComponent, i10.DriverFormComponent, i4.DecimalPipe, i4.DatePipe],\n      styles: [\".drivers-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  height: 100vh;\\n  overflow: hidden;\\n}\\n\\n.drivers-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n}\\n\\n.drivers-body[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  background-color: var(--gray-50);\\n}\\n\\n.drivers-list[_ngcontent-%COMP%] {\\n  max-height: 600px;\\n  overflow-y: auto;\\n}\\n\\n.list-group-item.active[_ngcontent-%COMP%] {\\n  background-color: var(--primary-light);\\n  color: var(--primary-dark);\\n  border-color: var(--primary-light);\\n}\\n\\n.list-group-item.active[_ngcontent-%COMP%]   .text-muted[_ngcontent-%COMP%] {\\n  color: var(--primary-dark) !important;\\n  opacity: 0.8;\\n}\\n\\n.avatar[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  background-color: var(--primary-light);\\n  color: var(--primary-dark);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-weight: 600;\\n}\\n\\n.avatar-lg[_ngcontent-%COMP%] {\\n  width: 64px;\\n  height: 64px;\\n  border-radius: 50%;\\n  background-color: var(--primary-light);\\n  color: var(--primary-dark);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-weight: 600;\\n  font-size: 1.5rem;\\n}\\n\\n.metric-mini-card[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-radius: 0.5rem;\\n  padding: 1rem;\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);\\n  border: 1px solid var(--gray-200);\\n}\\n\\n.metric-mini-title[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: var(--gray-500);\\n  margin-bottom: 0.5rem;\\n}\\n\\n.metric-mini-value[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: var(--gray-800);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["DriverStatus", "DeliveryStatus", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "error", "ɵɵlistener", "DriversManagementComponent_ng_container_14_button_35_Template_button_click_0_listener", "restoredCtx", "ɵɵrestoreView", "_r10", "driver_r8", "$implicit", "ctx_r9", "ɵɵnextContext", "ɵɵresetView", "selectDriver", "ɵɵelement", "ɵɵclassProp", "ctx_r4", "selectedDriver", "id", "name", "char<PERSON>t", "ɵɵtextInterpolate", "vehicleType", "ɵɵproperty", "getStatusText", "status", "getStatusClass", "todayDeliveries", "ctx_r11", "vehicleId", "ctx_r13", "driverPerformance", "totalDeliveries", "ɵɵpipeBind2", "onTimeRate", "avgDeliveryTime", "customerSatisfaction", "avgSpeed", "deliveriesPerHour", "delivery_r19", "orderId", "customerName", "address", "ctx_r18", "getDeliveryStatusText", "getDeliveryStatusClass", "estimatedDeliveryTime", "actualDeliveryTime", "ɵɵtemplate", "DriversManagementComponent_ng_container_14_div_38_div_62_tr_17_Template", "ctx_r16", "driverDeliveries", "DriversManagementComponent_ng_container_14_div_38_span_23_Template", "DriversManagementComponent_ng_container_14_div_38_div_41_Template", "DriversManagementComponent_ng_container_14_div_38_div_42_Template", "DriversManagementComponent_ng_container_14_div_38_div_43_Template", "DriversManagementComponent_ng_container_14_div_38_div_61_Template", "DriversManagementComponent_ng_container_14_div_38_div_62_Template", "DriversManagementComponent_ng_container_14_div_38_div_63_Template", "DriversManagementComponent_ng_container_14_div_38_Template_button_click_66_listener", "_r21", "ctx_r20", "onEditDriver", "DriversManagementComponent_ng_container_14_div_38_Template_button_click_72_listener", "ctx_r22", "onDeleteDriver", "ctx_r6", "email", "phone", "formatDate", "hireDate", "licenseNumber", "lastActive", "performanceLoading", "ɵɵpureFunction1", "_c0", "getDriverMapCenter", "length", "deliveriesLoading", "ɵɵelementContainerStart", "DriversManagementComponent_ng_container_14_Template_select_ngModelChange_9_listener", "$event", "_r24", "ctx_r23", "statusFilter", "DriversManagementComponent_ng_container_14_Template_select_change_9_listener", "ctx_r25", "applyFilters", "DriversManagementComponent_ng_container_14_Template_input_ngModelChange_24_listener", "ctx_r26", "searchTerm", "DriversManagementComponent_ng_container_14_Template_input_input_24_listener", "ctx_r27", "DriversManagementComponent_ng_container_14_button_35_Template", "DriversManagementComponent_ng_container_14_div_36_Template", "DriversManagementComponent_ng_container_14_div_38_Template", "DriversManagementComponent_ng_container_14_div_39_Template", "ɵɵelementContainerEnd", "ctx_r2", "filteredDrivers", "ctx_r3", "showDriverForm", "showDeleteConfirm", "DriversManagementComponent", "constructor", "driverService", "deliveryService", "realTimeService", "drivers", "loading", "editingDriver", "subscriptions", "ngOnInit", "loadDrivers", "setupRealTimeUpdates", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "push", "getDrivers", "subscribe", "next", "err", "console", "startConnection", "then", "log", "joinAdminGroup", "driverUpdates$", "driver", "updateDriver", "catch", "updatedDriver", "index", "findIndex", "d", "filtered", "filter", "parseInt", "trim", "search", "toLowerCase", "includes", "loadDriverPerformance", "loadDriverDeliveries", "driverId", "getDriverPerformance", "performance", "getDeliveriesByDriverId", "deliveries", "Available", "Busy", "OnBreak", "Offline", "Delivered", "InTransit", "Delayed", "Pending", "Cancelled", "String", "date", "Date", "toLocaleString", "undefined", "currentLocation", "latitude", "longitude", "onNewDriver", "onDriverFormSubmit", "driverData", "createDriver", "errors", "errorMessages", "field", "join", "title", "onDriverFormCancel", "confirmDelete", "deleteDriver", "cancelDelete", "onExport", "alert", "ɵɵdirectiveInject", "i1", "DriverService", "i2", "DeliveryService", "i3", "RealTimeService", "selectors", "decls", "vars", "consts", "template", "DriversManagementComponent_Template", "rf", "ctx", "DriversManagementComponent_Template_button_click_5_listener", "DriversManagementComponent_Template_button_click_8_listener", "DriversManagementComponent_div_12_Template", "DriversManagementComponent_div_13_Template", "DriversManagementComponent_ng_container_14_Template", "DriversManagementComponent_Template_button_click_21_listener", "DriversManagementComponent_Template_app_driver_form_submitted_23_listener", "DriversManagementComponent_Template_app_driver_form_cancelled_23_listener", "DriversManagementComponent_Template_button_click_30_listener", "DriversManagementComponent_Template_button_click_40_listener", "DriversManagementComponent_Template_button_click_42_listener", "DriversManagementComponent_div_44_Template", "ɵɵstyleProp"], "sources": ["C:\\Users\\<USER>\\delivery-dash-optimiser\\frontend\\src\\app\\pages\\drivers-management\\drivers-management.component.ts", "C:\\Users\\<USER>\\delivery-dash-optimiser\\frontend\\src\\app\\pages\\drivers-management\\drivers-management.component.html"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { Subscription } from 'rxjs';\nimport { DriverService } from '../../core/services/driver.service';\nimport { DeliveryService } from '../../core/services/delivery.service';\nimport { RealTimeService } from '../../core/services/real-time.service';\nimport { Driver, DriverStatus, DriverPerformance } from '../../core/models/driver.model';\nimport { Delivery, DeliveryStatus } from '../../core/models/delivery.model';\n\n@Component({\n  selector: 'app-drivers-management',\n  templateUrl: './drivers-management.component.html',\n  styleUrls: ['./drivers-management.component.scss']\n})\nexport class DriversManagementComponent implements OnInit, OnDestroy {\n  drivers: Driver[] = [];\n  filteredDrivers: Driver[] = [];\n  selectedDriver: Driver | null = null;\n  driverPerformance: DriverPerformance | null = null;\n  driverDeliveries: Delivery[] = [];\n\n  statusFilter: string = 'all';\n  searchTerm: string = '';\n\n  loading = true;\n  performanceLoading = false;\n  deliveriesLoading = false;\n  error = '';\n\n  // Modal states\n  showDriverForm = false;\n  showDeleteConfirm = false;\n  editingDriver: Driver | null = null;\n\n  private subscriptions: Subscription[] = [];\n\n  constructor(\n    private driverService: DriverService,\n    private deliveryService: DeliveryService,\n    private realTimeService: RealTimeService\n  ) { }\n\n  ngOnInit(): void {\n    this.loadDrivers();\n    this.setupRealTimeUpdates();\n  }\n\n  ngOnDestroy(): void {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n\n  private loadDrivers(): void {\n    this.loading = true;\n\n    this.subscriptions.push(\n      this.driverService.getDrivers().subscribe({\n        next: (drivers) => {\n          this.drivers = drivers;\n          this.applyFilters();\n          this.loading = false;\n        },\n        error: (err) => {\n          console.error('Error loading drivers', err);\n          this.error = 'Erreur lors du chargement des livreurs';\n          this.loading = false;\n        }\n      })\n    );\n  }\n\n  private setupRealTimeUpdates(): void {\n    // Start SignalR connection\n    this.realTimeService.startConnection()\n      .then(() => {\n        console.log('Connected to real-time hub');\n        this.realTimeService.joinAdminGroup();\n\n        // Subscribe to driver updates\n        this.subscriptions.push(\n          this.realTimeService.driverUpdates$.subscribe(driver => {\n            if (driver) {\n              this.updateDriver(driver);\n            }\n          })\n        );\n      })\n      .catch(err => {\n        console.error('Error connecting to real-time hub', err);\n      });\n  }\n\n  private updateDriver(updatedDriver: Driver): void {\n    const index = this.drivers.findIndex(d => d.id === updatedDriver.id);\n\n    if (index !== -1) {\n      this.drivers[index] = updatedDriver;\n    } else {\n      this.drivers.push(updatedDriver);\n    }\n\n    this.applyFilters();\n\n    // Update selected driver if it's the one that was updated\n    if (this.selectedDriver && this.selectedDriver.id === updatedDriver.id) {\n      this.selectedDriver = updatedDriver;\n    }\n  }\n\n  applyFilters(): void {\n    let filtered = [...this.drivers];\n\n    // Apply status filter\n    if (this.statusFilter !== 'all') {\n      filtered = filtered.filter(d => d.status === parseInt(this.statusFilter, 10));\n    }\n\n    // Apply search filter\n    if (this.searchTerm.trim() !== '') {\n      const search = this.searchTerm.toLowerCase();\n      filtered = filtered.filter(d =>\n        d.name.toLowerCase().includes(search) ||\n        d.email.toLowerCase().includes(search) ||\n        d.phone.toLowerCase().includes(search) ||\n        d.vehicleType.toLowerCase().includes(search)\n      );\n    }\n\n    this.filteredDrivers = filtered;\n  }\n\n  selectDriver(driver: Driver): void {\n    this.selectedDriver = driver;\n    this.loadDriverPerformance(driver.id);\n    this.loadDriverDeliveries(driver.id);\n  }\n\n  loadDriverPerformance(driverId: string): void {\n    this.performanceLoading = true;\n\n    this.subscriptions.push(\n      this.driverService.getDriverPerformance(driverId).subscribe({\n        next: (performance) => {\n          this.driverPerformance = performance;\n          this.performanceLoading = false;\n        },\n        error: (err) => {\n          console.error('Error loading driver performance', err);\n          this.performanceLoading = false;\n        }\n      })\n    );\n  }\n\n  loadDriverDeliveries(driverId: string): void {\n    this.deliveriesLoading = true;\n\n    this.subscriptions.push(\n      this.deliveryService.getDeliveriesByDriverId(driverId).subscribe({\n        next: (deliveries) => {\n          this.driverDeliveries = deliveries;\n          this.deliveriesLoading = false;\n        },\n        error: (err) => {\n          console.error('Error loading driver deliveries', err);\n          this.deliveriesLoading = false;\n        }\n      })\n    );\n  }\n\n  getStatusClass(status: DriverStatus): 'success' | 'danger' | 'warning' | 'info' | 'default' {\n    switch (status) {\n      case DriverStatus.Available:\n        return 'success';\n      case DriverStatus.Busy:\n        return 'info';\n      case DriverStatus.OnBreak:\n        return 'warning';\n      case DriverStatus.Offline:\n        return 'default';\n      default:\n        return 'default';\n    }\n  }\n\n  getStatusText(status: DriverStatus): string {\n    switch (status) {\n      case DriverStatus.Available:\n        return 'Disponible';\n      case DriverStatus.Busy:\n        return 'Occupé';\n      case DriverStatus.OnBreak:\n        return 'En pause';\n      case DriverStatus.Offline:\n        return 'Hors ligne';\n      default:\n        return status;\n    }\n  }\n\n  getDeliveryStatusClass(status: DeliveryStatus): 'success' | 'danger' | 'warning' | 'info' | 'default' {\n    switch (status) {\n      case DeliveryStatus.Delivered:\n        return 'success';\n      case DeliveryStatus.InTransit:\n        return 'info';\n      case DeliveryStatus.Delayed:\n        return 'danger';\n      case DeliveryStatus.Pending:\n        return 'warning';\n      case DeliveryStatus.Cancelled:\n        return 'default';\n      default:\n        return 'default';\n    }\n  }\n\n  getDeliveryStatusText(status: DeliveryStatus): string {\n    switch (status) {\n      case DeliveryStatus.Delivered:\n        return 'Livré';\n      case DeliveryStatus.InTransit:\n        return 'En cours';\n      case DeliveryStatus.Delayed:\n        return 'Retardé';\n      case DeliveryStatus.Pending:\n        return 'En attente';\n      case DeliveryStatus.Cancelled:\n        return 'Annulé';\n      default:\n        return String(status);\n    }\n  }\n\n  formatDate(date: Date | string): string {\n    return new Date(date).toLocaleString();\n  }\n\n  getDriverMapCenter(): [number, number] | undefined {\n    if (!this.selectedDriver) return undefined;\n    return [this.selectedDriver.currentLocation.latitude, this.selectedDriver.currentLocation.longitude];\n  }\n\n  // CRUD Operations\n  onNewDriver(): void {\n    this.editingDriver = null;\n    this.showDriverForm = true;\n  }\n\n  onEditDriver(driver: Driver): void {\n    this.editingDriver = driver;\n    this.showDriverForm = true;\n  }\n\n  onDeleteDriver(driver: Driver): void {\n    this.selectedDriver = driver;\n    this.showDeleteConfirm = true;\n  }\n\n  onDriverFormSubmit(driverData: any): void {\n    if (this.editingDriver) {\n      // Update existing driver\n      const updatedDriver: Driver = {\n        ...this.editingDriver,\n        ...driverData\n      };\n\n      this.subscriptions.push(\n        this.driverService.updateDriver(updatedDriver).subscribe({\n          next: () => {\n            this.loadDrivers();\n            this.showDriverForm = false;\n            this.editingDriver = null;\n          },\n          error: (err) => {\n            console.error('Error updating driver', err);\n            this.error = 'Erreur lors de la mise à jour du livreur';\n          }\n        })\n      );\n    } else {\n      // Create new driver - send the form data directly as CreateDriverRequest\n      this.subscriptions.push(\n        this.driverService.createDriver(driverData).subscribe({\n          next: () => {\n            this.loadDrivers();\n            this.showDriverForm = false;\n            this.error = ''; // Clear any previous errors\n          },\n          error: (err) => {\n            console.error('Error creating driver', err);\n\n            // Show detailed error message\n            if (err.error && err.error.errors) {\n              // Model validation errors\n              const errorMessages = [];\n              for (const field in err.error.errors) {\n                errorMessages.push(`${field}: ${err.error.errors[field].join(', ')}`);\n              }\n              this.error = `Erreurs de validation: ${errorMessages.join('; ')}`;\n            } else if (err.error && err.error.title) {\n              this.error = err.error.title;\n            } else {\n              this.error = 'Erreur lors de la création du livreur';\n            }\n          }\n        })\n      );\n    }\n  }\n\n  onDriverFormCancel(): void {\n    this.showDriverForm = false;\n    this.editingDriver = null;\n  }\n\n  confirmDelete(): void {\n    if (this.selectedDriver) {\n      this.subscriptions.push(\n        this.driverService.deleteDriver(this.selectedDriver.id).subscribe({\n          next: () => {\n            this.loadDrivers();\n            this.selectedDriver = null;\n            this.showDeleteConfirm = false;\n          },\n          error: (err) => {\n            console.error('Error deleting driver', err);\n            this.error = 'Erreur lors de la suppression du livreur';\n          }\n        })\n      );\n    }\n  }\n\n  cancelDelete(): void {\n    this.showDeleteConfirm = false;\n    this.selectedDriver = null;\n  }\n\n  onExport(): void {\n    // TODO: Implement export functionality\n    alert('Fonctionnalité d\\'export à venir!');\n  }\n}\n", "<div class=\"drivers-container\">\n  <app-sidebar></app-sidebar>\n\n  <div class=\"drivers-content\">\n    <app-header\n      title=\"Gestion des livreurs\"\n      subtitle=\"<PERSON><PERSON><PERSON> et suivez les performances de vos livreurs\"\n    >\n      <div class=\"d-flex gap-2\">\n        <button class=\"btn btn-primary\" (click)=\"onNewDriver()\">\n          <i class=\"fa-solid fa-plus me-2\"></i>\n          Nouveau livreur\n        </button>\n        <button class=\"btn btn-outline-secondary\" (click)=\"onExport()\">\n          <i class=\"fa-solid fa-file-export me-2\"></i>\n          Exporter\n        </button>\n      </div>\n    </app-header>\n\n    <div class=\"drivers-body p-4\">\n      <div *ngIf=\"loading\" class=\"text-center py-5\">\n        <div class=\"spinner-border text-primary\" role=\"status\">\n          <span class=\"visually-hidden\">Chargement...</span>\n        </div>\n        <p class=\"mt-2\">Chargement des données...</p>\n      </div>\n\n      <div *ngIf=\"error\" class=\"alert alert-danger\">\n        {{ error }}\n      </div>\n\n      <ng-container *ngIf=\"!loading && !error\">\n        <div class=\"row g-4\">\n          <!-- Filters and List -->\n          <div class=\"col-lg-4\">\n            <div class=\"card mb-4\">\n              <div class=\"card-body\">\n                <div class=\"row g-3\">\n                  <div class=\"col-md-6\">\n                    <label for=\"statusFilter\" class=\"form-label\">Statut</label>\n                    <select\n                      id=\"statusFilter\"\n                      class=\"form-select\"\n                      [(ngModel)]=\"statusFilter\"\n                      (change)=\"applyFilters()\"\n                    >\n                      <option value=\"all\">Tous les statuts</option>\n                      <option value=\"Active\">Actif</option>\n                      <option value=\"Inactive\">Inactif</option>\n                      <option value=\"OnBreak\">En pause</option>\n                      <option value=\"Offline\">Hors ligne</option>\n                    </select>\n                  </div>\n                  <div class=\"col-md-6\">\n                    <label for=\"searchFilter\" class=\"form-label\">Recherche</label>\n                    <div class=\"input-group\">\n                      <input\n                        type=\"text\"\n                        id=\"searchFilter\"\n                        class=\"form-control\"\n                        placeholder=\"Rechercher...\"\n                        [(ngModel)]=\"searchTerm\"\n                        (input)=\"applyFilters()\"\n                      >\n                      <span class=\"input-group-text\">\n                        <i class=\"fa-solid fa-search\"></i>\n                      </span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div class=\"card\">\n              <div class=\"card-header d-flex justify-content-between align-items-center\">\n                <h5 class=\"card-title mb-0\">Livreurs</h5>\n                <span class=\"badge bg-primary\">{{ filteredDrivers.length }}</span>\n              </div>\n              <div class=\"card-body p-0\">\n                <div class=\"list-group list-group-flush drivers-list\">\n                  <button\n                    *ngFor=\"let driver of filteredDrivers\"\n                    class=\"list-group-item list-group-item-action p-3\"\n                    [class.active]=\"selectedDriver?.id === driver.id\"\n                    (click)=\"selectDriver(driver)\"\n                  >\n                    <div class=\"d-flex align-items-center\">\n                      <div class=\"avatar me-3\">\n                        {{ driver.name.charAt(0) }}\n                      </div>\n                      <div class=\"flex-grow-1\">\n                        <h6 class=\"mb-1\">{{ driver.name }}</h6>\n                        <p class=\"text-muted small mb-0\">{{ driver.vehicleType }}</p>\n                      </div>\n                      <div class=\"text-end\">\n                        <app-status-badge\n                          [status]=\"getStatusText(driver.status)\"\n                          [variant]=\"getStatusClass(driver.status)\"\n                        ></app-status-badge>\n                        <p class=\"text-muted small mt-1\">\n                          {{ driver.todayDeliveries }} livraisons\n                        </p>\n                      </div>\n                    </div>\n                  </button>\n\n                  <div *ngIf=\"filteredDrivers.length === 0\" class=\"p-4 text-center text-muted\">\n                    <i class=\"fa-solid fa-users fa-2x mb-3\"></i>\n                    <p>Aucun livreur trouvé</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Driver Details -->\n          <div class=\"col-lg-8\">\n            <div *ngIf=\"selectedDriver\" class=\"row g-4\">\n              <!-- Driver Info Card -->\n              <div class=\"col-12\">\n                <div class=\"card\">\n                  <div class=\"card-body\">\n                    <div class=\"d-flex\">\n                      <div class=\"avatar-lg me-4\">\n                        {{ selectedDriver.name.charAt(0) }}\n                      </div>\n                      <div class=\"flex-grow-1\">\n                        <div class=\"d-flex justify-content-between align-items-center mb-2\">\n                          <h4 class=\"mb-0\">{{ selectedDriver.name }}</h4>\n                          <app-status-badge\n                            [status]=\"getStatusText(selectedDriver.status)\"\n                            [variant]=\"getStatusClass(selectedDriver.status)\"\n                          ></app-status-badge>\n                        </div>\n                        <div class=\"row\">\n                          <div class=\"col-md-6\">\n                            <p class=\"mb-1\">\n                              <i class=\"fa-solid fa-envelope text-muted me-2\"></i>\n                              {{ selectedDriver.email }}\n                            </p>\n                            <p class=\"mb-1\">\n                              <i class=\"fa-solid fa-phone text-muted me-2\"></i>\n                              {{ selectedDriver.phone }}\n                            </p>\n                            <p class=\"mb-0\">\n                              <i class=\"fa-solid fa-car text-muted me-2\"></i>\n                              {{ selectedDriver.vehicleType }}\n                              <span *ngIf=\"selectedDriver.vehicleId\" class=\"text-muted ms-1\">({{ selectedDriver.vehicleId }})</span>\n                            </p>\n                          </div>\n                          <div class=\"col-md-6\">\n                            <p class=\"mb-1\">\n                              <i class=\"fa-solid fa-calendar text-muted me-2\"></i>\n                              Embauché le {{ formatDate(selectedDriver.hireDate) }}\n                            </p>\n                            <p class=\"mb-1\">\n                              <i class=\"fa-solid fa-id-card text-muted me-2\"></i>\n                              Permis: {{ selectedDriver.licenseNumber || 'N/A' }}\n                            </p>\n                            <p class=\"mb-0\">\n                              <i class=\"fa-solid fa-clock text-muted me-2\"></i>\n                              Dernière activité: {{ formatDate(selectedDriver.lastActive) }}\n                            </p>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <!-- Performance Metrics -->\n              <div class=\"col-md-6\">\n                <div class=\"card h-100\">\n                  <div class=\"card-header\">\n                    <h5 class=\"card-title mb-0\">\n                      <i class=\"fa-solid fa-chart-line me-2 text-primary\"></i>\n                      Métriques de performance\n                    </h5>\n                  </div>\n                  <div class=\"card-body\">\n                    <div *ngIf=\"performanceLoading\" class=\"text-center py-4\">\n                      <div class=\"spinner-border spinner-border-sm text-primary\" role=\"status\">\n                        <span class=\"visually-hidden\">Chargement...</span>\n                      </div>\n                    </div>\n\n                    <div *ngIf=\"!performanceLoading && driverPerformance\" class=\"row g-3\">\n                      <div class=\"col-6\">\n                        <div class=\"metric-mini-card\">\n                          <div class=\"metric-mini-title\">Livraisons totales</div>\n                          <div class=\"metric-mini-value\">{{ driverPerformance.totalDeliveries }}</div>\n                        </div>\n                      </div>\n                      <div class=\"col-6\">\n                        <div class=\"metric-mini-card\">\n                          <div class=\"metric-mini-title\">Taux de ponctualité</div>\n                          <div class=\"metric-mini-value\">{{ driverPerformance.onTimeRate | number:'1.0-0' }}%</div>\n                        </div>\n                      </div>\n                      <div class=\"col-6\">\n                        <div class=\"metric-mini-card\">\n                          <div class=\"metric-mini-title\">Temps moyen</div>\n                          <div class=\"metric-mini-value\">{{ driverPerformance.avgDeliveryTime | number:'1.0-0' }} min</div>\n                        </div>\n                      </div>\n                      <div class=\"col-6\">\n                        <div class=\"metric-mini-card\">\n                          <div class=\"metric-mini-title\">Satisfaction client</div>\n                          <div class=\"metric-mini-value\">{{ driverPerformance.customerSatisfaction | number:'1.1-1' }}/5</div>\n                        </div>\n                      </div>\n                      <div class=\"col-6\">\n                        <div class=\"metric-mini-card\">\n                          <div class=\"metric-mini-title\">Vitesse moyenne</div>\n                          <div class=\"metric-mini-value\">{{ driverPerformance.avgSpeed | number:'1.0-0' }} km/h</div>\n                        </div>\n                      </div>\n                      <div class=\"col-6\">\n                        <div class=\"metric-mini-card\">\n                          <div class=\"metric-mini-title\">Livraisons par heure</div>\n                          <div class=\"metric-mini-value\">{{ driverPerformance.deliveriesPerHour | number:'1.1-1' }}</div>\n                        </div>\n                      </div>\n                    </div>\n\n                    <div *ngIf=\"!performanceLoading && !driverPerformance\" class=\"text-center py-4 text-muted\">\n                      <i class=\"fa-solid fa-chart-simple fa-2x mb-3\"></i>\n                      <p>Aucune donnée de performance disponible</p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <!-- Current Location -->\n              <div class=\"col-md-6\">\n                <div class=\"card h-100\">\n                  <div class=\"card-header\">\n                    <h5 class=\"card-title mb-0\">\n                      <i class=\"fa-solid fa-location-dot me-2 text-primary\"></i>\n                      Position actuelle\n                    </h5>\n                  </div>\n                  <div class=\"card-body p-0\">\n                    <app-map-view\n                      [drivers]=\"[selectedDriver]\"\n                      [height]=\"250\"\n                      [center]=\"getDriverMapCenter()\"\n                      [zoom]=\"14\"\n                    ></app-map-view>\n                  </div>\n                </div>\n              </div>\n\n              <!-- Recent Deliveries -->\n              <div class=\"col-12\">\n                <div class=\"card\">\n                  <div class=\"card-header d-flex justify-content-between align-items-center\">\n                    <h5 class=\"card-title mb-0\">\n                      <i class=\"fa-solid fa-box me-2 text-primary\"></i>\n                      Livraisons récentes\n                    </h5>\n                    <span class=\"badge bg-primary\">{{ driverDeliveries.length }}</span>\n                  </div>\n                  <div class=\"card-body p-0\">\n                    <div *ngIf=\"deliveriesLoading\" class=\"text-center py-4\">\n                      <div class=\"spinner-border spinner-border-sm text-primary\" role=\"status\">\n                        <span class=\"visually-hidden\">Chargement...</span>\n                      </div>\n                    </div>\n\n                    <div *ngIf=\"!deliveriesLoading && driverDeliveries.length > 0\" class=\"table-responsive\">\n                      <table class=\"table table-hover mb-0\">\n                        <thead>\n                          <tr>\n                            <th>ID</th>\n                            <th>Client</th>\n                            <th>Adresse</th>\n                            <th>Statut</th>\n                            <th>Heure estimée</th>\n                            <th>Heure réelle</th>\n                          </tr>\n                        </thead>\n                        <tbody>\n                          <tr *ngFor=\"let delivery of driverDeliveries\">\n                            <td>{{ delivery.orderId }}</td>\n                            <td>{{ delivery.customerName }}</td>\n                            <td>{{ delivery.address }}</td>\n                            <td>\n                              <app-status-badge\n                                [status]=\"getDeliveryStatusText(delivery.status)\"\n                                [variant]=\"getDeliveryStatusClass(delivery.status)\"\n                              ></app-status-badge>\n                            </td>\n                            <td>{{ delivery.estimatedDeliveryTime | date:'HH:mm' }}</td>\n                            <td>{{ delivery.actualDeliveryTime ? (delivery.actualDeliveryTime | date:'HH:mm') : '-' }}</td>\n                          </tr>\n                        </tbody>\n                      </table>\n                    </div>\n\n                    <div *ngIf=\"!deliveriesLoading && driverDeliveries.length === 0\" class=\"text-center py-4 text-muted\">\n                      <i class=\"fa-solid fa-box-open fa-2x mb-3\"></i>\n                      <p>Aucune livraison trouvée pour ce livreur</p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <!-- Actions -->\n              <div class=\"col-12\">\n                <div class=\"d-flex gap-2\">\n                  <button class=\"btn btn-primary\" (click)=\"onEditDriver(selectedDriver)\">\n                    <i class=\"fa-solid fa-pen-to-square me-2\"></i>\n                    Modifier\n                  </button>\n                  <button class=\"btn btn-success\">\n                    <i class=\"fa-solid fa-route me-2\"></i>\n                    Optimiser l'itinéraire\n                  </button>\n                  <button class=\"btn btn-outline-danger\" (click)=\"onDeleteDriver(selectedDriver)\">\n                    <i class=\"fa-solid fa-trash me-2\"></i>\n                    Supprimer\n                  </button>\n                </div>\n              </div>\n            </div>\n\n            <div *ngIf=\"!selectedDriver\" class=\"card\">\n              <div class=\"card-body text-center p-5\">\n                <i class=\"fa-solid fa-user fa-3x text-muted mb-3\"></i>\n                <h5>Aucun livreur sélectionné</h5>\n                <p class=\"text-muted\">Sélectionnez un livreur dans la liste pour voir les détails</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </ng-container>\n    </div>\n  </div>\n</div>\n\n<!-- Driver Form Modal -->\n<div class=\"modal fade\" [class.show]=\"showDriverForm\" [style.display]=\"showDriverForm ? 'block' : 'none'\" tabindex=\"-1\">\n  <div class=\"modal-dialog modal-lg\">\n    <div class=\"modal-content\">\n      <div class=\"modal-header\">\n        <h5 class=\"modal-title\">\n          {{ editingDriver ? 'Modifier le livreur' : 'Nouveau livreur' }}\n        </h5>\n        <button type=\"button\" class=\"btn-close\" (click)=\"onDriverFormCancel()\"></button>\n      </div>\n      <div class=\"modal-body\">\n        <app-driver-form\n          [driver]=\"editingDriver\"\n          (submitted)=\"onDriverFormSubmit($event)\"\n          (cancelled)=\"onDriverFormCancel()\"\n        ></app-driver-form>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Delete Confirmation Modal -->\n<div class=\"modal fade\" [class.show]=\"showDeleteConfirm\" [style.display]=\"showDeleteConfirm ? 'block' : 'none'\" tabindex=\"-1\">\n  <div class=\"modal-dialog\">\n    <div class=\"modal-content\">\n      <div class=\"modal-header\">\n        <h5 class=\"modal-title\">Confirmer la suppression</h5>\n        <button type=\"button\" class=\"btn-close\" (click)=\"cancelDelete()\"></button>\n      </div>\n      <div class=\"modal-body\">\n        <p>Êtes-vous sûr de vouloir supprimer le livreur <strong>{{ selectedDriver?.name }}</strong> ?</p>\n        <p class=\"text-muted\">Cette action est irréversible.</p>\n      </div>\n      <div class=\"modal-footer\">\n        <button type=\"button\" class=\"btn btn-secondary\" (click)=\"cancelDelete()\">Annuler</button>\n        <button type=\"button\" class=\"btn btn-danger\" (click)=\"confirmDelete()\">Supprimer</button>\n      </div>\n    </div>\n  </div>\n</div>\n\n<!-- Modal Backdrop -->\n<div class=\"modal-backdrop fade\" [class.show]=\"showDriverForm || showDeleteConfirm\" *ngIf=\"showDriverForm || showDeleteConfirm\"></div>\n"], "mappings": "AAKA,SAAiBA,YAAY,QAA2B,gCAAgC;AACxF,SAAmBC,cAAc,QAAQ,kCAAkC;;;;;;;;;;;;;;ICerEC,EAAA,CAAAC,cAAA,cAA8C;IAEZD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEpDH,EAAA,CAAAC,cAAA,YAAgB;IAAAD,EAAA,CAAAE,MAAA,qCAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAG/CH,EAAA,CAAAC,cAAA,cAA8C;IAC5CD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,KAAA,MACF;;;;;;IAmDYP,EAAA,CAAAC,cAAA,iBAKC;IADCD,EAAA,CAAAQ,UAAA,mBAAAC,sFAAA;MAAA,MAAAC,WAAA,GAAAV,EAAA,CAAAW,aAAA,CAAAC,IAAA;MAAA,MAAAC,SAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAShB,EAAA,CAAAiB,WAAA,CAAAF,MAAA,CAAAG,YAAA,CAAAL,SAAA,CAAoB;IAAA,EAAC;IAE9Bb,EAAA,CAAAC,cAAA,cAAuC;IAEnCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAyB;IACND,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,YAAiC;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAE/DH,EAAA,CAAAC,cAAA,cAAsB;IACpBD,EAAA,CAAAmB,SAAA,4BAGoB;IACpBnB,EAAA,CAAAC,cAAA,aAAiC;IAC/BD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAlBRH,EAAA,CAAAoB,WAAA,YAAAC,MAAA,CAAAC,cAAA,kBAAAD,MAAA,CAAAC,cAAA,CAAAC,EAAA,MAAAV,SAAA,CAAAU,EAAA,CAAiD;IAK7CvB,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAQ,SAAA,CAAAW,IAAA,CAAAC,MAAA,SACF;IAEmBzB,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAA0B,iBAAA,CAAAb,SAAA,CAAAW,IAAA,CAAiB;IACDxB,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAA0B,iBAAA,CAAAb,SAAA,CAAAc,WAAA,CAAwB;IAIvD3B,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAA4B,UAAA,WAAAP,MAAA,CAAAQ,aAAA,CAAAhB,SAAA,CAAAiB,MAAA,EAAuC,YAAAT,MAAA,CAAAU,cAAA,CAAAlB,SAAA,CAAAiB,MAAA;IAIvC9B,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAQ,SAAA,CAAAmB,eAAA,iBACF;;;;;IAKNhC,EAAA,CAAAC,cAAA,cAA6E;IAC3ED,EAAA,CAAAmB,SAAA,YAA4C;IAC5CnB,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,gCAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAuCjBH,EAAA,CAAAC,cAAA,eAA+D;IAAAD,EAAA,CAAAE,MAAA,GAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAvCH,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAK,kBAAA,MAAA4B,OAAA,CAAAX,cAAA,CAAAY,SAAA,MAAgC;;;;;IAkCzGlC,EAAA,CAAAC,cAAA,eAAyD;IAEvBD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAItDH,EAAA,CAAAC,cAAA,cAAsE;IAGjCD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACvDH,EAAA,CAAAC,cAAA,eAA+B;IAAAD,EAAA,CAAAE,MAAA,GAAuC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGhFH,EAAA,CAAAC,cAAA,eAAmB;IAEgBD,EAAA,CAAAE,MAAA,gCAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACxDH,EAAA,CAAAC,cAAA,gBAA+B;IAAAD,EAAA,CAAAE,MAAA,IAAoD;;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAG7FH,EAAA,CAAAC,cAAA,gBAAmB;IAEgBD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAChDH,EAAA,CAAAC,cAAA,gBAA+B;IAAAD,EAAA,CAAAE,MAAA,IAA4D;;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGrGH,EAAA,CAAAC,cAAA,gBAAmB;IAEgBD,EAAA,CAAAE,MAAA,2BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACxDH,EAAA,CAAAC,cAAA,gBAA+B;IAAAD,EAAA,CAAAE,MAAA,IAA+D;;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGxGH,EAAA,CAAAC,cAAA,gBAAmB;IAEgBD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACpDH,EAAA,CAAAC,cAAA,gBAA+B;IAAAD,EAAA,CAAAE,MAAA,IAAsD;;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAG/FH,EAAA,CAAAC,cAAA,gBAAmB;IAEgBD,EAAA,CAAAE,MAAA,4BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACzDH,EAAA,CAAAC,cAAA,gBAA+B;IAAAD,EAAA,CAAAE,MAAA,IAA0D;;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IA9BhEH,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAA0B,iBAAA,CAAAS,OAAA,CAAAC,iBAAA,CAAAC,eAAA,CAAuC;IAMvCrC,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAK,kBAAA,KAAAL,EAAA,CAAAsC,WAAA,QAAAH,OAAA,CAAAC,iBAAA,CAAAG,UAAA,gBAAoD;IAMpDvC,EAAA,CAAAI,SAAA,GAA4D;IAA5DJ,EAAA,CAAAK,kBAAA,KAAAL,EAAA,CAAAsC,WAAA,QAAAH,OAAA,CAAAC,iBAAA,CAAAI,eAAA,mBAA4D;IAM5DxC,EAAA,CAAAI,SAAA,GAA+D;IAA/DJ,EAAA,CAAAK,kBAAA,KAAAL,EAAA,CAAAsC,WAAA,SAAAH,OAAA,CAAAC,iBAAA,CAAAK,oBAAA,iBAA+D;IAM/DzC,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAAK,kBAAA,KAAAL,EAAA,CAAAsC,WAAA,SAAAH,OAAA,CAAAC,iBAAA,CAAAM,QAAA,oBAAsD;IAMtD1C,EAAA,CAAAI,SAAA,GAA0D;IAA1DJ,EAAA,CAAA0B,iBAAA,CAAA1B,EAAA,CAAAsC,WAAA,SAAAH,OAAA,CAAAC,iBAAA,CAAAO,iBAAA,WAA0D;;;;;IAK/F3C,EAAA,CAAAC,cAAA,eAA2F;IACzFD,EAAA,CAAAmB,SAAA,aAAmD;IACnDnB,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,mDAAuC;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAqChDH,EAAA,CAAAC,cAAA,eAAwD;IAEtBD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAiBhDH,EAAA,CAAAC,cAAA,SAA8C;IACxCD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/BH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/BH,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAmB,SAAA,2BAGoB;IACtBnB,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,IAAmD;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5DH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAsF;;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAV3FH,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAA0B,iBAAA,CAAAkB,YAAA,CAAAC,OAAA,CAAsB;IACtB7C,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAA0B,iBAAA,CAAAkB,YAAA,CAAAE,YAAA,CAA2B;IAC3B9C,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAA0B,iBAAA,CAAAkB,YAAA,CAAAG,OAAA,CAAsB;IAGtB/C,EAAA,CAAAI,SAAA,GAAiD;IAAjDJ,EAAA,CAAA4B,UAAA,WAAAoB,OAAA,CAAAC,qBAAA,CAAAL,YAAA,CAAAd,MAAA,EAAiD,YAAAkB,OAAA,CAAAE,sBAAA,CAAAN,YAAA,CAAAd,MAAA;IAIjD9B,EAAA,CAAAI,SAAA,GAAmD;IAAnDJ,EAAA,CAAA0B,iBAAA,CAAA1B,EAAA,CAAAsC,WAAA,QAAAM,YAAA,CAAAO,qBAAA,WAAmD;IACnDnD,EAAA,CAAAI,SAAA,GAAsF;IAAtFJ,EAAA,CAAA0B,iBAAA,CAAAkB,YAAA,CAAAQ,kBAAA,GAAApD,EAAA,CAAAsC,WAAA,SAAAM,YAAA,CAAAQ,kBAAA,iBAAsF;;;;;IAxBlGpD,EAAA,CAAAC,cAAA,eAAwF;IAI5ED,EAAA,CAAAE,MAAA,SAAE;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACXH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACfH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACfH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,0BAAa;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,yBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAGzBH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAAqD,UAAA,KAAAC,uEAAA,oBAYK;IACPtD,EAAA,CAAAG,YAAA,EAAQ;;;;IAbmBH,EAAA,CAAAI,SAAA,IAAmB;IAAnBJ,EAAA,CAAA4B,UAAA,YAAA2B,OAAA,CAAAC,gBAAA,CAAmB;;;;;IAiBlDxD,EAAA,CAAAC,cAAA,eAAqG;IACnGD,EAAA,CAAAmB,SAAA,aAA+C;IAC/CnB,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,oDAAwC;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;;;;;IA1LzDH,EAAA,CAAAC,cAAA,cAA4C;IAOhCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAyB;IAEJD,EAAA,CAAAE,MAAA,IAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/CH,EAAA,CAAAmB,SAAA,4BAGoB;IACtBnB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAiB;IAGXD,EAAA,CAAAmB,SAAA,aAAoD;IACpDnB,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,aAAgB;IACdD,EAAA,CAAAmB,SAAA,aAAiD;IACjDnB,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,aAAgB;IACdD,EAAA,CAAAmB,SAAA,aAA+C;IAC/CnB,EAAA,CAAAE,MAAA,IACA;IAAAF,EAAA,CAAAqD,UAAA,KAAAI,kEAAA,mBAAsG;IACxGzD,EAAA,CAAAG,YAAA,EAAI;IAENH,EAAA,CAAAC,cAAA,eAAsB;IAElBD,EAAA,CAAAmB,SAAA,aAAoD;IACpDnB,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,aAAgB;IACdD,EAAA,CAAAmB,SAAA,aAAmD;IACnDnB,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,aAAgB;IACdD,EAAA,CAAAmB,SAAA,aAAiD;IACjDnB,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAUlBH,EAAA,CAAAC,cAAA,eAAsB;IAIdD,EAAA,CAAAmB,SAAA,aAAwD;IACxDnB,EAAA,CAAAE,MAAA,uCACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEPH,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAqD,UAAA,KAAAK,iEAAA,kBAIM;IAEN1D,EAAA,CAAAqD,UAAA,KAAAM,iEAAA,oBAqCM;IAEN3D,EAAA,CAAAqD,UAAA,KAAAO,iEAAA,kBAGM;IACR5D,EAAA,CAAAG,YAAA,EAAM;IAKVH,EAAA,CAAAC,cAAA,eAAsB;IAIdD,EAAA,CAAAmB,SAAA,aAA0D;IAC1DnB,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEPH,EAAA,CAAAC,cAAA,eAA2B;IACzBD,EAAA,CAAAmB,SAAA,wBAKgB;IAClBnB,EAAA,CAAAG,YAAA,EAAM;IAKVH,EAAA,CAAAC,cAAA,eAAoB;IAIZD,EAAA,CAAAmB,SAAA,aAAiD;IACjDnB,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,gBAA+B;IAAAD,EAAA,CAAAE,MAAA,IAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAErEH,EAAA,CAAAC,cAAA,eAA2B;IACzBD,EAAA,CAAAqD,UAAA,KAAAQ,iEAAA,kBAIM;IAEN7D,EAAA,CAAAqD,UAAA,KAAAS,iEAAA,mBA4BM;IAEN9D,EAAA,CAAAqD,UAAA,KAAAU,iEAAA,kBAGM;IACR/D,EAAA,CAAAG,YAAA,EAAM;IAKVH,EAAA,CAAAC,cAAA,eAAoB;IAEgBD,EAAA,CAAAQ,UAAA,mBAAAwD,oFAAA;MAAAhE,EAAA,CAAAW,aAAA,CAAAsD,IAAA;MAAA,MAAAC,OAAA,GAAAlE,EAAA,CAAAgB,aAAA;MAAA,OAAShB,EAAA,CAAAiB,WAAA,CAAAiD,OAAA,CAAAC,YAAA,CAAAD,OAAA,CAAA5C,cAAA,CAA4B;IAAA,EAAC;IACpEtB,EAAA,CAAAmB,SAAA,aAA8C;IAC9CnB,EAAA,CAAAE,MAAA,kBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAgC;IAC9BD,EAAA,CAAAmB,SAAA,aAAsC;IACtCnB,EAAA,CAAAE,MAAA,qCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAgF;IAAzCD,EAAA,CAAAQ,UAAA,mBAAA4D,oFAAA;MAAApE,EAAA,CAAAW,aAAA,CAAAsD,IAAA;MAAA,MAAAI,OAAA,GAAArE,EAAA,CAAAgB,aAAA;MAAA,OAAShB,EAAA,CAAAiB,WAAA,CAAAoD,OAAA,CAAAC,cAAA,CAAAD,OAAA,CAAA/C,cAAA,CAA8B;IAAA,EAAC;IAC7EtB,EAAA,CAAAmB,SAAA,aAAsC;IACtCnB,EAAA,CAAAE,MAAA,mBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAvMHH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAkE,MAAA,CAAAjD,cAAA,CAAAE,IAAA,CAAAC,MAAA,SACF;IAGqBzB,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAA0B,iBAAA,CAAA6C,MAAA,CAAAjD,cAAA,CAAAE,IAAA,CAAyB;IAExCxB,EAAA,CAAAI,SAAA,GAA+C;IAA/CJ,EAAA,CAAA4B,UAAA,WAAA2C,MAAA,CAAA1C,aAAA,CAAA0C,MAAA,CAAAjD,cAAA,CAAAQ,MAAA,EAA+C,YAAAyC,MAAA,CAAAxC,cAAA,CAAAwC,MAAA,CAAAjD,cAAA,CAAAQ,MAAA;IAQ7C9B,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAkE,MAAA,CAAAjD,cAAA,CAAAkD,KAAA,MACF;IAGExE,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAkE,MAAA,CAAAjD,cAAA,CAAAmD,KAAA,MACF;IAGEzE,EAAA,CAAAI,SAAA,GACA;IADAJ,EAAA,CAAAK,kBAAA,MAAAkE,MAAA,CAAAjD,cAAA,CAAAK,WAAA,MACA;IAAO3B,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAA4B,UAAA,SAAA2C,MAAA,CAAAjD,cAAA,CAAAY,SAAA,CAA8B;IAMrClC,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,uBAAAkE,MAAA,CAAAG,UAAA,CAAAH,MAAA,CAAAjD,cAAA,CAAAqD,QAAA,OACF;IAGE3E,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,cAAAkE,MAAA,CAAAjD,cAAA,CAAAsD,aAAA,eACF;IAGE5E,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,mCAAAkE,MAAA,CAAAG,UAAA,CAAAH,MAAA,CAAAjD,cAAA,CAAAuD,UAAA,OACF;IAmBF7E,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAA4B,UAAA,SAAA2C,MAAA,CAAAO,kBAAA,CAAwB;IAMxB9E,EAAA,CAAAI,SAAA,GAA8C;IAA9CJ,EAAA,CAAA4B,UAAA,UAAA2C,MAAA,CAAAO,kBAAA,IAAAP,MAAA,CAAAnC,iBAAA,CAA8C;IAuC9CpC,EAAA,CAAAI,SAAA,GAA+C;IAA/CJ,EAAA,CAAA4B,UAAA,UAAA2C,MAAA,CAAAO,kBAAA,KAAAP,MAAA,CAAAnC,iBAAA,CAA+C;IAmBnDpC,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAA4B,UAAA,YAAA5B,EAAA,CAAA+E,eAAA,KAAAC,GAAA,EAAAT,MAAA,CAAAjD,cAAA,EAA4B,0BAAAiD,MAAA,CAAAU,kBAAA;IAiBCjF,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAA0B,iBAAA,CAAA6C,MAAA,CAAAf,gBAAA,CAAA0B,MAAA,CAA6B;IAGtDlF,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAA4B,UAAA,SAAA2C,MAAA,CAAAY,iBAAA,CAAuB;IAMvBnF,EAAA,CAAAI,SAAA,GAAuD;IAAvDJ,EAAA,CAAA4B,UAAA,UAAA2C,MAAA,CAAAY,iBAAA,IAAAZ,MAAA,CAAAf,gBAAA,CAAA0B,MAAA,KAAuD;IA8BvDlF,EAAA,CAAAI,SAAA,GAAyD;IAAzDJ,EAAA,CAAA4B,UAAA,UAAA2C,MAAA,CAAAY,iBAAA,IAAAZ,MAAA,CAAAf,gBAAA,CAAA0B,MAAA,OAAyD;;;;;IA2BvElF,EAAA,CAAAC,cAAA,cAA0C;IAEtCD,EAAA,CAAAmB,SAAA,aAAsD;IACtDnB,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,0CAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClCH,EAAA,CAAAC,cAAA,YAAsB;IAAAD,EAAA,CAAAE,MAAA,4EAA2D;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;;IA7S/FH,EAAA,CAAAoF,uBAAA,GAAyC;IACvCpF,EAAA,CAAAC,cAAA,cAAqB;IAOoCD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC3DH,EAAA,CAAAC,cAAA,iBAKC;IAFCD,EAAA,CAAAQ,UAAA,2BAAA6E,oFAAAC,MAAA;MAAAtF,EAAA,CAAAW,aAAA,CAAA4E,IAAA;MAAA,MAAAC,OAAA,GAAAxF,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAAuE,OAAA,CAAAC,YAAA,GAAAH,MAAA;IAAA,EAA0B,oBAAAI,6EAAA;MAAA1F,EAAA,CAAAW,aAAA,CAAA4E,IAAA;MAAA,MAAAI,OAAA,GAAA3F,EAAA,CAAAgB,aAAA;MAAA,OAChBhB,EAAA,CAAAiB,WAAA,CAAA0E,OAAA,CAAAC,YAAA,EAAc;IAAA,EADE;IAG1B5F,EAAA,CAAAC,cAAA,kBAAoB;IAAAD,EAAA,CAAAE,MAAA,wBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC7CH,EAAA,CAAAC,cAAA,kBAAuB;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACrCH,EAAA,CAAAC,cAAA,kBAAyB;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACzCH,EAAA,CAAAC,cAAA,kBAAwB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACzCH,EAAA,CAAAC,cAAA,kBAAwB;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAG/CH,EAAA,CAAAC,cAAA,eAAsB;IACyBD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC9DH,EAAA,CAAAC,cAAA,eAAyB;IAMrBD,EAAA,CAAAQ,UAAA,2BAAAqF,oFAAAP,MAAA;MAAAtF,EAAA,CAAAW,aAAA,CAAA4E,IAAA;MAAA,MAAAO,OAAA,GAAA9F,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAA6E,OAAA,CAAAC,UAAA,GAAAT,MAAA;IAAA,EAAwB,mBAAAU,4EAAA;MAAAhG,EAAA,CAAAW,aAAA,CAAA4E,IAAA;MAAA,MAAAU,OAAA,GAAAjG,EAAA,CAAAgB,aAAA;MAAA,OACfhB,EAAA,CAAAiB,WAAA,CAAAgF,OAAA,CAAAL,YAAA,EAAc;IAAA,EADC;IAL1B5F,EAAA,CAAAG,YAAA,EAOC;IACDH,EAAA,CAAAC,cAAA,gBAA+B;IAC7BD,EAAA,CAAAmB,SAAA,aAAkC;IACpCnB,EAAA,CAAAG,YAAA,EAAO;IAOjBH,EAAA,CAAAC,cAAA,eAAkB;IAEcD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzCH,EAAA,CAAAC,cAAA,gBAA+B;IAAAD,EAAA,CAAAE,MAAA,IAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEpEH,EAAA,CAAAC,cAAA,eAA2B;IAEvBD,EAAA,CAAAqD,UAAA,KAAA6C,6DAAA,sBAwBS;IAETlG,EAAA,CAAAqD,UAAA,KAAA8C,0DAAA,kBAGM;IACRnG,EAAA,CAAAG,YAAA,EAAM;IAMZH,EAAA,CAAAC,cAAA,eAAsB;IACpBD,EAAA,CAAAqD,UAAA,KAAA+C,0DAAA,oBAiNM;IAENpG,EAAA,CAAAqD,UAAA,KAAAgD,0DAAA,kBAMM;IACRrG,EAAA,CAAAG,YAAA,EAAM;IAEVH,EAAA,CAAAsG,qBAAA,EAAe;;;;IAtSCtG,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAA4B,UAAA,YAAA2E,MAAA,CAAAd,YAAA,CAA0B;IAkBxBzF,EAAA,CAAAI,SAAA,IAAwB;IAAxBJ,EAAA,CAAA4B,UAAA,YAAA2E,MAAA,CAAAR,UAAA,CAAwB;IAeD/F,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAA0B,iBAAA,CAAA6E,MAAA,CAAAC,eAAA,CAAAtB,MAAA,CAA4B;IAKpClF,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAA4B,UAAA,YAAA2E,MAAA,CAAAC,eAAA,CAAkB;IAyBjCxG,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAA4B,UAAA,SAAA2E,MAAA,CAAAC,eAAA,CAAAtB,MAAA,OAAkC;IAWxClF,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAA4B,UAAA,SAAA2E,MAAA,CAAAjF,cAAA,CAAoB;IAmNpBtB,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAA4B,UAAA,UAAA2E,MAAA,CAAAjF,cAAA,CAAqB;;;;;IAwDvCtB,EAAA,CAAAmB,SAAA,eAAsI;;;;IAArGnB,EAAA,CAAAoB,WAAA,SAAAqF,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,iBAAA,CAAkD;;;ADpXnF,OAAM,MAAOC,0BAA0B;EAsBrCC,YACUC,aAA4B,EAC5BC,eAAgC,EAChCC,eAAgC;IAFhC,KAAAF,aAAa,GAAbA,aAAa;IACb,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,eAAe,GAAfA,eAAe;IAxBzB,KAAAC,OAAO,GAAa,EAAE;IACtB,KAAAT,eAAe,GAAa,EAAE;IAC9B,KAAAlF,cAAc,GAAkB,IAAI;IACpC,KAAAc,iBAAiB,GAA6B,IAAI;IAClD,KAAAoB,gBAAgB,GAAe,EAAE;IAEjC,KAAAiC,YAAY,GAAW,KAAK;IAC5B,KAAAM,UAAU,GAAW,EAAE;IAEvB,KAAAmB,OAAO,GAAG,IAAI;IACd,KAAApC,kBAAkB,GAAG,KAAK;IAC1B,KAAAK,iBAAiB,GAAG,KAAK;IACzB,KAAA5E,KAAK,GAAG,EAAE;IAEV;IACA,KAAAmG,cAAc,GAAG,KAAK;IACtB,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAQ,aAAa,GAAkB,IAAI;IAE3B,KAAAC,aAAa,GAAmB,EAAE;EAMtC;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE;IAClB,IAAI,CAACC,oBAAoB,EAAE;EAC7B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACJ,aAAa,CAACK,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;EACtD;EAEQL,WAAWA,CAAA;IACjB,IAAI,CAACJ,OAAO,GAAG,IAAI;IAEnB,IAAI,CAACE,aAAa,CAACQ,IAAI,CACrB,IAAI,CAACd,aAAa,CAACe,UAAU,EAAE,CAACC,SAAS,CAAC;MACxCC,IAAI,EAAGd,OAAO,IAAI;QAChB,IAAI,CAACA,OAAO,GAAGA,OAAO;QACtB,IAAI,CAACrB,YAAY,EAAE;QACnB,IAAI,CAACsB,OAAO,GAAG,KAAK;MACtB,CAAC;MACD3G,KAAK,EAAGyH,GAAG,IAAI;QACbC,OAAO,CAAC1H,KAAK,CAAC,uBAAuB,EAAEyH,GAAG,CAAC;QAC3C,IAAI,CAACzH,KAAK,GAAG,wCAAwC;QACrD,IAAI,CAAC2G,OAAO,GAAG,KAAK;MACtB;KACD,CAAC,CACH;EACH;EAEQK,oBAAoBA,CAAA;IAC1B;IACA,IAAI,CAACP,eAAe,CAACkB,eAAe,EAAE,CACnCC,IAAI,CAAC,MAAK;MACTF,OAAO,CAACG,GAAG,CAAC,4BAA4B,CAAC;MACzC,IAAI,CAACpB,eAAe,CAACqB,cAAc,EAAE;MAErC;MACA,IAAI,CAACjB,aAAa,CAACQ,IAAI,CACrB,IAAI,CAACZ,eAAe,CAACsB,cAAc,CAACR,SAAS,CAACS,MAAM,IAAG;QACrD,IAAIA,MAAM,EAAE;UACV,IAAI,CAACC,YAAY,CAACD,MAAM,CAAC;;MAE7B,CAAC,CAAC,CACH;IACH,CAAC,CAAC,CACDE,KAAK,CAACT,GAAG,IAAG;MACXC,OAAO,CAAC1H,KAAK,CAAC,mCAAmC,EAAEyH,GAAG,CAAC;IACzD,CAAC,CAAC;EACN;EAEQQ,YAAYA,CAACE,aAAqB;IACxC,MAAMC,KAAK,GAAG,IAAI,CAAC1B,OAAO,CAAC2B,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACtH,EAAE,KAAKmH,aAAa,CAACnH,EAAE,CAAC;IAEpE,IAAIoH,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,IAAI,CAAC1B,OAAO,CAAC0B,KAAK,CAAC,GAAGD,aAAa;KACpC,MAAM;MACL,IAAI,CAACzB,OAAO,CAACW,IAAI,CAACc,aAAa,CAAC;;IAGlC,IAAI,CAAC9C,YAAY,EAAE;IAEnB;IACA,IAAI,IAAI,CAACtE,cAAc,IAAI,IAAI,CAACA,cAAc,CAACC,EAAE,KAAKmH,aAAa,CAACnH,EAAE,EAAE;MACtE,IAAI,CAACD,cAAc,GAAGoH,aAAa;;EAEvC;EAEA9C,YAAYA,CAAA;IACV,IAAIkD,QAAQ,GAAG,CAAC,GAAG,IAAI,CAAC7B,OAAO,CAAC;IAEhC;IACA,IAAI,IAAI,CAACxB,YAAY,KAAK,KAAK,EAAE;MAC/BqD,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACF,CAAC,IAAIA,CAAC,CAAC/G,MAAM,KAAKkH,QAAQ,CAAC,IAAI,CAACvD,YAAY,EAAE,EAAE,CAAC,CAAC;;IAG/E;IACA,IAAI,IAAI,CAACM,UAAU,CAACkD,IAAI,EAAE,KAAK,EAAE,EAAE;MACjC,MAAMC,MAAM,GAAG,IAAI,CAACnD,UAAU,CAACoD,WAAW,EAAE;MAC5CL,QAAQ,GAAGA,QAAQ,CAACC,MAAM,CAACF,CAAC,IAC1BA,CAAC,CAACrH,IAAI,CAAC2H,WAAW,EAAE,CAACC,QAAQ,CAACF,MAAM,CAAC,IACrCL,CAAC,CAACrE,KAAK,CAAC2E,WAAW,EAAE,CAACC,QAAQ,CAACF,MAAM,CAAC,IACtCL,CAAC,CAACpE,KAAK,CAAC0E,WAAW,EAAE,CAACC,QAAQ,CAACF,MAAM,CAAC,IACtCL,CAAC,CAAClH,WAAW,CAACwH,WAAW,EAAE,CAACC,QAAQ,CAACF,MAAM,CAAC,CAC7C;;IAGH,IAAI,CAAC1C,eAAe,GAAGsC,QAAQ;EACjC;EAEA5H,YAAYA,CAACqH,MAAc;IACzB,IAAI,CAACjH,cAAc,GAAGiH,MAAM;IAC5B,IAAI,CAACc,qBAAqB,CAACd,MAAM,CAAChH,EAAE,CAAC;IACrC,IAAI,CAAC+H,oBAAoB,CAACf,MAAM,CAAChH,EAAE,CAAC;EACtC;EAEA8H,qBAAqBA,CAACE,QAAgB;IACpC,IAAI,CAACzE,kBAAkB,GAAG,IAAI;IAE9B,IAAI,CAACsC,aAAa,CAACQ,IAAI,CACrB,IAAI,CAACd,aAAa,CAAC0C,oBAAoB,CAACD,QAAQ,CAAC,CAACzB,SAAS,CAAC;MAC1DC,IAAI,EAAG0B,WAAW,IAAI;QACpB,IAAI,CAACrH,iBAAiB,GAAGqH,WAAW;QACpC,IAAI,CAAC3E,kBAAkB,GAAG,KAAK;MACjC,CAAC;MACDvE,KAAK,EAAGyH,GAAG,IAAI;QACbC,OAAO,CAAC1H,KAAK,CAAC,kCAAkC,EAAEyH,GAAG,CAAC;QACtD,IAAI,CAAClD,kBAAkB,GAAG,KAAK;MACjC;KACD,CAAC,CACH;EACH;EAEAwE,oBAAoBA,CAACC,QAAgB;IACnC,IAAI,CAACpE,iBAAiB,GAAG,IAAI;IAE7B,IAAI,CAACiC,aAAa,CAACQ,IAAI,CACrB,IAAI,CAACb,eAAe,CAAC2C,uBAAuB,CAACH,QAAQ,CAAC,CAACzB,SAAS,CAAC;MAC/DC,IAAI,EAAG4B,UAAU,IAAI;QACnB,IAAI,CAACnG,gBAAgB,GAAGmG,UAAU;QAClC,IAAI,CAACxE,iBAAiB,GAAG,KAAK;MAChC,CAAC;MACD5E,KAAK,EAAGyH,GAAG,IAAI;QACbC,OAAO,CAAC1H,KAAK,CAAC,iCAAiC,EAAEyH,GAAG,CAAC;QACrD,IAAI,CAAC7C,iBAAiB,GAAG,KAAK;MAChC;KACD,CAAC,CACH;EACH;EAEApD,cAAcA,CAACD,MAAoB;IACjC,QAAQA,MAAM;MACZ,KAAKhC,YAAY,CAAC8J,SAAS;QACzB,OAAO,SAAS;MAClB,KAAK9J,YAAY,CAAC+J,IAAI;QACpB,OAAO,MAAM;MACf,KAAK/J,YAAY,CAACgK,OAAO;QACvB,OAAO,SAAS;MAClB,KAAKhK,YAAY,CAACiK,OAAO;QACvB,OAAO,SAAS;MAClB;QACE,OAAO,SAAS;;EAEtB;EAEAlI,aAAaA,CAACC,MAAoB;IAChC,QAAQA,MAAM;MACZ,KAAKhC,YAAY,CAAC8J,SAAS;QACzB,OAAO,YAAY;MACrB,KAAK9J,YAAY,CAAC+J,IAAI;QACpB,OAAO,QAAQ;MACjB,KAAK/J,YAAY,CAACgK,OAAO;QACvB,OAAO,UAAU;MACnB,KAAKhK,YAAY,CAACiK,OAAO;QACvB,OAAO,YAAY;MACrB;QACE,OAAOjI,MAAM;;EAEnB;EAEAoB,sBAAsBA,CAACpB,MAAsB;IAC3C,QAAQA,MAAM;MACZ,KAAK/B,cAAc,CAACiK,SAAS;QAC3B,OAAO,SAAS;MAClB,KAAKjK,cAAc,CAACkK,SAAS;QAC3B,OAAO,MAAM;MACf,KAAKlK,cAAc,CAACmK,OAAO;QACzB,OAAO,QAAQ;MACjB,KAAKnK,cAAc,CAACoK,OAAO;QACzB,OAAO,SAAS;MAClB,KAAKpK,cAAc,CAACqK,SAAS;QAC3B,OAAO,SAAS;MAClB;QACE,OAAO,SAAS;;EAEtB;EAEAnH,qBAAqBA,CAACnB,MAAsB;IAC1C,QAAQA,MAAM;MACZ,KAAK/B,cAAc,CAACiK,SAAS;QAC3B,OAAO,OAAO;MAChB,KAAKjK,cAAc,CAACkK,SAAS;QAC3B,OAAO,UAAU;MACnB,KAAKlK,cAAc,CAACmK,OAAO;QACzB,OAAO,SAAS;MAClB,KAAKnK,cAAc,CAACoK,OAAO;QACzB,OAAO,YAAY;MACrB,KAAKpK,cAAc,CAACqK,SAAS;QAC3B,OAAO,QAAQ;MACjB;QACE,OAAOC,MAAM,CAACvI,MAAM,CAAC;;EAE3B;EAEA4C,UAAUA,CAAC4F,IAAmB;IAC5B,OAAO,IAAIC,IAAI,CAACD,IAAI,CAAC,CAACE,cAAc,EAAE;EACxC;EAEAvF,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAAC3D,cAAc,EAAE,OAAOmJ,SAAS;IAC1C,OAAO,CAAC,IAAI,CAACnJ,cAAc,CAACoJ,eAAe,CAACC,QAAQ,EAAE,IAAI,CAACrJ,cAAc,CAACoJ,eAAe,CAACE,SAAS,CAAC;EACtG;EAEA;EACAC,WAAWA,CAAA;IACT,IAAI,CAAC1D,aAAa,GAAG,IAAI;IACzB,IAAI,CAACT,cAAc,GAAG,IAAI;EAC5B;EAEAvC,YAAYA,CAACoE,MAAc;IACzB,IAAI,CAACpB,aAAa,GAAGoB,MAAM;IAC3B,IAAI,CAAC7B,cAAc,GAAG,IAAI;EAC5B;EAEApC,cAAcA,CAACiE,MAAc;IAC3B,IAAI,CAACjH,cAAc,GAAGiH,MAAM;IAC5B,IAAI,CAAC5B,iBAAiB,GAAG,IAAI;EAC/B;EAEAmE,kBAAkBA,CAACC,UAAe;IAChC,IAAI,IAAI,CAAC5D,aAAa,EAAE;MACtB;MACA,MAAMuB,aAAa,GAAW;QAC5B,GAAG,IAAI,CAACvB,aAAa;QACrB,GAAG4D;OACJ;MAED,IAAI,CAAC3D,aAAa,CAACQ,IAAI,CACrB,IAAI,CAACd,aAAa,CAAC0B,YAAY,CAACE,aAAa,CAAC,CAACZ,SAAS,CAAC;QACvDC,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAACT,WAAW,EAAE;UAClB,IAAI,CAACZ,cAAc,GAAG,KAAK;UAC3B,IAAI,CAACS,aAAa,GAAG,IAAI;QAC3B,CAAC;QACD5G,KAAK,EAAGyH,GAAG,IAAI;UACbC,OAAO,CAAC1H,KAAK,CAAC,uBAAuB,EAAEyH,GAAG,CAAC;UAC3C,IAAI,CAACzH,KAAK,GAAG,0CAA0C;QACzD;OACD,CAAC,CACH;KACF,MAAM;MACL;MACA,IAAI,CAAC6G,aAAa,CAACQ,IAAI,CACrB,IAAI,CAACd,aAAa,CAACkE,YAAY,CAACD,UAAU,CAAC,CAACjD,SAAS,CAAC;QACpDC,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAACT,WAAW,EAAE;UAClB,IAAI,CAACZ,cAAc,GAAG,KAAK;UAC3B,IAAI,CAACnG,KAAK,GAAG,EAAE,CAAC,CAAC;QACnB,CAAC;;QACDA,KAAK,EAAGyH,GAAG,IAAI;UACbC,OAAO,CAAC1H,KAAK,CAAC,uBAAuB,EAAEyH,GAAG,CAAC;UAE3C;UACA,IAAIA,GAAG,CAACzH,KAAK,IAAIyH,GAAG,CAACzH,KAAK,CAAC0K,MAAM,EAAE;YACjC;YACA,MAAMC,aAAa,GAAG,EAAE;YACxB,KAAK,MAAMC,KAAK,IAAInD,GAAG,CAACzH,KAAK,CAAC0K,MAAM,EAAE;cACpCC,aAAa,CAACtD,IAAI,CAAC,GAAGuD,KAAK,KAAKnD,GAAG,CAACzH,KAAK,CAAC0K,MAAM,CAACE,KAAK,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;;YAEvE,IAAI,CAAC7K,KAAK,GAAG,0BAA0B2K,aAAa,CAACE,IAAI,CAAC,IAAI,CAAC,EAAE;WAClE,MAAM,IAAIpD,GAAG,CAACzH,KAAK,IAAIyH,GAAG,CAACzH,KAAK,CAAC8K,KAAK,EAAE;YACvC,IAAI,CAAC9K,KAAK,GAAGyH,GAAG,CAACzH,KAAK,CAAC8K,KAAK;WAC7B,MAAM;YACL,IAAI,CAAC9K,KAAK,GAAG,uCAAuC;;QAExD;OACD,CAAC,CACH;;EAEL;EAEA+K,kBAAkBA,CAAA;IAChB,IAAI,CAAC5E,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACS,aAAa,GAAG,IAAI;EAC3B;EAEAoE,aAAaA,CAAA;IACX,IAAI,IAAI,CAACjK,cAAc,EAAE;MACvB,IAAI,CAAC8F,aAAa,CAACQ,IAAI,CACrB,IAAI,CAACd,aAAa,CAAC0E,YAAY,CAAC,IAAI,CAAClK,cAAc,CAACC,EAAE,CAAC,CAACuG,SAAS,CAAC;QAChEC,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAACT,WAAW,EAAE;UAClB,IAAI,CAAChG,cAAc,GAAG,IAAI;UAC1B,IAAI,CAACqF,iBAAiB,GAAG,KAAK;QAChC,CAAC;QACDpG,KAAK,EAAGyH,GAAG,IAAI;UACbC,OAAO,CAAC1H,KAAK,CAAC,uBAAuB,EAAEyH,GAAG,CAAC;UAC3C,IAAI,CAACzH,KAAK,GAAG,0CAA0C;QACzD;OACD,CAAC,CACH;;EAEL;EAEAkL,YAAYA,CAAA;IACV,IAAI,CAAC9E,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACrF,cAAc,GAAG,IAAI;EAC5B;EAEAoK,QAAQA,CAAA;IACN;IACAC,KAAK,CAAC,mCAAmC,CAAC;EAC5C;;;uBAxUW/E,0BAA0B,EAAA5G,EAAA,CAAA4L,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAA9L,EAAA,CAAA4L,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAhM,EAAA,CAAA4L,iBAAA,CAAAK,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAA1BtF,0BAA0B;MAAAuF,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCbvCzM,EAAA,CAAAC,cAAA,aAA+B;UAC7BD,EAAA,CAAAmB,SAAA,kBAA2B;UAE3BnB,EAAA,CAAAC,cAAA,aAA6B;UAMSD,EAAA,CAAAQ,UAAA,mBAAAmM,4DAAA;YAAA,OAASD,GAAA,CAAA7B,WAAA,EAAa;UAAA,EAAC;UACrD7K,EAAA,CAAAmB,SAAA,WAAqC;UACrCnB,EAAA,CAAAE,MAAA,wBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,gBAA+D;UAArBD,EAAA,CAAAQ,UAAA,mBAAAoM,4DAAA;YAAA,OAASF,GAAA,CAAAhB,QAAA,EAAU;UAAA,EAAC;UAC5D1L,EAAA,CAAAmB,SAAA,WAA4C;UAC5CnB,EAAA,CAAAE,MAAA,kBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAIbH,EAAA,CAAAC,cAAA,cAA8B;UAC5BD,EAAA,CAAAqD,UAAA,KAAAwJ,0CAAA,iBAKM;UAEN7M,EAAA,CAAAqD,UAAA,KAAAyJ,0CAAA,kBAEM;UAEN9M,EAAA,CAAAqD,UAAA,KAAA0J,mDAAA,4BAkTe;UACjB/M,EAAA,CAAAG,YAAA,EAAM;UAKVH,EAAA,CAAAC,cAAA,eAAwH;UAK9GD,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,kBAAuE;UAA/BD,EAAA,CAAAQ,UAAA,mBAAAwM,6DAAA;YAAA,OAASN,GAAA,CAAApB,kBAAA,EAAoB;UAAA,EAAC;UAACtL,EAAA,CAAAG,YAAA,EAAS;UAElFH,EAAA,CAAAC,cAAA,eAAwB;UAGpBD,EAAA,CAAAQ,UAAA,uBAAAyM,0EAAA3H,MAAA;YAAA,OAAaoH,GAAA,CAAA5B,kBAAA,CAAAxF,MAAA,CAA0B;UAAA,EAAC,uBAAA4H,0EAAA;YAAA,OAC3BR,GAAA,CAAApB,kBAAA,EAAoB;UAAA,EADO;UAEzCtL,EAAA,CAAAG,YAAA,EAAkB;UAO3BH,EAAA,CAAAC,cAAA,eAA8H;UAI9FD,EAAA,CAAAE,MAAA,gCAAwB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrDH,EAAA,CAAAC,cAAA,kBAAiE;UAAzBD,EAAA,CAAAQ,UAAA,mBAAA2M,6DAAA;YAAA,OAAST,GAAA,CAAAjB,YAAA,EAAc;UAAA,EAAC;UAACzL,EAAA,CAAAG,YAAA,EAAS;UAE5EH,EAAA,CAAAC,cAAA,eAAwB;UACnBD,EAAA,CAAAE,MAAA,gEAA8C;UAAAF,EAAA,CAAAC,cAAA,cAAQ;UAAAD,EAAA,CAAAE,MAAA,IAA0B;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAACH,EAAA,CAAAE,MAAA,UAAC;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAClGH,EAAA,CAAAC,cAAA,aAAsB;UAAAD,EAAA,CAAAE,MAAA,2CAA8B;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAE1DH,EAAA,CAAAC,cAAA,eAA0B;UACwBD,EAAA,CAAAQ,UAAA,mBAAA4M,6DAAA;YAAA,OAASV,GAAA,CAAAjB,YAAA,EAAc;UAAA,EAAC;UAACzL,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACzFH,EAAA,CAAAC,cAAA,kBAAuE;UAA1BD,EAAA,CAAAQ,UAAA,mBAAA6M,6DAAA;YAAA,OAASX,GAAA,CAAAnB,aAAA,EAAe;UAAA,EAAC;UAACvL,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAOjGH,EAAA,CAAAqD,UAAA,KAAAiK,0CAAA,kBAAsI;;;UA5W1HtN,EAAA,CAAAI,SAAA,IAAa;UAAbJ,EAAA,CAAA4B,UAAA,SAAA8K,GAAA,CAAAxF,OAAA,CAAa;UAOblH,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAA4B,UAAA,SAAA8K,GAAA,CAAAnM,KAAA,CAAW;UAIFP,EAAA,CAAAI,SAAA,GAAwB;UAAxBJ,EAAA,CAAA4B,UAAA,UAAA8K,GAAA,CAAAxF,OAAA,KAAAwF,GAAA,CAAAnM,KAAA,CAAwB;UAwTSP,EAAA,CAAAI,SAAA,GAAmD;UAAnDJ,EAAA,CAAAuN,WAAA,YAAAb,GAAA,CAAAhG,cAAA,oBAAmD;UAAjF1G,EAAA,CAAAoB,WAAA,SAAAsL,GAAA,CAAAhG,cAAA,CAA6B;UAK3C1G,EAAA,CAAAI,SAAA,GACF;UADEJ,EAAA,CAAAK,kBAAA,MAAAqM,GAAA,CAAAvF,aAAA,kDACF;UAKEnH,EAAA,CAAAI,SAAA,GAAwB;UAAxBJ,EAAA,CAAA4B,UAAA,WAAA8K,GAAA,CAAAvF,aAAA,CAAwB;UAUuBnH,EAAA,CAAAI,SAAA,GAAsD;UAAtDJ,EAAA,CAAAuN,WAAA,YAAAb,GAAA,CAAA/F,iBAAA,oBAAsD;UAAvF3G,EAAA,CAAAoB,WAAA,SAAAsL,GAAA,CAAA/F,iBAAA,CAAgC;UAQS3G,EAAA,CAAAI,SAAA,IAA0B;UAA1BJ,EAAA,CAAA0B,iBAAA,CAAAgL,GAAA,CAAApL,cAAA,kBAAAoL,GAAA,CAAApL,cAAA,CAAAE,IAAA,CAA0B;UAYNxB,EAAA,CAAAI,SAAA,GAAyC;UAAzCJ,EAAA,CAAA4B,UAAA,SAAA8K,GAAA,CAAAhG,cAAA,IAAAgG,GAAA,CAAA/F,iBAAA,CAAyC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}