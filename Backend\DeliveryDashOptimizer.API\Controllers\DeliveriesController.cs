using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using DeliveryDashOptimizer.API.Models;
using DeliveryDashOptimizer.API.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace DeliveryDashOptimizer.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class DeliveriesController : ControllerBase
    {
        private readonly IDeliveryService _deliveryService;
        private readonly ILogger<DeliveriesController> _logger;

        public DeliveriesController(IDeliveryService deliveryService, ILogger<DeliveriesController> logger)
        {
            _deliveryService = deliveryService;
            _logger = logger;
        }

        [HttpGet]
        [Authorize(Roles = "Admin,Manager,Dispatcher")]
        public async Task<ActionResult<IEnumerable<Delivery>>> GetDeliveries()
        {
            try
            {
                var deliveries = await _deliveryService.GetAllDeliveriesAsync();
                return Ok(deliveries);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving deliveries");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("{id}")]
        [Authorize(Roles = "Admin,Manager,Dispatcher,Driver,Customer")]
        public async Task<ActionResult<Delivery>> GetDelivery(string id)
        {
            try
            {
                var delivery = await _deliveryService.GetDeliveryByIdAsync(id);
                if (delivery == null)
                {
                    return NotFound();
                }
                return Ok(delivery);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error retrieving delivery with ID {id}");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("order/{orderId}")]
        [Authorize(Roles = "Admin,Manager,Dispatcher,Customer")]
        public async Task<ActionResult<Delivery>> GetDeliveryByOrderId(string orderId)
        {
            try
            {
                var delivery = await _deliveryService.GetDeliveryByOrderIdAsync(orderId);
                if (delivery == null)
                {
                    return NotFound();
                }
                return Ok(delivery);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error retrieving delivery with order ID {orderId}");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("driver/{driverId}")]
        [Authorize(Roles = "Admin,Manager,Dispatcher,Driver")]
        public async Task<ActionResult<IEnumerable<Delivery>>> GetDeliveriesByDriver(string driverId)
        {
            try
            {
                var deliveries = await _deliveryService.GetDeliveriesByDriverIdAsync(driverId);
                return Ok(deliveries);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error retrieving deliveries for driver {driverId}");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("status/{status}")]
        [Authorize(Roles = "Admin,Manager,Dispatcher")]
        public async Task<ActionResult<IEnumerable<Delivery>>> GetDeliveriesByStatus(string status)
        {
            try
            {
                if (!Enum.TryParse<DeliveryStatus>(status, true, out var deliveryStatus))
                {
                    return BadRequest("Invalid status");
                }

                var deliveries = await _deliveryService.GetDeliveriesByStatusAsync(deliveryStatus);
                return Ok(deliveries);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error retrieving deliveries with status {status}");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPost]
        [Authorize(Roles = "Admin,Manager,Dispatcher")]
        public async Task<ActionResult<Delivery>> CreateDelivery([FromBody] Delivery delivery)
        {
            try
            {
                var createdDelivery = await _deliveryService.CreateDeliveryAsync(delivery);
                return CreatedAtAction(nameof(GetDelivery), new { id = createdDelivery.Id }, createdDelivery);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating delivery");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPut("{id}")]
        [Authorize(Roles = "Admin,Manager,Dispatcher,Driver")]
        public async Task<IActionResult> UpdateDelivery(string id, Delivery delivery)
        {
            if (id != delivery.Id)
            {
                return BadRequest();
            }

            try
            {
                await _deliveryService.UpdateDeliveryAsync(delivery);
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error updating delivery with ID {id}");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpDelete("{id}")]
        [Authorize(Roles = "Admin,Manager")]
        public async Task<IActionResult> DeleteDelivery(string id)
        {
            try
            {
                await _deliveryService.DeleteDeliveryAsync(id);
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error deleting delivery with ID {id}");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPut("{id}/status")]
        [Authorize(Roles = "Admin,Manager,Dispatcher,Driver")]
        public async Task<ActionResult<Delivery>> UpdateDeliveryStatus(string id, [FromBody] UpdateDeliveryStatusRequest request)
        {
            try
            {
                var delivery = await _deliveryService.UpdateDeliveryStatusAsync(id, request.Status);
                if (delivery == null)
                {
                    return NotFound();
                }
                return Ok(delivery);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error updating delivery status for ID {id}");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("customer")]
        [Authorize(Roles = "Admin,Manager,Dispatcher,Customer")]
        public async Task<ActionResult<IEnumerable<Delivery>>> GetDeliveriesByCustomer([FromQuery] string customerId)
        {
            try
            {
                if (string.IsNullOrEmpty(customerId))
                {
                    return BadRequest("Customer ID is required");
                }

                var deliveries = await _deliveryService.GetDeliveriesByCustomerIdAsync(customerId);
                return Ok(deliveries);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving deliveries for customer {CustomerId}", customerId);
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("customer/{customerId}/stats")]
        [Authorize(Roles = "Admin,Manager,Customer")]
        public async Task<ActionResult<object>> GetCustomerStats(string customerId)
        {
            try
            {
                if (string.IsNullOrEmpty(customerId))
                {
                    return BadRequest("Customer ID is required");
                }

                var deliveries = await _deliveryService.GetDeliveriesByCustomerIdAsync(customerId);

                var stats = new
                {
                    TotalOrders = deliveries.Count(),
                    DeliveredOrders = deliveries.Count(d => d.Status == DeliveryStatus.Delivered),
                    PendingOrders = deliveries.Count(d => d.Status == DeliveryStatus.Pending),
                    InTransitOrders = deliveries.Count(d => d.Status == DeliveryStatus.InTransit),
                    CancelledOrders = deliveries.Count(d => d.Status == DeliveryStatus.Cancelled),
                    AverageRating = deliveries.Where(d => d.CustomerRating.HasValue && d.CustomerRating > 0)
                                             .Select(d => d.CustomerRating.Value)
                                             .DefaultIfEmpty(0)
                                             .Average()
                };

                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving stats for customer {CustomerId}", customerId);
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("area")]
        [Authorize(Roles = "Admin,Manager,Dispatcher")]
        public async Task<ActionResult<IEnumerable<Delivery>>> GetDeliveriesInArea([FromQuery] double latitude, [FromQuery] double longitude, [FromQuery] double radiusKm)
        {
            try
            {
                var deliveries = await _deliveryService.GetDeliveriesInAreaAsync(latitude, longitude, radiusKm);
                return Ok(deliveries);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving deliveries in area");
                return StatusCode(500, "Internal server error");
            }
        }
    }

    public class UpdateDeliveryStatusRequest
    {
        public DeliveryStatus Status { get; set; }
    }
}
