{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport { UserRole } from '../../../core/models/user.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/common\";\nfunction UserFormComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getFieldError(\"username\"), \" \");\n  }\n}\nfunction UserFormComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getFieldError(\"email\"), \" \");\n  }\n}\nfunction UserFormComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getFieldError(\"firstName\"), \" \");\n  }\n}\nfunction UserFormComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.getFieldError(\"lastName\"), \" \");\n  }\n}\nfunction UserFormComponent_option_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 26);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const role_r9 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", role_r9.value);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", role_r9.label, \" \");\n  }\n}\nfunction UserFormComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.getFieldError(\"role\"), \" \");\n  }\n}\nfunction UserFormComponent_div_34_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r10.getFieldError(\"password\"), \" \");\n  }\n}\nfunction UserFormComponent_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"label\", 27);\n    i0.ɵɵtext(2, \"Mot de passe *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 28);\n    i0.ɵɵtemplate(4, UserFormComponent_div_34_div_4_Template, 2, 1, \"div\", 5);\n    i0.ɵɵelementStart(5, \"div\", 29);\n    i0.ɵɵtext(6, \" Le mot de passe doit contenir au moins 6 caract\\u00E8res \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r6.isFieldInvalid(\"password\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.isFieldInvalid(\"password\"));\n  }\n}\nfunction UserFormComponent_div_35_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r11.getFieldError(\"confirmPassword\"), \" \");\n  }\n}\nfunction UserFormComponent_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"label\", 30);\n    i0.ɵɵtext(2, \"Confirmer le mot de passe *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 31);\n    i0.ɵɵtemplate(4, UserFormComponent_div_35_div_4_Template, 2, 1, \"div\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r7.isFieldInvalid(\"confirmPassword\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r7.isFieldInvalid(\"confirmPassword\"));\n  }\n}\nfunction UserFormComponent_div_36_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r12.getFieldError(\"password\"), \" \");\n  }\n}\nfunction UserFormComponent_div_36_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r13.getFieldError(\"confirmPassword\"), \" \");\n  }\n}\nfunction UserFormComponent_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"div\", 33);\n    i0.ɵɵelement(2, \"i\", 34);\n    i0.ɵɵtext(3, \" Laissez les champs de mot de passe vides pour conserver le mot de passe actuel. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 1)(5, \"div\", 2)(6, \"label\", 27);\n    i0.ɵɵtext(7, \"Nouveau mot de passe\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"input\", 35);\n    i0.ɵɵtemplate(9, UserFormComponent_div_36_div_9_Template, 2, 1, \"div\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 2)(11, \"label\", 30);\n    i0.ɵɵtext(12, \"Confirmer le nouveau mot de passe\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(13, \"input\", 36);\n    i0.ɵɵtemplate(14, UserFormComponent_div_36_div_14_Template, 2, 1, \"div\", 5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r8.isFieldInvalid(\"password\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.isFieldInvalid(\"password\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"is-invalid\", ctx_r8.isFieldInvalid(\"confirmPassword\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r8.isFieldInvalid(\"confirmPassword\"));\n  }\n}\nexport let UserFormComponent = /*#__PURE__*/(() => {\n  class UserFormComponent {\n    constructor(fb) {\n      this.fb = fb;\n      this.user = null;\n      this.submitted = new EventEmitter();\n      this.cancelled = new EventEmitter();\n      this.isEditMode = false;\n      this.userRoles = [{\n        value: UserRole.Admin,\n        label: 'Administrateur'\n      }, {\n        value: UserRole.Manager,\n        label: 'Gestionnaire'\n      }, {\n        value: UserRole.Dispatcher,\n        label: 'Répartiteur'\n      }, {\n        value: UserRole.Driver,\n        label: 'Livreur'\n      }, {\n        value: UserRole.Customer,\n        label: 'Client'\n      }];\n      this.userForm = this.createForm();\n    }\n    ngOnInit() {\n      this.isEditMode = !!this.user;\n      if (this.user) {\n        this.populateForm();\n      }\n    }\n    createForm() {\n      return this.fb.group({\n        username: ['', [Validators.required, Validators.minLength(3)]],\n        email: ['', [Validators.required, Validators.email]],\n        password: ['', [Validators.required, Validators.minLength(6)]],\n        confirmPassword: ['', [Validators.required]],\n        firstName: ['', [Validators.required]],\n        lastName: ['', [Validators.required]],\n        phoneNumber: [''],\n        role: [UserRole.Customer, [Validators.required]]\n      }, {\n        validators: this.passwordMatchValidator\n      });\n    }\n    populateForm() {\n      if (this.user) {\n        this.userForm.patchValue({\n          username: this.user.username,\n          email: this.user.email,\n          firstName: this.user.firstName,\n          lastName: this.user.lastName,\n          phoneNumber: this.user.phoneNumber,\n          role: this.user.role\n        });\n        // Remove password validation for edit mode\n        this.userForm.get('password')?.clearValidators();\n        this.userForm.get('confirmPassword')?.clearValidators();\n        this.userForm.updateValueAndValidity();\n      }\n    }\n    passwordMatchValidator(form) {\n      const password = form.get('password');\n      const confirmPassword = form.get('confirmPassword');\n      if (password && confirmPassword && password.value !== confirmPassword.value) {\n        confirmPassword.setErrors({\n          passwordMismatch: true\n        });\n        return {\n          passwordMismatch: true\n        };\n      }\n      return null;\n    }\n    onSubmit() {\n      if (this.userForm.valid) {\n        const formValue = this.userForm.value;\n        // Remove password fields if in edit mode and they're empty\n        if (this.isEditMode && !formValue.password) {\n          delete formValue.password;\n          delete formValue.confirmPassword;\n        }\n        // Remove confirmPassword from the final data\n        delete formValue.confirmPassword;\n        this.submitted.emit(formValue);\n      } else {\n        this.markFormGroupTouched();\n      }\n    }\n    onCancel() {\n      this.cancelled.emit();\n    }\n    markFormGroupTouched() {\n      Object.keys(this.userForm.controls).forEach(key => {\n        const control = this.userForm.get(key);\n        control?.markAsTouched();\n      });\n    }\n    isFieldInvalid(fieldName) {\n      const field = this.userForm.get(fieldName);\n      return !!(field && field.invalid && (field.dirty || field.touched));\n    }\n    getFieldError(fieldName) {\n      const field = this.userForm.get(fieldName);\n      if (field && field.errors) {\n        if (field.errors['required']) {\n          return 'Ce champ est requis';\n        }\n        if (field.errors['email']) {\n          return 'Format d\\'email invalide';\n        }\n        if (field.errors['minlength']) {\n          const requiredLength = field.errors['minlength'].requiredLength;\n          return `Minimum ${requiredLength} caractères requis`;\n        }\n        if (field.errors['passwordMismatch']) {\n          return 'Les mots de passe ne correspondent pas';\n        }\n      }\n      return '';\n    }\n    static {\n      this.ɵfac = function UserFormComponent_Factory(t) {\n        return new (t || UserFormComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: UserFormComponent,\n        selectors: [[\"app-user-form\"]],\n        inputs: {\n          user: \"user\"\n        },\n        outputs: {\n          submitted: \"submitted\",\n          cancelled: \"cancelled\"\n        },\n        decls: 44,\n        vars: 22,\n        consts: [[3, \"formGroup\", \"ngSubmit\"], [1, \"row\", \"g-3\"], [1, \"col-md-6\"], [\"for\", \"username\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"username\", \"formControlName\", \"username\", \"placeholder\", \"Entrez le nom d'utilisateur\", 1, \"form-control\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [\"for\", \"email\", 1, \"form-label\"], [\"type\", \"email\", \"id\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"Entrez l'adresse email\", 1, \"form-control\"], [\"for\", \"firstName\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"firstName\", \"formControlName\", \"firstName\", \"placeholder\", \"Entrez le pr\\u00E9nom\", 1, \"form-control\"], [\"for\", \"lastName\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"lastName\", \"formControlName\", \"lastName\", \"placeholder\", \"Entrez le nom de famille\", 1, \"form-control\"], [\"for\", \"phoneNumber\", 1, \"form-label\"], [\"type\", \"tel\", \"id\", \"phoneNumber\", \"formControlName\", \"phoneNumber\", \"placeholder\", \"Entrez le num\\u00E9ro de t\\u00E9l\\u00E9phone\", 1, \"form-control\"], [\"for\", \"role\", 1, \"form-label\"], [\"id\", \"role\", \"formControlName\", \"role\", 1, \"form-select\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"col-md-6\", 4, \"ngIf\"], [\"class\", \"col-12\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-end\", \"gap-2\", \"mt-4\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"fa-solid\", \"fa-times\", \"me-2\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", 3, \"disabled\"], [1, \"fa-solid\", \"fa-save\", \"me-2\"], [1, \"invalid-feedback\"], [3, \"value\"], [\"for\", \"password\", 1, \"form-label\"], [\"type\", \"password\", \"id\", \"password\", \"formControlName\", \"password\", \"placeholder\", \"Entrez le mot de passe\", 1, \"form-control\"], [1, \"form-text\"], [\"for\", \"confirmPassword\", 1, \"form-label\"], [\"type\", \"password\", \"id\", \"confirmPassword\", \"formControlName\", \"confirmPassword\", \"placeholder\", \"Confirmez le mot de passe\", 1, \"form-control\"], [1, \"col-12\"], [1, \"alert\", \"alert-info\"], [1, \"fa-solid\", \"fa-info-circle\", \"me-2\"], [\"type\", \"password\", \"id\", \"password\", \"formControlName\", \"password\", \"placeholder\", \"Nouveau mot de passe (optionnel)\", 1, \"form-control\"], [\"type\", \"password\", \"id\", \"confirmPassword\", \"formControlName\", \"confirmPassword\", \"placeholder\", \"Confirmez le nouveau mot de passe\", 1, \"form-control\"]],\n        template: function UserFormComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"form\", 0);\n            i0.ɵɵlistener(\"ngSubmit\", function UserFormComponent_Template_form_ngSubmit_0_listener() {\n              return ctx.onSubmit();\n            });\n            i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"label\", 3);\n            i0.ɵɵtext(4, \"Nom d'utilisateur *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(5, \"input\", 4);\n            i0.ɵɵtemplate(6, UserFormComponent_div_6_Template, 2, 1, \"div\", 5);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(7, \"div\", 2)(8, \"label\", 6);\n            i0.ɵɵtext(9, \"Email *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(10, \"input\", 7);\n            i0.ɵɵtemplate(11, UserFormComponent_div_11_Template, 2, 1, \"div\", 5);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(12, \"div\", 2)(13, \"label\", 8);\n            i0.ɵɵtext(14, \"Pr\\u00E9nom *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(15, \"input\", 9);\n            i0.ɵɵtemplate(16, UserFormComponent_div_16_Template, 2, 1, \"div\", 5);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(17, \"div\", 2)(18, \"label\", 10);\n            i0.ɵɵtext(19, \"Nom *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(20, \"input\", 11);\n            i0.ɵɵtemplate(21, UserFormComponent_div_21_Template, 2, 1, \"div\", 5);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(22, \"div\", 2)(23, \"label\", 12);\n            i0.ɵɵtext(24, \"T\\u00E9l\\u00E9phone\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelement(25, \"input\", 13);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(26, \"div\", 2)(27, \"label\", 14);\n            i0.ɵɵtext(28, \"R\\u00F4le *\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(29, \"select\", 15)(30, \"option\", 16);\n            i0.ɵɵtext(31, \"S\\u00E9lectionnez un r\\u00F4le\");\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(32, UserFormComponent_option_32_Template, 2, 2, \"option\", 17);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(33, UserFormComponent_div_33_Template, 2, 1, \"div\", 5);\n            i0.ɵɵelementEnd();\n            i0.ɵɵtemplate(34, UserFormComponent_div_34_Template, 7, 3, \"div\", 18);\n            i0.ɵɵtemplate(35, UserFormComponent_div_35_Template, 5, 3, \"div\", 18);\n            i0.ɵɵtemplate(36, UserFormComponent_div_36_Template, 15, 6, \"div\", 19);\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(37, \"div\", 20)(38, \"button\", 21);\n            i0.ɵɵlistener(\"click\", function UserFormComponent_Template_button_click_38_listener() {\n              return ctx.onCancel();\n            });\n            i0.ɵɵelement(39, \"i\", 22);\n            i0.ɵɵtext(40, \" Annuler \");\n            i0.ɵɵelementEnd();\n            i0.ɵɵelementStart(41, \"button\", 23);\n            i0.ɵɵelement(42, \"i\", 24);\n            i0.ɵɵtext(43);\n            i0.ɵɵelementEnd()()();\n          }\n          if (rf & 2) {\n            i0.ɵɵproperty(\"formGroup\", ctx.userForm);\n            i0.ɵɵadvance(5);\n            i0.ɵɵclassProp(\"is-invalid\", ctx.isFieldInvalid(\"username\"));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isFieldInvalid(\"username\"));\n            i0.ɵɵadvance(4);\n            i0.ɵɵclassProp(\"is-invalid\", ctx.isFieldInvalid(\"email\"));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isFieldInvalid(\"email\"));\n            i0.ɵɵadvance(4);\n            i0.ɵɵclassProp(\"is-invalid\", ctx.isFieldInvalid(\"firstName\"));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isFieldInvalid(\"firstName\"));\n            i0.ɵɵadvance(4);\n            i0.ɵɵclassProp(\"is-invalid\", ctx.isFieldInvalid(\"lastName\"));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isFieldInvalid(\"lastName\"));\n            i0.ɵɵadvance(8);\n            i0.ɵɵclassProp(\"is-invalid\", ctx.isFieldInvalid(\"role\"));\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngForOf\", ctx.userRoles);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isFieldInvalid(\"role\"));\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isEditMode);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", !ctx.isEditMode);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.isEditMode);\n            i0.ɵɵadvance(5);\n            i0.ɵɵproperty(\"disabled\", ctx.userForm.invalid);\n            i0.ɵɵadvance(2);\n            i0.ɵɵtextInterpolate1(\" \", ctx.isEditMode ? \"Mettre \\u00E0 jour\" : \"Cr\\u00E9er\", \" \");\n          }\n        },\n        dependencies: [i2.NgForOf, i2.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.DefaultValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName],\n        styles: [\".form-control[_ngcontent-%COMP%], .form-select[_ngcontent-%COMP%]{border-radius:.375rem;border:1px solid #ced4da;transition:border-color .15s ease-in-out,box-shadow .15s ease-in-out}.form-control[_ngcontent-%COMP%]:focus, .form-select[_ngcontent-%COMP%]:focus{border-color:#86b7fe;outline:0;box-shadow:0 0 0 .25rem #0d6efd40}.form-control.is-invalid[_ngcontent-%COMP%], .form-select.is-invalid[_ngcontent-%COMP%]{border-color:#dc3545}.form-control.is-invalid[_ngcontent-%COMP%]:focus, .form-select.is-invalid[_ngcontent-%COMP%]:focus{border-color:#dc3545;box-shadow:0 0 0 .25rem #dc354540}.invalid-feedback[_ngcontent-%COMP%]{display:block;width:100%;margin-top:.25rem;font-size:.875em;color:#dc3545}.form-text[_ngcontent-%COMP%]{margin-top:.25rem;font-size:.875em;color:#6c757d}.alert[_ngcontent-%COMP%]{border-radius:.375rem;border:1px solid transparent;padding:.75rem 1rem}.alert-info[_ngcontent-%COMP%]{color:#055160;background-color:#cff4fc;border-color:#b6effb}.btn[_ngcontent-%COMP%]{border-radius:.375rem;font-weight:500;transition:all .15s ease-in-out}.btn[_ngcontent-%COMP%]:disabled{opacity:.65;cursor:not-allowed}.btn-primary[_ngcontent-%COMP%]{background-color:#0d6efd;border-color:#0d6efd}.btn-primary[_ngcontent-%COMP%]:hover:not(:disabled){background-color:#0b5ed7;border-color:#0a58ca}.btn-secondary[_ngcontent-%COMP%]{background-color:#6c757d;border-color:#6c757d}.btn-secondary[_ngcontent-%COMP%]:hover{background-color:#5c636a;border-color:#565e64}@media (max-width: 768px){.d-flex.justify-content-end[_ngcontent-%COMP%]{flex-direction:column}.btn[_ngcontent-%COMP%]{width:100%;margin-bottom:.5rem}.btn[_ngcontent-%COMP%]:last-child{margin-bottom:0}}\"]\n      });\n    }\n  }\n  return UserFormComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}