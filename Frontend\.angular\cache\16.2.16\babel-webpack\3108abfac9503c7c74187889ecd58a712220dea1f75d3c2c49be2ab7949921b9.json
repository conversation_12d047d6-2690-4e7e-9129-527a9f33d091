{"ast": null, "code": "import { HttpParams } from '@angular/common/http';\nimport { environment } from '@env/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let DriverService = /*#__PURE__*/(() => {\n  class DriverService {\n    constructor(http) {\n      this.http = http;\n      this.apiUrl = `${environment.apiUrl}/drivers`;\n    }\n    getDrivers() {\n      return this.http.get(this.apiUrl);\n    }\n    getDriverById(id) {\n      return this.http.get(`${this.apiUrl}/${id}`);\n    }\n    getDriversByStatus(status) {\n      const params = new HttpParams().set('status', status);\n      return this.http.get(this.apiUrl, {\n        params\n      });\n    }\n    createDriver(driver) {\n      return this.http.post(this.apiUrl, driver);\n    }\n    updateDriver(driver) {\n      return this.http.put(`${this.apiUrl}/${driver.id}`, driver);\n    }\n    deleteDriver(id) {\n      return this.http.delete(`${this.apiUrl}/${id}`);\n    }\n    updateDriverStatus(id, status) {\n      return this.http.put(`${this.apiUrl}/${id}/status`, {\n        status\n      });\n    }\n    updateDriverLocation(id, location) {\n      return this.http.put(`${this.apiUrl}/${id}/location`, location);\n    }\n    getDriverPerformance(id) {\n      return this.http.get(`${this.apiUrl}/${id}/performance`);\n    }\n    getAvailableDriversInArea(latitude, longitude, radiusKm) {\n      const params = new HttpParams().set('latitude', latitude.toString()).set('longitude', longitude.toString()).set('radiusKm', radiusKm.toString());\n      return this.http.get(`${this.apiUrl}/available`, {\n        params\n      });\n    }\n    // Driver-specific methods for authenticated drivers\n    getCurrentDriver() {\n      return this.http.get(`${this.apiUrl}/me`);\n    }\n    getCurrentDriverDeliveries() {\n      return this.http.get(`${this.apiUrl}/me/deliveries`);\n    }\n    getDriverDeliveries(driverId) {\n      return this.http.get(`${this.apiUrl}/${driverId}/deliveries`);\n    }\n    static {\n      this.ɵfac = function DriverService_Factory(t) {\n        return new (t || DriverService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: DriverService,\n        factory: DriverService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return DriverService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}