export interface Delivery {
  id: string;
  orderId: string;
  customerId: string;
  customerName: string;
  address: string;
  status: DeliveryStatus;
  driverId: string;
  driverName: string;
  estimatedDeliveryTime: Date;
  actualDeliveryTime?: Date;
  priority: DeliveryPriority;
  coordinates: GeoCoordinates;
  createdAt: Date;
  pickupTime?: Date;
  notes?: string;
  distance?: number;
  customerRating?: number;
  customerFeedback?: string;
  weatherCondition?: WeatherCondition;
  trafficCondition?: TrafficCondition;
}

export enum DeliveryStatus {
  Pending = 0,
  InTransit = 1,
  Delivered = 2,
  Delayed = 3,
  Cancelled = 4
}

export enum DeliveryPriority {
  Low = 0,
  Medium = 1,
  High = 2,
  Urgent = 3
}

export enum WeatherCondition {
  Clear = 0,
  Cloudy = 1,
  Rainy = 2,
  Snowy = 3,
  Stormy = 4
}

export enum TrafficCondition {
  Light = 0,
  Moderate = 1,
  Heavy = 2,
  Severe = 3
}

export interface GeoCoordinates {
  latitude: number;
  longitude: number;
}
