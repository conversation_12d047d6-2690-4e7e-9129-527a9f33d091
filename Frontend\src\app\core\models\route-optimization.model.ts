import { DeliveryPriority, GeoCoordinates, TrafficCondition, WeatherCondition } from './delivery.model';

export interface RouteOptimization {
  id: string;
  driverId: string;
  routes: RouteStop[];
  totalDistance: number;
  totalTime: number;
  fuelEfficiency: number;
  optimizationScore: number;
  createdAt: Date;
  completedAt?: Date;
  isActive: boolean;
  optimizationMethod: string;
  weatherCondition?: WeatherCondition;
  trafficCondition?: TrafficCondition;
}

export interface RouteStop {
  deliveryId: string;
  order: number;
  estimatedTime: number;
  distance: number;
  location: GeoCoordinates;
  address: string;
  customerName: string;
  priority: DeliveryPriority;
  estimatedArrival: Date;
  actualArrival?: Date;
}

export interface RouteOptimizationRequest {
  driverId: string;
  deliveryIds: string[];
  startLocation?: GeoCoordinates;
  endLocation?: GeoCoordinates;
  considerTraffic: boolean;
  considerWeather: boolean;
  prioritizeUrgent: boolean;
  strategy: OptimizationObjective;
}

export enum OptimizationObjective {
  MinimizeDistance = 0,
  MinimizeTime = 1,
  MaximizeDeliveries = 2,
  BalanceWorkload = 3
}

export interface DeliveryTimePredictionRequest {
  deliveryId: string;
  driverId?: string;
  currentLocation?: GeoCoordinates;
  currentTime: Date;
}

export interface DeliveryTimePrediction {
  deliveryId: string;
  predictedDeliveryTime: Date;
  confidenceScore: number;
  estimatedTimeInMinutes: number;
  factorImpacts: FactorImpact[];
}

export interface FactorImpact {
  factor: string;
  impact: number;
}

export interface TrafficData {
  location: string;
  condition: TrafficCondition;
  averageSpeed: number;
  freeFlowSpeed: number;
  delayInMinutes: number;
  congestion: number;
  description: string;
  timestamp: Date;
  incidents: TrafficIncident[];
  roadSegments: RoadSegment[];
}

export interface TrafficIncident {
  id: string;
  type: string;
  description: string;
  location: GeoCoordinates;
  startTime: Date;
  endTime?: Date;
  severity: string;
  impactRadius: number;
}

export interface RoadSegment {
  id: string;
  name: string;
  startLocation: GeoCoordinates;
  endLocation: GeoCoordinates;
  currentSpeed: number;
  freeFlowSpeed: number;
  length: number;
  condition: TrafficCondition;
}

export interface WeatherData {
  location: string;
  condition: WeatherCondition;
  temperature: number;
  humidity: number;
  windSpeed: number;
  windDirection: string;
  visibility: number;
  pressure: number;
  description: string;
  timestamp: Date;
  forecast: WeatherForecast[];
}

export interface WeatherForecast {
  dateTime: Date;
  condition: WeatherCondition;
  temperature: number;
  precipitationProbability: number;
  windSpeed: number;
}
