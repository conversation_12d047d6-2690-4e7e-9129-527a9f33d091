<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Driver Creation</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select { width: 300px; padding: 8px; border: 1px solid #ccc; border-radius: 4px; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin-top: 20px; padding: 15px; border-radius: 4px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Test Driver Creation API</h1>

    <form id="driverForm">
        <div class="form-group">
            <label for="name">Name *</label>
            <input type="text" id="name" name="name" required value="Test Driver">
        </div>

        <div class="form-group">
            <label for="email">Email *</label>
            <input type="email" id="email" name="email" required value="<EMAIL>">
        </div>

        <div class="form-group">
            <label for="phone">Phone *</label>
            <input type="tel" id="phone" name="phone" required value="+1234567890">
        </div>

        <div class="form-group">
            <label for="vehicleType">Vehicle Type *</label>
            <select id="vehicleType" name="vehicleType" required>
                <option value="Car">Car</option>
                <option value="Motorcycle">Motorcycle</option>
                <option value="Van">Van</option>
                <option value="Truck">Truck</option>
            </select>
        </div>

        <div class="form-group">
            <label for="vehicleId">Vehicle ID</label>
            <input type="text" id="vehicleId" name="vehicleId" value="VH001">
        </div>

        <div class="form-group">
            <label for="status">Status *</label>
            <select id="status" name="status" required>
                <option value="0">Available</option>
                <option value="1">Busy</option>
                <option value="2">OnBreak</option>
                <option value="3">Offline</option>
            </select>
        </div>

        <div class="form-group">
            <label for="latitude">Latitude *</label>
            <input type="number" id="latitude" name="latitude" step="any" required value="36.8065">
        </div>

        <div class="form-group">
            <label for="longitude">Longitude *</label>
            <input type="number" id="longitude" name="longitude" step="any" required value="10.1815">
        </div>

        <div class="form-group">
            <label for="licenseNumber">License Number</label>
            <input type="text" id="licenseNumber" name="licenseNumber" value="DL123456">
        </div>

        <div class="form-group">
            <label for="licenseExpiryDate">License Expiry Date</label>
            <input type="date" id="licenseExpiryDate" name="licenseExpiryDate" value="2025-12-31">
        </div>

        <div class="form-group">
            <label for="maxDeliveriesPerDay">Max Deliveries Per Day</label>
            <input type="number" id="maxDeliveriesPerDay" name="maxDeliveriesPerDay" min="1" max="50" value="20">
        </div>

        <div class="form-group">
            <label for="preferredZones">Preferred Zones</label>
            <input type="text" id="preferredZones" name="preferredZones" value="Zone A, Zone B">
        </div>

        <div class="form-group">
            <label>
                <input type="checkbox" id="isAvailableForUrgentDeliveries" name="isAvailableForUrgentDeliveries" checked>
                Available for Urgent Deliveries
            </label>
        </div>

        <button type="submit">Create Driver</button>
    </form>

    <div id="result"></div>

    <script>
        document.getElementById('driverForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(e.target);
            const data = {
                name: formData.get('name'),
                email: formData.get('email'),
                phone: formData.get('phone'),
                vehicleType: formData.get('vehicleType'),
                vehicleId: formData.get('vehicleId') || null,
                status: parseInt(formData.get('status')),
                currentLocation: {
                    Latitude: parseFloat(formData.get('latitude')),
                    Longitude: parseFloat(formData.get('longitude'))
                },
                profilePictureUrl: null,
                licenseNumber: formData.get('licenseNumber') || null,
                licenseExpiryDate: formData.get('licenseExpiryDate') || null,
                isAvailableForUrgentDeliveries: formData.has('isAvailableForUrgentDeliveries'),
                preferredZones: formData.get('preferredZones') || null,
                maxDeliveriesPerDay: parseInt(formData.get('maxDeliveriesPerDay')) || 20
            };

            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<p>Creating driver...</p>';

            // Log the data being sent
            console.log('Driver data being sent:', JSON.stringify(data, null, 2));

            try {
                // First, get a token (you'll need to login first)
                const loginResponse = await fetch('http://localhost:5001/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin123',
                        password: 'Admin@123'
                    })
                });

                if (!loginResponse.ok) {
                    throw new Error('Login failed');
                }

                const loginData = await loginResponse.json();
                const token = loginData.token;

                // Now create the driver
                const response = await fetch('http://localhost:5001/api/drivers', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify(data)
                });

                const responseData = await response.text();
                let parsedData;
                try {
                    parsedData = JSON.parse(responseData);
                } catch {
                    parsedData = responseData;
                }

                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h3>✅ Driver Created Successfully!</h3>
                            <pre>${JSON.stringify(parsedData, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h3>❌ Error Creating Driver</h3>
                            <p><strong>Status:</strong> ${response.status} ${response.statusText}</p>
                            <pre>${JSON.stringify(parsedData, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h3>❌ Network Error</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        });
    </script>
</body>
</html>
