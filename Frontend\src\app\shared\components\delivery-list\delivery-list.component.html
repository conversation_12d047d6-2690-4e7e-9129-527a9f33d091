<div class="delivery-list">
  <div *ngIf="loading" class="text-center">
    <app-loading-spinner></app-loading-spinner>
  </div>

  <div *ngIf="!loading && deliveries.length === 0" class="text-center text-muted">
    <p>Aucune livraison trouvée</p>
  </div>

  <div *ngIf="!loading && deliveries.length > 0" class="table-responsive">
    <table class="table table-striped">
      <thead>
        <tr>
          <th>ID</th>
          <th>Client</th>
          <th>Adresse</th>
          <th>Statut</th>
          <th>Date</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let delivery of deliveries">
          <td>{{ delivery.id }}</td>
          <td>{{ delivery.customerName }}</td>
          <td>{{ delivery.address }}</td>
          <td>
            <span class="badge" [ngClass]="{
              'bg-success': delivery.status === 2,
              'bg-warning': delivery.status === 1,
              'bg-secondary': delivery.status === 0
            }">
              {{ delivery.status }}
            </span>
          </td>
          <td>{{ delivery.estimatedDeliveryTime | date:'short' }}</td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
