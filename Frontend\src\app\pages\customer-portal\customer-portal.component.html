<!-- Customer Portal Header -->
<div class="customer-portal">
  <div class="container-fluid">
    <!-- Header Section -->
    <div class="portal-header bg-primary text-white py-3 mb-4">
      <div class="container">
        <div class="row align-items-center">
          <div class="col-md-4">
            <h1 class="h4 mb-0">
              <i class="fa-solid fa-truck me-2"></i>
              Espace Client
            </h1>
          </div>
          <div class="col-md-4 text-center">
            <div class="input-group" style="max-width: 300px; margin: 0 auto;">
              <input
                type="text"
                class="form-control"
                placeholder="Numéro de suivi"
                [(ngModel)]="trackingId"
                (keyup.enter)="trackDelivery()"
              >
              <button
                class="btn btn-light"
                type="button"
                (click)="trackDelivery()"
              >
                <i class="fa-solid fa-search"></i>
              </button>
            </div>
          </div>
          <div class="col-md-4 text-md-end">
            <div class="d-flex align-items-center justify-content-md-end gap-3">
              <!-- User Info -->
              <div class="d-flex align-items-center" *ngIf="currentUser">
                <i class="fa-solid fa-user-circle fa-lg me-2"></i>
                <span class="small">{{ currentUser.firstName }} {{ currentUser.lastName }}</span>
              </div>

              <!-- Navigation Tabs -->
              <div class="btn-group" role="group">
                <button
                  type="button"
                  class="btn btn-outline-light btn-sm"
                  [class.active]="activeTab === 'tracking'"
                  (click)="setActiveTab('tracking')"
                >
                  <i class="fa-solid fa-search me-1"></i>
                  Suivi
                </button>
                <button
                  type="button"
                  class="btn btn-outline-light btn-sm"
                  [class.active]="activeTab === 'orders'"
                  (click)="setActiveTab('orders')"
                >
                  <i class="fa-solid fa-list me-1"></i>
                  Commandes
                </button>
                <button
                  type="button"
                  class="btn btn-outline-light btn-sm"
                  [class.active]="activeTab === 'profile'"
                  (click)="setActiveTab('profile')"
                >
                  <i class="fa-solid fa-user me-1"></i>
                  Profil
                </button>
              </div>

              <!-- Logout Button -->
              <button
                class="btn btn-outline-light btn-sm"
                (click)="logout()"
                title="Se déconnecter"
              >
                <i class="fa-solid fa-sign-out-alt me-1"></i>
                Déconnexion
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="container">
      <!-- Loading State -->
      <div *ngIf="loading" class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Chargement...</span>
        </div>
        <p class="mt-3 text-muted">Recherche de votre livraison...</p>
      </div>

      <!-- Error State -->
      <div *ngIf="error && !loading" class="alert alert-danger" role="alert">
        <i class="fa-solid fa-exclamation-triangle me-2"></i>
        {{ error }}
      </div>

      <!-- Tab Content -->

      <!-- Tracking Tab -->
      <div *ngIf="activeTab === 'tracking'">
        <!-- Delivery Information -->
        <div *ngIf="delivery && !loading && !error">
        <!-- Status Progress -->
        <div class="card mb-4">
          <div class="card-body">
            <h5 class="card-title mb-4">
              <i class="fa-solid fa-route me-2"></i>
              État de votre livraison
            </h5>

            <div class="progress-tracker">
              <div class="progress-line"></div>
              <div class="row">
                <div
                  *ngFor="let step of statusSteps; let i = index"
                  class="col-md-4"
                >
                  <div class="progress-step text-center">
                    <div
                      class="step-icon"
                      [class.completed]="isStatusCompleted(i)"
                      [class.active]="isStatusActive(i)"
                    >
                      <i [class]="step.icon"></i>
                    </div>
                    <h6 class="step-title mt-2"
                        [class.text-primary]="isStatusActive(i)"
                        [class.text-success]="isStatusCompleted(i) && !isStatusActive(i)">
                      {{ step.label }}
                    </h6>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Delivery Details -->
        <div class="row g-4">
          <!-- Main Information -->
          <div class="col-lg-8">
            <div class="card">
              <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                  <i class="fa-solid fa-info-circle me-2"></i>
                  Détails de la livraison
                </h5>
                <span
                  class="badge"
                  [class]="'bg-' + getStatusClass(delivery.status)"
                >
                  {{ getStatusText(delivery.status) }}
                </span>
              </div>
              <div class="card-body">
                <div class="row g-3">
                  <div class="col-md-6">
                    <label class="form-label text-muted">Numéro de commande</label>
                    <p class="fw-bold">{{ delivery.orderId }}</p>
                  </div>
                  <div class="col-md-6">
                    <label class="form-label text-muted">Client</label>
                    <p class="fw-bold">{{ delivery.customerName }}</p>
                  </div>
                  <div class="col-12">
                    <label class="form-label text-muted">Adresse de livraison</label>
                    <p class="fw-bold">
                      <i class="fa-solid fa-location-dot me-2 text-primary"></i>
                      {{ delivery.address }}
                    </p>
                  </div>
                  <div class="col-md-6">
                    <label class="form-label text-muted">Livreur</label>
                    <p class="fw-bold">
                      <i class="fa-solid fa-user me-2 text-info"></i>
                      {{ delivery.driverName }}
                    </p>
                  </div>
                  <div class="col-md-6">
                    <label class="form-label text-muted">Priorité</label>
                    <span
                      class="badge"
                      [class]="delivery.priority === 3 ? 'bg-danger' :
                               delivery.priority === 2 ? 'bg-warning' :
                               delivery.priority === 1 ? 'bg-info' : 'bg-secondary'"
                    >
                      {{ delivery.priority }}
                    </span>
                  </div>
                </div>

                <hr>

                <!-- Timeline -->
                <h6 class="text-muted mb-3">Chronologie</h6>
                <div class="timeline">
                  <div class="timeline-item">
                    <div class="timeline-marker bg-success"></div>
                    <div class="timeline-content">
                      <h6 class="mb-1">Commande créée</h6>
                      <p class="text-muted small mb-0">{{ formatDate(delivery.createdAt) }}</p>
                    </div>
                  </div>

                  <div *ngIf="delivery.pickupTime" class="timeline-item">
                    <div class="timeline-marker bg-info"></div>
                    <div class="timeline-content">
                      <h6 class="mb-1">Prise en charge</h6>
                      <p class="text-muted small mb-0">{{ formatDate(delivery.pickupTime) }}</p>
                    </div>
                  </div>

                  <div *ngIf="delivery.actualDeliveryTime" class="timeline-item">
                    <div class="timeline-marker bg-success"></div>
                    <div class="timeline-content">
                      <h6 class="mb-1">Livraison effectuée</h6>
                      <p class="text-muted small mb-0">{{ formatDate(delivery.actualDeliveryTime) }}</p>
                    </div>
                  </div>
                </div>

                <!-- Notes -->
                <div *ngIf="delivery.notes" class="mt-4">
                  <h6 class="text-muted mb-2">Instructions spéciales</h6>
                  <div class="alert alert-info">
                    <i class="fa-solid fa-note-sticky me-2"></i>
                    {{ delivery.notes }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Sidebar Information -->
          <div class="col-lg-4">
            <!-- Estimated Time -->
            <div class="card mb-4">
              <div class="card-body text-center">
                <i class="fa-solid fa-clock fa-2x text-primary mb-3"></i>
                <h5 class="card-title">Temps estimé</h5>
                <p class="h4 text-primary mb-2">{{ getEstimatedTimeRemaining() }}</p>
                <p class="text-muted small mb-0">
                  Livraison prévue le {{ formatDate(delivery.estimatedDeliveryTime) }}
                </p>
              </div>
            </div>

            <!-- Contact Information -->
            <div class="card mb-4">
              <div class="card-header">
                <h6 class="card-title mb-0">
                  <i class="fa-solid fa-phone me-2"></i>
                  Contact
                </h6>
              </div>
              <div class="card-body">
                <p class="mb-2">
                  <strong>Service client:</strong><br>
                  <a href="tel:+33123456789" class="text-decoration-none">
                    <i class="fa-solid fa-phone me-2"></i>
                    01 23 45 67 89
                  </a>
                </p>
                <p class="mb-0">
                  <strong>Email:</strong><br>
                  <a href="mailto:<EMAIL>" class="text-decoration-none">
                    <i class="fa-solid fa-envelope me-2"></i>
                    <EMAIL>
                  </a>
                </p>
              </div>
            </div>

            <!-- Customer Feedback -->
            <div *ngIf="delivery.status === 2" class="card">
              <div class="card-header">
                <h6 class="card-title mb-0">
                  <i class="fa-solid fa-star me-2"></i>
                  Votre avis
                </h6>
              </div>
              <div class="card-body">
                <div *ngIf="delivery.customerRating" class="mb-3">
                  <p class="mb-2">Note donnée:</p>
                  <div class="rating">
                    <i *ngFor="let star of [1,2,3,4,5]"
                       class="fa-solid fa-star"
                       [class.text-warning]="star <= delivery.customerRating!"
                       [class.text-muted]="star > delivery.customerRating!"></i>
                  </div>
                </div>
                <div *ngIf="delivery.customerFeedback" class="mb-3">
                  <p class="mb-2">Commentaire:</p>
                  <p class="text-muted">{{ delivery.customerFeedback }}</p>
                </div>
                <div *ngIf="!delivery.customerRating">
                  <p class="text-muted">Merci de nous faire part de votre expérience!</p>
                  <button class="btn btn-primary btn-sm">
                    <i class="fa-solid fa-star me-2"></i>
                    Donner mon avis
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        </div>

        <!-- No Tracking ID State -->
        <div *ngIf="!trackingId && !loading" class="text-center py-5">
          <i class="fa-solid fa-search fa-3x text-muted mb-3"></i>
          <h4 class="text-muted">Entrez votre numéro de suivi</h4>
          <p class="text-muted">Saisissez votre numéro de commande ou de suivi dans le champ ci-dessus</p>
        </div>
      </div>

      <!-- Orders Tab -->
      <div *ngIf="activeTab === 'orders'">
        <div class="row">
          <div class="col-12">
            <div class="card">
              <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                  <i class="fa-solid fa-list me-2"></i>
                  Mes Commandes
                </h5>
                <div class="d-flex gap-2">
                  <select class="form-select form-select-sm" [(ngModel)]="orderFilter" (change)="filterOrders()">
                    <option value="all">Toutes les commandes</option>
                    <option value="pending">En attente</option>
                    <option value="in-transit">En cours</option>
                    <option value="delivered">Livrées</option>
                    <option value="cancelled">Annulées</option>
                  </select>
                  <button class="btn btn-primary btn-sm" (click)="loadOrders()">
                    <i class="fa-solid fa-refresh me-1"></i>
                    Actualiser
                  </button>
                </div>
              </div>
              <div class="card-body">
                <div *ngIf="loadingOrders" class="text-center py-4">
                  <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Chargement...</span>
                  </div>
                </div>

                <div *ngIf="!loadingOrders && orders.length === 0" class="text-center py-4">
                  <i class="fa-solid fa-box-open fa-3x text-muted mb-3"></i>
                  <h5 class="text-muted">Aucune commande trouvée</h5>
                  <p class="text-muted">Vous n'avez pas encore passé de commande.</p>
                </div>

                <div *ngIf="!loadingOrders && orders.length > 0" class="table-responsive">
                  <table class="table table-hover">
                    <thead>
                      <tr>
                        <th>Commande</th>
                        <th>Date</th>
                        <th>Adresse</th>
                        <th>Statut</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr *ngFor="let order of filteredOrders">
                        <td>
                          <strong>{{ order.orderId }}</strong><br>
                          <small class="text-muted">{{ order.customerName }}</small>
                        </td>
                        <td>{{ formatDate(order.createdAt) }}</td>
                        <td>
                          <i class="fa-solid fa-location-dot me-1 text-primary"></i>
                          {{ order.address }}
                        </td>
                        <td>
                          <span class="badge" [class]="'bg-' + getStatusClass(order.status)">
                            {{ getStatusText(order.status) }}
                          </span>
                        </td>
                        <td>
                          <button
                            class="btn btn-outline-primary btn-sm me-2"
                            (click)="viewOrderDetails(order)"
                          >
                            <i class="fa-solid fa-eye me-1"></i>
                            Voir
                          </button>
                          <button
                            class="btn btn-outline-info btn-sm"
                            (click)="trackOrder(order.orderId)"
                          >
                            <i class="fa-solid fa-search me-1"></i>
                            Suivre
                          </button>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Profile Tab -->
      <div *ngIf="activeTab === 'profile'">
        <div class="row g-4">
          <!-- Profile Information -->
          <div class="col-lg-8">
            <div class="card">
              <div class="card-header">
                <h5 class="card-title mb-0">
                  <i class="fa-solid fa-user me-2"></i>
                  Informations Personnelles
                </h5>
              </div>
              <div class="card-body">
                <form [formGroup]="profileForm" (ngSubmit)="updateProfile()">
                  <div class="row g-3">
                    <div class="col-md-6">
                      <label class="form-label">Prénom</label>
                      <input type="text" class="form-control" formControlName="firstName">
                    </div>
                    <div class="col-md-6">
                      <label class="form-label">Nom</label>
                      <input type="text" class="form-control" formControlName="lastName">
                    </div>
                    <div class="col-12">
                      <label class="form-label">Email</label>
                      <input type="email" class="form-control" formControlName="email">
                    </div>
                    <div class="col-md-6">
                      <label class="form-label">Téléphone</label>
                      <input type="tel" class="form-control" formControlName="phone">
                    </div>
                    <div class="col-md-6">
                      <label class="form-label">Date de naissance</label>
                      <input type="date" class="form-control" formControlName="dateOfBirth">
                    </div>
                    <div class="col-12">
                      <label class="form-label">Adresse</label>
                      <textarea class="form-control" rows="3" formControlName="address"></textarea>
                    </div>
                  </div>

                  <hr>

                  <div class="d-flex justify-content-between">
                    <button type="button" class="btn btn-outline-secondary" (click)="resetProfile()">
                      <i class="fa-solid fa-undo me-1"></i>
                      Annuler
                    </button>
                    <button type="submit" class="btn btn-primary" [disabled]="!profileForm.valid || updatingProfile">
                      <i class="fa-solid fa-save me-1"></i>
                      <span *ngIf="!updatingProfile">Sauvegarder</span>
                      <span *ngIf="updatingProfile">Sauvegarde...</span>
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>

          <!-- Account Settings -->
          <div class="col-lg-4">
            <div class="card mb-4">
              <div class="card-header">
                <h6 class="card-title mb-0">
                  <i class="fa-solid fa-cog me-2"></i>
                  Paramètres du Compte
                </h6>
              </div>
              <div class="card-body">
                <div class="d-grid gap-2">
                  <button class="btn btn-outline-primary btn-sm" (click)="changePassword()">
                    <i class="fa-solid fa-key me-1"></i>
                    Changer le mot de passe
                  </button>
                  <button class="btn btn-outline-info btn-sm" (click)="manageNotifications()">
                    <i class="fa-solid fa-bell me-1"></i>
                    Notifications
                  </button>
                  <button class="btn btn-outline-secondary btn-sm" (click)="downloadData()">
                    <i class="fa-solid fa-download me-1"></i>
                    Télécharger mes données
                  </button>
                </div>
              </div>
            </div>

            <!-- Account Statistics -->
            <div class="card">
              <div class="card-header">
                <h6 class="card-title mb-0">
                  <i class="fa-solid fa-chart-bar me-2"></i>
                  Statistiques
                </h6>
              </div>
              <div class="card-body">
                <div class="row g-3 text-center">
                  <div class="col-6">
                    <div class="border rounded p-2">
                      <h4 class="text-primary mb-1">{{ userStats.totalOrders }}</h4>
                      <small class="text-muted">Commandes</small>
                    </div>
                  </div>
                  <div class="col-6">
                    <div class="border rounded p-2">
                      <h4 class="text-success mb-1">{{ userStats.deliveredOrders }}</h4>
                      <small class="text-muted">Livrées</small>
                    </div>
                  </div>
                  <div class="col-12">
                    <div class="border rounded p-2">
                      <h5 class="text-info mb-1">{{ userStats.averageRating }}/5</h5>
                      <small class="text-muted">Note moyenne donnée</small>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
