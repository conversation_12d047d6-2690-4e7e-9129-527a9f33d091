{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { BehaviorSubject, of, throwError } from 'rxjs';\nimport { map, tap, catchError } from 'rxjs/operators';\nimport { environment } from '../../../environments/environment';\nimport { UserRole } from '../models/user.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let AuthService = /*#__PURE__*/(() => {\n  class AuthService {\n    constructor(http) {\n      this.http = http;\n      this.apiUrl = `${environment.apiUrl}/auth`;\n      const storedUser = localStorage.getItem('currentUser');\n      const accessToken = localStorage.getItem('accessToken');\n      // Only restore user if we have both user data and a valid token\n      const user = storedUser && accessToken ? JSON.parse(storedUser) : null;\n      this.currentUserSubject = new BehaviorSubject(user);\n      this.currentUser$ = this.currentUserSubject.asObservable();\n      // Clear invalid data if user exists but no token\n      if (storedUser && !accessToken) {\n        this.clearAuthData();\n      }\n    }\n    get currentUserValue() {\n      return this.currentUserSubject.value;\n    }\n    get accessToken() {\n      return localStorage.getItem('accessToken');\n    }\n    get refreshToken() {\n      return localStorage.getItem('refreshToken');\n    }\n    login(loginRequest) {\n      return this.http.post(`${this.apiUrl}/login`, {\n        email: loginRequest.email,\n        password: loginRequest.password,\n        rememberMe: loginRequest.rememberMe || false\n      }).pipe(tap(response => {\n        if (response.success) {\n          // Convert role string to number for consistency\n          const user = {\n            ...response.user,\n            role: this.convertRoleStringToNumber(response.user.role)\n          };\n          // Store tokens\n          localStorage.setItem('accessToken', response.accessToken);\n          localStorage.setItem('refreshToken', response.refreshToken);\n          localStorage.setItem('currentUser', JSON.stringify(user));\n          // Update current user\n          this.currentUserSubject.next(user);\n        }\n      }), map(response => response.user), catchError(error => {\n        console.error('Login error:', error);\n        return throwError(error);\n      }));\n    }\n    register(registerRequest) {\n      return this.http.post(`${this.apiUrl}/register`, registerRequest).pipe(tap(response => {\n        if (response.success) {\n          // Convert role string to number for consistency\n          const user = {\n            ...response.user,\n            role: this.convertRoleStringToNumber(response.user.role)\n          };\n          // Store tokens\n          localStorage.setItem('accessToken', response.accessToken);\n          localStorage.setItem('refreshToken', response.refreshToken);\n          localStorage.setItem('currentUser', JSON.stringify(user));\n          // Update current user\n          this.currentUserSubject.next(user);\n        }\n      }), map(response => response.user), catchError(error => {\n        console.error('Registration error:', error);\n        return throwError(error);\n      }));\n    }\n    logout() {\n      const userId = this.currentUserValue?.id;\n      const currentAccessToken = this.accessToken;\n      // Clear local storage first\n      localStorage.removeItem('currentUser');\n      localStorage.removeItem('accessToken');\n      localStorage.removeItem('refreshToken');\n      this.currentUserSubject.next(null);\n      // Call logout API if user was logged in\n      if (userId && currentAccessToken) {\n        // Use the stored token for the logout call since we just cleared localStorage\n        const headers = new HttpHeaders().set('Authorization', `Bearer ${currentAccessToken}`);\n        return this.http.post(`${this.apiUrl}/logout`, {}, {\n          headers\n        }).pipe(catchError(error => {\n          console.error('Logout error:', error);\n          return of(null); // Don't fail logout on API error\n        }));\n      }\n\n      return of(null);\n    }\n    refreshAccessToken() {\n      const refreshToken = this.refreshToken;\n      if (!refreshToken) {\n        return throwError('No refresh token available');\n      }\n      return this.http.post(`${this.apiUrl}/refresh`, {\n        refreshToken\n      }).pipe(tap(response => {\n        if (response.success) {\n          localStorage.setItem('accessToken', response.accessToken);\n          localStorage.setItem('refreshToken', response.refreshToken);\n        }\n      }), catchError(error => {\n        console.error('Token refresh error:', error);\n        this.logout();\n        return throwError(error);\n      }));\n    }\n    getCurrentUser() {\n      return this.currentUserValue;\n    }\n    getCurrentUserFromApi() {\n      return this.http.get(`${this.apiUrl}/me`).pipe(map(response => response.user), catchError(error => {\n        console.error('Get current user error:', error);\n        return throwError(error);\n      }));\n    }\n    updateProfile(user) {\n      return this.http.put(`${this.apiUrl}/profile`, user).pipe(tap(response => {\n        if (response.success) {\n          // Update local user data\n          localStorage.setItem('currentUser', JSON.stringify(response.user));\n          this.currentUserSubject.next(response.user);\n        }\n      }), map(response => response.user), catchError(error => {\n        console.error('Update profile error:', error);\n        return throwError(error);\n      }));\n    }\n    isAuthenticated() {\n      return !!this.currentUserValue && !!this.accessToken;\n    }\n    hasRole(role) {\n      return this.currentUserValue?.role === role;\n    }\n    hasAnyRole(roles) {\n      return roles.includes(this.currentUserValue?.role || UserRole.Customer);\n    }\n    clearAuthData() {\n      localStorage.removeItem('currentUser');\n      localStorage.removeItem('accessToken');\n      localStorage.removeItem('refreshToken');\n      this.currentUserSubject.next(null);\n    }\n    convertRoleStringToNumber(roleString) {\n      if (typeof roleString === 'number') {\n        return roleString;\n      }\n      switch (roleString.toLowerCase()) {\n        case 'admin':\n          return UserRole.Admin;\n        case 'manager':\n          return UserRole.Manager;\n        case 'dispatcher':\n          return UserRole.Dispatcher;\n        case 'driver':\n          return UserRole.Driver;\n        case 'customer':\n          return UserRole.Customer;\n        default:\n          return UserRole.Customer;\n      }\n    }\n    static {\n      this.ɵfac = function AuthService_Factory(t) {\n        return new (t || AuthService)(i0.ɵɵinject(i1.HttpClient));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: AuthService,\n        factory: AuthService.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return AuthService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}