{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport { OptimizationObjective } from '../../core/models/route-optimization.model';\nimport { DeliveryStatus } from '../../core/models/delivery.model';\nimport { DriverStatus } from '../../core/models/driver.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../core/services/optimization.service\";\nimport * as i3 from \"../../core/services/delivery.service\";\nimport * as i4 from \"../../core/services/driver.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"../../shared/components/sidebar/sidebar.component\";\nimport * as i7 from \"../../shared/components/header/header.component\";\nimport * as i8 from \"../../shared/components/status-badge/status-badge.component\";\nimport * as i9 from \"../../shared/components/map-view/map-view.component\";\nfunction RouteOptimizationComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"div\", 8)(2, \"span\", 9);\n    i0.ɵɵtext(3, \"Chargement...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 10);\n    i0.ɵɵtext(5, \"Chargement des donn\\u00E9es...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction RouteOptimizationComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.error, \" \");\n  }\n}\nfunction RouteOptimizationComponent_ng_container_7_option_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 31);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const driver_r14 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", driver_r14.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", driver_r14.name, \" (\", driver_r14.vehicleType, \") \");\n  }\n}\nfunction RouteOptimizationComponent_ng_container_7_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵtext(1, \" Veuillez s\\u00E9lectionner un livreur \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RouteOptimizationComponent_ng_container_7_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"input\", 51);\n    i0.ɵɵlistener(\"change\", function RouteOptimizationComponent_ng_container_7_div_21_Template_input_change_1_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r17);\n      const delivery_r15 = restoredCtx.$implicit;\n      const ctx_r16 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r16.onDeliverySelectionChange(delivery_r15.id, $event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"label\", 52);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const delivery_r15 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"id\", \"delivery-\" + delivery_r15.id)(\"value\", delivery_r15.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"for\", \"delivery-\" + delivery_r15.id);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\" \", delivery_r15.customerName, \" - \", delivery_r15.address, \" \");\n  }\n}\nfunction RouteOptimizationComponent_ng_container_7_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵtext(1, \" Veuillez s\\u00E9lectionner au moins une livraison \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RouteOptimizationComponent_ng_container_7_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵtext(1, \" Aucune livraison en attente disponible \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RouteOptimizationComponent_ng_container_7_span_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 54);\n  }\n}\nfunction RouteOptimizationComponent_ng_container_7_div_54_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"div\", 62)(2, \"span\", 9);\n    i0.ɵɵtext(3, \"Chargement...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction RouteOptimizationComponent_ng_container_7_div_54_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"div\", 64);\n    i0.ɵɵelement(2, \"app-status-badge\", 65);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\")(4, \"p\", 66);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 66);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"number\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r19 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"status\", ctx_r19.getTrafficConditionText(ctx_r19.trafficData.condition))(\"variant\", ctx_r19.getTrafficConditionClass(ctx_r19.trafficData.condition));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Vitesse moyenne: \", i0.ɵɵpipeBind2(6, 4, ctx_r19.trafficData.averageSpeed, \"1.0-0\"), \" km/h\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Congestion: \", i0.ɵɵpipeBind2(9, 7, ctx_r19.trafficData.congestion * 100, \"1.0-0\"), \"%\");\n  }\n}\nfunction RouteOptimizationComponent_ng_container_7_div_54_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67);\n    i0.ɵɵtext(1, \" Donn\\u00E9es de trafic non disponibles \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RouteOptimizationComponent_ng_container_7_div_54_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"div\", 62)(2, \"span\", 9);\n    i0.ɵɵtext(3, \"Chargement...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction RouteOptimizationComponent_ng_container_7_div_54_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"div\", 64);\n    i0.ɵɵelement(2, \"app-status-badge\", 65);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\")(4, \"p\", 66);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 66);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"number\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"status\", ctx_r22.getWeatherConditionText(ctx_r22.weatherData.condition))(\"variant\", ctx_r22.getWeatherConditionClass(ctx_r22.weatherData.condition));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Temp\\u00E9rature: \", i0.ɵɵpipeBind2(6, 4, ctx_r22.weatherData.temperature, \"1.0-0\"), \"\\u00B0C\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Visibilit\\u00E9: \", i0.ɵɵpipeBind2(9, 7, ctx_r22.weatherData.visibility, \"1.0-0\"), \" km\");\n  }\n}\nfunction RouteOptimizationComponent_ng_container_7_div_54_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67);\n    i0.ɵɵtext(1, \" Donn\\u00E9es m\\u00E9t\\u00E9o non disponibles \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RouteOptimizationComponent_ng_container_7_div_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"div\", 15)(2, \"h5\", 16);\n    i0.ɵɵelement(3, \"i\", 56);\n    i0.ɵɵtext(4, \" Conditions externes \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 18)(6, \"div\", 32)(7, \"h6\", 57);\n    i0.ɵɵtext(8, \"Conditions de trafic\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, RouteOptimizationComponent_ng_container_7_div_54_div_9_Template, 4, 0, \"div\", 58);\n    i0.ɵɵtemplate(10, RouteOptimizationComponent_ng_container_7_div_54_div_10_Template, 10, 10, \"div\", 59);\n    i0.ɵɵtemplate(11, RouteOptimizationComponent_ng_container_7_div_54_div_11_Template, 2, 0, \"div\", 60);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\")(13, \"h6\", 57);\n    i0.ɵɵtext(14, \"Conditions m\\u00E9t\\u00E9o\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, RouteOptimizationComponent_ng_container_7_div_54_div_15_Template, 4, 0, \"div\", 58);\n    i0.ɵɵtemplate(16, RouteOptimizationComponent_ng_container_7_div_54_div_16_Template, 10, 10, \"div\", 59);\n    i0.ɵɵtemplate(17, RouteOptimizationComponent_ng_container_7_div_54_div_17_Template, 2, 0, \"div\", 60);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.trafficLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r9.trafficLoading && ctx_r9.trafficData);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r9.trafficLoading && !ctx_r9.trafficData);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r9.weatherLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r9.weatherLoading && ctx_r9.weatherData);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r9.weatherLoading && !ctx_r9.weatherData);\n  }\n}\nfunction RouteOptimizationComponent_ng_container_7_div_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 68);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 69);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"number\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r10.optimizedRoute.routes.length, \" arr\\u00EAts\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(5, 2, ctx_r10.optimizedRoute.optimizationScore, \"1.0-0\"), \"% d'efficacit\\u00E9\");\n  }\n}\nfunction RouteOptimizationComponent_ng_container_7_div_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"div\", 8)(2, \"span\", 9);\n    i0.ɵɵtext(3, \"Chargement...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 10);\n    i0.ɵɵtext(5, \"Optimisation de l'itin\\u00E9raire...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction RouteOptimizationComponent_ng_container_7_div_64_tr_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\")(8, \"span\", 80);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"td\");\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"td\");\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const stop_r25 = ctx.$implicit;\n    const ctx_r24 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stop_r25.order);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stop_r25.customerName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stop_r25.address);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"bg-danger\", stop_r25.priority === \"Urgent\")(\"bg-warning\", stop_r25.priority === \"High\")(\"bg-info\", stop_r25.priority === \"Medium\")(\"bg-secondary\", stop_r25.priority === \"Low\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", stop_r25.priority, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(12, 15, stop_r25.distance, \"1.1-1\"), \" km\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r24.formatTime(stop_r25.estimatedTime));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(17, 18, stop_r25.estimatedArrival, \"HH:mm\"));\n  }\n}\nfunction RouteOptimizationComponent_ng_container_7_div_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"app-map-view\", 70);\n    i0.ɵɵelementStart(2, \"div\", 71)(3, \"div\", 72)(4, \"div\", 73)(5, \"div\", 74)(6, \"div\", 75);\n    i0.ɵɵtext(7, \"Distance totale\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 76);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"number\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 73)(12, \"div\", 74)(13, \"div\", 75);\n    i0.ɵɵtext(14, \"Temps total\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 76);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 73)(18, \"div\", 74)(19, \"div\", 75);\n    i0.ɵɵtext(20, \"Efficacit\\u00E9 carburant\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 76);\n    i0.ɵɵtext(22);\n    i0.ɵɵpipe(23, \"number\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"div\", 73)(25, \"div\", 74)(26, \"div\", 75);\n    i0.ɵɵtext(27, \"M\\u00E9thode\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 76);\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(30, \"h6\", 20);\n    i0.ɵɵtext(31, \"Arr\\u00EAts planifi\\u00E9s\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 77)(33, \"table\", 78)(34, \"thead\")(35, \"tr\")(36, \"th\");\n    i0.ɵɵtext(37, \"#\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"th\");\n    i0.ɵɵtext(39, \"Client\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"th\");\n    i0.ɵɵtext(41, \"Adresse\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"th\");\n    i0.ɵɵtext(43, \"Priorit\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"th\");\n    i0.ɵɵtext(45, \"Distance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"th\");\n    i0.ɵɵtext(47, \"Temps estim\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"th\");\n    i0.ɵɵtext(49, \"Heure d'arriv\\u00E9e\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(50, \"tbody\");\n    i0.ɵɵtemplate(51, RouteOptimizationComponent_ng_container_7_div_64_tr_51_Template, 18, 21, \"tr\", 79);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r12 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"deliveries\", ctx_r12.getOptimizedDeliveries())(\"drivers\", ctx_r12.getSelectedDriverArray())(\"routes\", ctx_r12.getOptimizedRouteArray())(\"height\", 400);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(10, 9, ctx_r12.optimizedRoute.totalDistance, \"1.1-1\"), \" km\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r12.formatTime(ctx_r12.optimizedRoute.totalTime));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind2(23, 12, ctx_r12.optimizedRoute.fuelEfficiency, \"1.1-1\"), \"%\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r12.getOptimizationMethodText());\n    i0.ɵɵadvance(22);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r12.getSortedRouteStops());\n  }\n}\nfunction RouteOptimizationComponent_ng_container_7_div_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 81);\n    i0.ɵɵelement(1, \"i\", 82);\n    i0.ɵɵelementStart(2, \"h5\");\n    i0.ɵɵtext(3, \"Aucun itin\\u00E9raire optimis\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5, \"S\\u00E9lectionnez un livreur et des livraisons, puis cliquez sur \\\"Optimiser l'itin\\u00E9raire\\\"\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction RouteOptimizationComponent_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 12)(2, \"div\", 13)(3, \"div\", 14)(4, \"div\", 15)(5, \"h5\", 16);\n    i0.ɵɵelement(6, \"i\", 17);\n    i0.ɵɵtext(7, \" Param\\u00E8tres d'optimisation \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 18)(9, \"form\", 19);\n    i0.ɵɵlistener(\"ngSubmit\", function RouteOptimizationComponent_ng_container_7_Template_form_ngSubmit_9_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.optimizeRoute());\n    });\n    i0.ɵɵelementStart(10, \"div\", 20)(11, \"label\", 21);\n    i0.ɵɵtext(12, \"Livreur\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"select\", 22);\n    i0.ɵɵlistener(\"change\", function RouteOptimizationComponent_ng_container_7_Template_select_change_13_listener() {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r28 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r28.onDriverChange());\n    });\n    i0.ɵɵelementStart(14, \"option\", 23);\n    i0.ɵɵtext(15, \"S\\u00E9lectionnez un livreur\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(16, RouteOptimizationComponent_ng_container_7_option_16_Template, 2, 3, \"option\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(17, RouteOptimizationComponent_ng_container_7_div_17_Template, 2, 0, \"div\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 20)(19, \"label\", 26);\n    i0.ɵɵtext(20, \"Livraisons \\u00E0 optimiser\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(21, RouteOptimizationComponent_ng_container_7_div_21_Template, 4, 5, \"div\", 27);\n    i0.ɵɵtemplate(22, RouteOptimizationComponent_ng_container_7_div_22_Template, 2, 0, \"div\", 25);\n    i0.ɵɵtemplate(23, RouteOptimizationComponent_ng_container_7_div_23_Template, 2, 0, \"div\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 20)(25, \"label\", 29);\n    i0.ɵɵtext(26, \"Strat\\u00E9gie d'optimisation\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"select\", 30)(28, \"option\", 31);\n    i0.ɵɵtext(29, \"\\u00C9quilibr\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"option\", 31);\n    i0.ɵɵtext(31, \"Minimiser la distance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"option\", 31);\n    i0.ɵɵtext(33, \"Minimiser le temps\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"option\", 31);\n    i0.ɵɵtext(35, \"Maximiser les livraisons\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(36, \"div\", 32)(37, \"label\", 26);\n    i0.ɵɵtext(38, \"Options suppl\\u00E9mentaires\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"div\", 33);\n    i0.ɵɵelement(40, \"input\", 34);\n    i0.ɵɵelementStart(41, \"label\", 35);\n    i0.ɵɵtext(42, \" Prendre en compte le trafic \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(43, \"div\", 33);\n    i0.ɵɵelement(44, \"input\", 36);\n    i0.ɵɵelementStart(45, \"label\", 37);\n    i0.ɵɵtext(46, \" Prendre en compte la m\\u00E9t\\u00E9o \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(47, \"div\", 38);\n    i0.ɵɵelement(48, \"input\", 39);\n    i0.ɵɵelementStart(49, \"label\", 40);\n    i0.ɵɵtext(50, \" Prioriser les livraisons urgentes \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(51, \"button\", 41);\n    i0.ɵɵtemplate(52, RouteOptimizationComponent_ng_container_7_span_52_Template, 1, 0, \"span\", 42);\n    i0.ɵɵtext(53, \" Optimiser l'itin\\u00E9raire \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(54, RouteOptimizationComponent_ng_container_7_div_54_Template, 18, 6, \"div\", 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(55, \"div\", 44)(56, \"div\", 45)(57, \"div\", 46)(58, \"h5\", 16);\n    i0.ɵɵelement(59, \"i\", 47);\n    i0.ɵɵtext(60, \" Itin\\u00E9raire optimis\\u00E9 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(61, RouteOptimizationComponent_ng_container_7_div_61_Template, 6, 5, \"div\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(62, \"div\", 48);\n    i0.ɵɵtemplate(63, RouteOptimizationComponent_ng_container_7_div_63_Template, 6, 0, \"div\", 4);\n    i0.ɵɵtemplate(64, RouteOptimizationComponent_ng_container_7_div_64_Template, 52, 15, \"div\", 6);\n    i0.ɵɵtemplate(65, RouteOptimizationComponent_ng_container_7_div_65_Template, 6, 0, \"div\", 49);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    let tmp_2_0;\n    let tmp_4_0;\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"formGroup\", ctx_r2.optimizationForm);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.drivers);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx_r2.optimizationForm.get(\"driverId\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx_r2.optimizationForm.get(\"driverId\")) == null ? null : tmp_2_0.touched));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.pendingDeliveries);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx_r2.optimizationForm.get(\"deliveryIds\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx_r2.optimizationForm.get(\"deliveryIds\")) == null ? null : tmp_4_0.touched));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.pendingDeliveries.length === 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"value\", \"Balanced\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", \"MinimizeDistance\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", \"MinimizeTime\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", \"MaximizeDeliveries\");\n    i0.ɵɵadvance(17);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.optimizationForm.invalid || ctx_r2.optimizationLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.optimizationLoading);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedDriver);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.optimizedRoute);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.optimizationLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.optimizationLoading && ctx_r2.optimizedRoute);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.optimizationLoading && !ctx_r2.optimizedRoute);\n  }\n}\nexport class RouteOptimizationComponent {\n  constructor(formBuilder, optimizationService, deliveryService, driverService) {\n    this.formBuilder = formBuilder;\n    this.optimizationService = optimizationService;\n    this.deliveryService = deliveryService;\n    this.driverService = driverService;\n    this.drivers = [];\n    this.deliveries = [];\n    this.pendingDeliveries = [];\n    this.selectedDriver = null;\n    this.optimizedRoute = null;\n    this.trafficData = null;\n    this.weatherData = null;\n    this.loading = true;\n    this.driversLoading = false;\n    this.deliveriesLoading = false;\n    this.optimizationLoading = false;\n    this.trafficLoading = false;\n    this.weatherLoading = false;\n    this.error = '';\n    this.subscriptions = [];\n  }\n  ngOnInit() {\n    this.initForm();\n    this.loadData();\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n  initForm() {\n    this.optimizationForm = this.formBuilder.group({\n      driverId: ['', Validators.required],\n      deliveryIds: [[], Validators.required],\n      considerTraffic: [true],\n      considerWeather: [true],\n      prioritizeUrgent: [true],\n      strategy: [OptimizationObjective.BalanceWorkload]\n    });\n  }\n  loadData() {\n    this.loading = true;\n    this.driversLoading = true;\n    this.deliveriesLoading = true;\n    // Load drivers\n    this.subscriptions.push(this.driverService.getDrivers().subscribe({\n      next: drivers => {\n        this.drivers = drivers.filter(d => d.status === DriverStatus.Available);\n        this.driversLoading = false;\n        this.checkLoading();\n      },\n      error: err => {\n        console.error('Error loading drivers', err);\n        this.error = 'Erreur lors du chargement des livreurs';\n        this.driversLoading = false;\n        this.checkLoading();\n      }\n    }));\n    // Load deliveries\n    this.subscriptions.push(this.deliveryService.getDeliveries().subscribe({\n      next: deliveries => {\n        this.deliveries = deliveries;\n        this.pendingDeliveries = deliveries.filter(d => d.status === DeliveryStatus.Pending || d.status === DeliveryStatus.Delayed);\n        this.deliveriesLoading = false;\n        this.checkLoading();\n      },\n      error: err => {\n        console.error('Error loading deliveries', err);\n        this.error = 'Erreur lors du chargement des livraisons';\n        this.deliveriesLoading = false;\n        this.checkLoading();\n      }\n    }));\n  }\n  checkLoading() {\n    this.loading = this.driversLoading || this.deliveriesLoading;\n  }\n  onDriverChange() {\n    const driverId = this.optimizationForm.get('driverId')?.value;\n    if (!driverId) {\n      this.selectedDriver = null;\n      return;\n    }\n    this.selectedDriver = this.drivers.find(d => d.id === driverId) || null;\n    if (this.selectedDriver) {\n      // Load existing route for this driver\n      this.loadOptimizedRoute(driverId);\n      // Load traffic and weather data for driver's location\n      this.loadTrafficData(this.selectedDriver.currentLocation.latitude, this.selectedDriver.currentLocation.longitude);\n      this.loadWeatherData(this.selectedDriver.currentLocation.latitude, this.selectedDriver.currentLocation.longitude);\n    }\n  }\n  loadOptimizedRoute(driverId) {\n    this.optimizationLoading = true;\n    this.subscriptions.push(this.optimizationService.getOptimizedRoute(driverId).subscribe({\n      next: route => {\n        this.optimizedRoute = route;\n        this.optimizationLoading = false;\n      },\n      error: err => {\n        console.error('Error loading optimized route', err);\n        this.optimizedRoute = null;\n        this.optimizationLoading = false;\n      }\n    }));\n  }\n  loadTrafficData(latitude, longitude) {\n    this.trafficLoading = true;\n    this.subscriptions.push(this.optimizationService.getTrafficData(latitude, longitude).subscribe({\n      next: data => {\n        this.trafficData = data;\n        this.trafficLoading = false;\n      },\n      error: err => {\n        console.error('Error loading traffic data', err);\n        this.trafficData = null;\n        this.trafficLoading = false;\n      }\n    }));\n  }\n  loadWeatherData(latitude, longitude) {\n    this.weatherLoading = true;\n    this.subscriptions.push(this.optimizationService.getWeatherData(latitude, longitude).subscribe({\n      next: data => {\n        this.weatherData = data;\n        this.weatherLoading = false;\n      },\n      error: err => {\n        console.error('Error loading weather data', err);\n        this.weatherData = null;\n        this.weatherLoading = false;\n      }\n    }));\n  }\n  optimizeRoute() {\n    if (this.optimizationForm.invalid) {\n      return;\n    }\n    this.optimizationLoading = true;\n    const request = {\n      driverId: this.optimizationForm.get('driverId')?.value,\n      deliveryIds: this.optimizationForm.get('deliveryIds')?.value,\n      considerTraffic: this.optimizationForm.get('considerTraffic')?.value,\n      considerWeather: this.optimizationForm.get('considerWeather')?.value,\n      prioritizeUrgent: this.optimizationForm.get('prioritizeUrgent')?.value,\n      strategy: this.optimizationForm.get('strategy')?.value\n    };\n    this.subscriptions.push(this.optimizationService.optimizeRoute(request).subscribe({\n      next: route => {\n        this.optimizedRoute = route;\n        this.optimizationLoading = false;\n      },\n      error: err => {\n        console.error('Error optimizing route', err);\n        this.error = 'Erreur lors de l\\'optimisation de l\\'itinéraire';\n        this.optimizationLoading = false;\n      }\n    }));\n  }\n  getStrategyText(strategy) {\n    switch (strategy) {\n      case OptimizationObjective.MinimizeDistance:\n        return 'Minimiser la distance';\n      case OptimizationObjective.MinimizeTime:\n        return 'Minimiser le temps';\n      case OptimizationObjective.MaximizeDeliveries:\n        return 'Maximiser les livraisons';\n      case OptimizationObjective.BalanceWorkload:\n        return 'Équilibré';\n      default:\n        return strategy;\n    }\n  }\n  getTrafficConditionText(condition) {\n    switch (condition) {\n      case TrafficCondition.Light:\n        return 'Fluide';\n      case TrafficCondition.Moderate:\n        return 'Modéré';\n      case TrafficCondition.Heavy:\n        return 'Dense';\n      case TrafficCondition.Severe:\n        return 'Très dense';\n      default:\n        return condition.toString();\n    }\n  }\n  getWeatherConditionText(condition) {\n    switch (condition) {\n      case WeatherCondition.Clear:\n        return 'Dégagé';\n      case WeatherCondition.Cloudy:\n        return 'Nuageux';\n      case WeatherCondition.Rainy:\n        return 'Pluvieux';\n      case WeatherCondition.Snowy:\n        return 'Neigeux';\n      case WeatherCondition.Stormy:\n        return 'Orageux';\n      default:\n        return condition.toString();\n    }\n  }\n  getTrafficConditionClass(condition) {\n    switch (condition) {\n      case TrafficCondition.Light:\n        return 'success';\n      case TrafficCondition.Moderate:\n        return 'info';\n      case TrafficCondition.Heavy:\n        return 'warning';\n      case TrafficCondition.Severe:\n        return 'danger';\n      default:\n        return 'default';\n    }\n  }\n  getWeatherConditionClass(condition) {\n    switch (condition) {\n      case WeatherCondition.Clear:\n        return 'success';\n      case WeatherCondition.Cloudy:\n        return 'info';\n      case WeatherCondition.Rainy:\n        return 'warning';\n      case WeatherCondition.Snowy:\n        return 'warning';\n      case WeatherCondition.Stormy:\n        return 'danger';\n      default:\n        return 'default';\n    }\n  }\n  formatTime(minutes) {\n    const hours = Math.floor(minutes / 60);\n    const mins = Math.floor(minutes % 60);\n    if (hours > 0) {\n      return `${hours}h ${mins}min`;\n    } else {\n      return `${mins} min`;\n    }\n  }\n  onDeliverySelectionChange(deliveryId, event) {\n    const isChecked = event.target.checked;\n    const currentIds = this.optimizationForm.get('deliveryIds')?.value || [];\n    if (isChecked) {\n      if (!currentIds.includes(deliveryId)) {\n        this.optimizationForm.get('deliveryIds')?.setValue([...currentIds, deliveryId]);\n      }\n    } else {\n      this.optimizationForm.get('deliveryIds')?.setValue(currentIds.filter(id => id !== deliveryId));\n    }\n  }\n  getOptimizedDeliveries() {\n    if (!this.optimizedRoute) return [];\n    return this.deliveries.filter(d => this.optimizedRoute.routes.some(r => r.deliveryId === d.id));\n  }\n  getSelectedDriverArray() {\n    return this.selectedDriver ? [this.selectedDriver] : [];\n  }\n  getOptimizedRouteArray() {\n    return this.optimizedRoute ? [this.optimizedRoute] : [];\n  }\n  getSortedRouteStops() {\n    if (!this.optimizedRoute) return [];\n    return [...this.optimizedRoute.routes].sort((a, b) => a.order - b.order);\n  }\n  getOptimizationMethodText() {\n    if (!this.optimizedRoute) return '';\n    return this.getStrategyText(this.optimizedRoute.optimizationMethod);\n  }\n  static {\n    this.ɵfac = function RouteOptimizationComponent_Factory(t) {\n      return new (t || RouteOptimizationComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.OptimizationService), i0.ɵɵdirectiveInject(i3.DeliveryService), i0.ɵɵdirectiveInject(i4.DriverService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RouteOptimizationComponent,\n      selectors: [[\"app-route-optimization\"]],\n      decls: 8,\n      vars: 3,\n      consts: [[1, \"optimization-container\"], [1, \"optimization-content\"], [\"title\", \"Optimisation des itin\\u00E9raires\", \"subtitle\", \"Optimisez les trajets de vos livreurs pour maximiser l'efficacit\\u00E9\"], [1, \"optimization-body\", \"p-4\"], [\"class\", \"text-center py-5\", 4, \"ngIf\"], [\"class\", \"alert alert-danger\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"text-center\", \"py-5\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"mt-2\"], [1, \"alert\", \"alert-danger\"], [1, \"row\", \"g-4\"], [1, \"col-lg-4\"], [1, \"card\"], [1, \"card-header\"], [1, \"card-title\", \"mb-0\"], [1, \"fa-solid\", \"fa-sliders\", \"me-2\", \"text-primary\"], [1, \"card-body\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"mb-3\"], [\"for\", \"driverSelect\", 1, \"form-label\"], [\"id\", \"driverSelect\", \"formControlName\", \"driverId\", 1, \"form-select\", 3, \"change\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-danger small mt-1\", 4, \"ngIf\"], [1, \"form-label\"], [\"class\", \"form-check\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-muted small mt-1\", 4, \"ngIf\"], [\"for\", \"strategySelect\", 1, \"form-label\"], [\"id\", \"strategySelect\", \"formControlName\", \"strategy\", 1, \"form-select\"], [3, \"value\"], [1, \"mb-4\"], [1, \"form-check\", \"mb-2\"], [\"type\", \"checkbox\", \"id\", \"considerTraffic\", \"formControlName\", \"considerTraffic\", 1, \"form-check-input\"], [\"for\", \"considerTraffic\", 1, \"form-check-label\"], [\"type\", \"checkbox\", \"id\", \"considerWeather\", \"formControlName\", \"considerWeather\", 1, \"form-check-input\"], [\"for\", \"considerWeather\", 1, \"form-check-label\"], [1, \"form-check\"], [\"type\", \"checkbox\", \"id\", \"prioritizeUrgent\", \"formControlName\", \"prioritizeUrgent\", 1, \"form-check-input\"], [\"for\", \"prioritizeUrgent\", 1, \"form-check-label\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\", \"w-100\", 3, \"disabled\"], [\"class\", \"spinner-border spinner-border-sm me-2\", \"role\", \"status\", \"aria-hidden\", \"true\", 4, \"ngIf\"], [\"class\", \"card mt-4\", 4, \"ngIf\"], [1, \"col-lg-8\"], [1, \"card\", \"mb-4\"], [1, \"card-header\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"fa-solid\", \"fa-route\", \"me-2\", \"text-primary\"], [1, \"card-body\", \"p-0\"], [\"class\", \"text-center py-5 text-muted\", 4, \"ngIf\"], [1, \"text-danger\", \"small\", \"mt-1\"], [\"type\", \"checkbox\", 1, \"form-check-input\", 3, \"id\", \"value\", \"change\"], [1, \"form-check-label\", 3, \"for\"], [1, \"text-muted\", \"small\", \"mt-1\"], [\"role\", \"status\", \"aria-hidden\", \"true\", 1, \"spinner-border\", \"spinner-border-sm\", \"me-2\"], [1, \"card\", \"mt-4\"], [1, \"fa-solid\", \"fa-cloud\", \"me-2\", \"text-primary\"], [1, \"text-muted\", \"mb-2\"], [\"class\", \"text-center py-2\", 4, \"ngIf\"], [\"class\", \"d-flex align-items-center\", 4, \"ngIf\"], [\"class\", \"text-muted small\", 4, \"ngIf\"], [1, \"text-center\", \"py-2\"], [\"role\", \"status\", 1, \"spinner-border\", \"spinner-border-sm\", \"text-primary\"], [1, \"d-flex\", \"align-items-center\"], [1, \"me-3\"], [3, \"status\", \"variant\"], [1, \"mb-0\", \"small\"], [1, \"text-muted\", \"small\"], [1, \"badge\", \"bg-primary\", \"me-2\"], [1, \"badge\", \"bg-success\"], [3, \"deliveries\", \"drivers\", \"routes\", \"height\"], [1, \"p-4\"], [1, \"row\", \"g-3\", \"mb-4\"], [1, \"col-md-3\"], [1, \"metric-mini-card\"], [1, \"metric-mini-title\"], [1, \"metric-mini-value\"], [1, \"table-responsive\"], [1, \"table\", \"table-hover\"], [4, \"ngFor\", \"ngForOf\"], [1, \"badge\"], [1, \"text-center\", \"py-5\", \"text-muted\"], [1, \"fa-solid\", \"fa-route\", \"fa-3x\", \"mb-3\"]],\n      template: function RouteOptimizationComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"app-sidebar\");\n          i0.ɵɵelementStart(2, \"div\", 1);\n          i0.ɵɵelement(3, \"app-header\", 2);\n          i0.ɵɵelementStart(4, \"div\", 3);\n          i0.ɵɵtemplate(5, RouteOptimizationComponent_div_5_Template, 6, 0, \"div\", 4);\n          i0.ɵɵtemplate(6, RouteOptimizationComponent_div_6_Template, 2, 1, \"div\", 5);\n          i0.ɵɵtemplate(7, RouteOptimizationComponent_ng_container_7_Template, 66, 17, \"ng-container\", 6);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.error);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.error);\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i1.ɵNgNoValidate, i1.NgSelectOption, i1.ɵNgSelectMultipleOption, i1.CheckboxControlValueAccessor, i1.SelectControlValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i6.SidebarComponent, i7.HeaderComponent, i8.StatusBadgeComponent, i9.MapViewComponent, i5.DecimalPipe, i5.DatePipe],\n      styles: [\".optimization-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  height: 100vh;\\n  overflow: hidden;\\n}\\n\\n.optimization-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  overflow: hidden;\\n}\\n\\n.optimization-body[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  background-color: var(--gray-50);\\n}\\n\\n.metric-mini-card[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-radius: 0.5rem;\\n  padding: 1rem;\\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);\\n  border: 1px solid var(--gray-200);\\n}\\n\\n.metric-mini-title[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  color: var(--gray-500);\\n  margin-bottom: 0.5rem;\\n}\\n\\n.metric-mini-value[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: var(--gray-800);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvcGFnZXMvcm91dGUtb3B0aW1pemF0aW9uL3JvdXRlLW9wdGltaXphdGlvbi5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGFBQUE7RUFDQSxhQUFBO0VBQ0EsZ0JBQUE7QUFDRjs7QUFFQTtFQUNFLE9BQUE7RUFDQSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxnQkFBQTtBQUNGOztBQUVBO0VBQ0UsT0FBQTtFQUNBLGdCQUFBO0VBQ0EsZ0NBQUE7QUFDRjs7QUFFQTtFQUNFLHVCQUFBO0VBQ0EscUJBQUE7RUFDQSxhQUFBO0VBQ0EseUNBQUE7RUFDQSxpQ0FBQTtBQUNGOztBQUVBO0VBQ0Usa0JBQUE7RUFDQSxzQkFBQTtFQUNBLHFCQUFBO0FBQ0Y7O0FBRUE7RUFDRSxrQkFBQTtFQUNBLGdCQUFBO0VBQ0Esc0JBQUE7QUFDRiIsInNvdXJjZXNDb250ZW50IjpbIi5vcHRpbWl6YXRpb24tY29udGFpbmVyIHtcbiAgZGlzcGxheTogZmxleDtcbiAgaGVpZ2h0OiAxMDB2aDtcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcbn1cblxuLm9wdGltaXphdGlvbi1jb250ZW50IHtcbiAgZmxleDogMTtcbiAgZGlzcGxheTogZmxleDtcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcbn1cblxuLm9wdGltaXphdGlvbi1ib2R5IHtcbiAgZmxleDogMTtcbiAgb3ZlcmZsb3cteTogYXV0bztcbiAgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0tZ3JheS01MCk7XG59XG5cbi5tZXRyaWMtbWluaS1jYXJkIHtcbiAgYmFja2dyb3VuZC1jb2xvcjogd2hpdGU7XG4gIGJvcmRlci1yYWRpdXM6IDAuNXJlbTtcbiAgcGFkZGluZzogMXJlbTtcbiAgYm94LXNoYWRvdzogMCAxcHggMnB4IHJnYmEoMCwgMCwgMCwgMC4wNSk7XG4gIGJvcmRlcjogMXB4IHNvbGlkIHZhcigtLWdyYXktMjAwKTtcbn1cblxuLm1ldHJpYy1taW5pLXRpdGxlIHtcbiAgZm9udC1zaXplOiAwLjc1cmVtO1xuICBjb2xvcjogdmFyKC0tZ3JheS01MDApO1xuICBtYXJnaW4tYm90dG9tOiAwLjVyZW07XG59XG5cbi5tZXRyaWMtbWluaS12YWx1ZSB7XG4gIGZvbnQtc2l6ZTogMS4yNXJlbTtcbiAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgY29sb3I6IHZhcigtLWdyYXktODAwKTtcbn1cbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "OptimizationObjective", "DeliveryStatus", "DriverStatus", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "error", "ɵɵproperty", "driver_r14", "id", "ɵɵtextInterpolate2", "name", "vehicleType", "ɵɵlistener", "RouteOptimizationComponent_ng_container_7_div_21_Template_input_change_1_listener", "$event", "restoredCtx", "ɵɵrestoreView", "_r17", "delivery_r15", "$implicit", "ctx_r16", "ɵɵnextContext", "ɵɵresetView", "onDeliverySelectionChange", "customerName", "address", "ɵɵelement", "ctx_r19", "getTrafficConditionText", "trafficData", "condition", "getTrafficConditionClass", "ɵɵpipeBind2", "averageSpeed", "congestion", "ctx_r22", "getWeatherConditionText", "weatherData", "getWeatherConditionClass", "temperature", "visibility", "ɵɵtemplate", "RouteOptimizationComponent_ng_container_7_div_54_div_9_Template", "RouteOptimizationComponent_ng_container_7_div_54_div_10_Template", "RouteOptimizationComponent_ng_container_7_div_54_div_11_Template", "RouteOptimizationComponent_ng_container_7_div_54_div_15_Template", "RouteOptimizationComponent_ng_container_7_div_54_div_16_Template", "RouteOptimizationComponent_ng_container_7_div_54_div_17_Template", "ctx_r9", "trafficLoading", "weatherLoading", "ctx_r10", "optimizedRoute", "routes", "length", "optimizationScore", "ɵɵtextInterpolate", "stop_r25", "order", "ɵɵclassProp", "priority", "distance", "ctx_r24", "formatTime", "estimatedTime", "estimatedArrival", "RouteOptimizationComponent_ng_container_7_div_64_tr_51_Template", "ctx_r12", "getOptimizedDeliveries", "getSelectedDriverArray", "getOptimizedRouteArray", "totalDistance", "totalTime", "fuelEfficiency", "getOptimizationMethodText", "getSortedRouteStops", "ɵɵelementContainerStart", "RouteOptimizationComponent_ng_container_7_Template_form_ngSubmit_9_listener", "_r27", "ctx_r26", "optimizeRoute", "RouteOptimizationComponent_ng_container_7_Template_select_change_13_listener", "ctx_r28", "onDriverChange", "RouteOptimizationComponent_ng_container_7_option_16_Template", "RouteOptimizationComponent_ng_container_7_div_17_Template", "RouteOptimizationComponent_ng_container_7_div_21_Template", "RouteOptimizationComponent_ng_container_7_div_22_Template", "RouteOptimizationComponent_ng_container_7_div_23_Template", "RouteOptimizationComponent_ng_container_7_span_52_Template", "RouteOptimizationComponent_ng_container_7_div_54_Template", "RouteOptimizationComponent_ng_container_7_div_61_Template", "RouteOptimizationComponent_ng_container_7_div_63_Template", "RouteOptimizationComponent_ng_container_7_div_64_Template", "RouteOptimizationComponent_ng_container_7_div_65_Template", "ɵɵelementContainerEnd", "ctx_r2", "optimizationForm", "drivers", "tmp_2_0", "get", "invalid", "touched", "pendingDeliveries", "tmp_4_0", "optimizationLoading", "selectedDriver", "RouteOptimizationComponent", "constructor", "formBuilder", "optimizationService", "deliveryService", "driverService", "deliveries", "loading", "driversLoading", "deliveriesLoading", "subscriptions", "ngOnInit", "initForm", "loadData", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "group", "driverId", "required", "deliveryIds", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "prioritizeUrgent", "strategy", "BalanceWorkload", "push", "getDrivers", "subscribe", "next", "filter", "d", "status", "Available", "checkLoading", "err", "console", "getDeliveries", "Pending", "Delayed", "value", "find", "loadOptimizedRoute", "loadTrafficData", "currentLocation", "latitude", "longitude", "loadWeatherData", "getOptimizedRoute", "route", "getTrafficData", "data", "getWeatherData", "request", "getStrategyText", "MinimizeDistance", "MinimizeTime", "MaximizeDeliveries", "TrafficCondition", "Light", "Moderate", "Heavy", "Severe", "toString", "WeatherCondition", "Clear", "Cloudy", "Rainy", "Snowy", "Stormy", "minutes", "hours", "Math", "floor", "mins", "deliveryId", "event", "isChecked", "target", "checked", "currentIds", "includes", "setValue", "some", "r", "sort", "a", "b", "optimizationMethod", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "OptimizationService", "i3", "DeliveryService", "i4", "DriverService", "selectors", "decls", "vars", "consts", "template", "RouteOptimizationComponent_Template", "rf", "ctx", "RouteOptimizationComponent_div_5_Template", "RouteOptimizationComponent_div_6_Template", "RouteOptimizationComponent_ng_container_7_Template"], "sources": ["C:\\Users\\<USER>\\delivery-dash-optimiser\\frontend\\src\\app\\pages\\route-optimization\\route-optimization.component.ts", "C:\\Users\\<USER>\\delivery-dash-optimiser\\frontend\\src\\app\\pages\\route-optimization\\route-optimization.component.html"], "sourcesContent": ["import { Component, On<PERSON>ni<PERSON>, <PERSON><PERSON><PERSON>roy } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Subscription } from 'rxjs';\nimport { OptimizationService } from '../../core/services/optimization.service';\nimport { DeliveryService } from '../../core/services/delivery.service';\nimport { DriverService } from '../../core/services/driver.service';\nimport {\n  RouteOptimization,\n  RouteOptimizationRequest,\n  OptimizationObjective,\n  DeliveryTimePrediction,\n  DeliveryTimePredictionRequest,\n  TrafficData,\n  WeatherData\n} from '../../core/models/route-optimization.model';\nimport { Delivery, DeliveryStatus } from '../../core/models/delivery.model';\nimport { Driver, DriverStatus } from '../../core/models/driver.model';\n\n@Component({\n  selector: 'app-route-optimization',\n  templateUrl: './route-optimization.component.html',\n  styleUrls: ['./route-optimization.component.scss']\n})\nexport class RouteOptimizationComponent implements OnInit, OnDestroy {\n  drivers: Driver[] = [];\n  deliveries: Delivery[] = [];\n  pendingDeliveries: Delivery[] = [];\n  selectedDriver: Driver | null = null;\n  optimizedRoute: RouteOptimization | null = null;\n  trafficData: TrafficData | null = null;\n  weatherData: WeatherData | null = null;\n\n  optimizationForm!: FormGroup;\n\n  loading = true;\n  driversLoading = false;\n  deliveriesLoading = false;\n  optimizationLoading = false;\n  trafficLoading = false;\n  weatherLoading = false;\n\n  error = '';\n\n  private subscriptions: Subscription[] = [];\n\n  constructor(\n    private formBuilder: FormBuilder,\n    private optimizationService: OptimizationService,\n    private deliveryService: DeliveryService,\n    private driverService: DriverService\n  ) { }\n\n  ngOnInit(): void {\n    this.initForm();\n    this.loadData();\n  }\n\n  ngOnDestroy(): void {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n\n  private initForm(): void {\n    this.optimizationForm = this.formBuilder.group({\n      driverId: ['', Validators.required],\n      deliveryIds: [[], Validators.required],\n      considerTraffic: [true],\n      considerWeather: [true],\n      prioritizeUrgent: [true],\n      strategy: [OptimizationObjective.BalanceWorkload]\n    });\n  }\n\n  private loadData(): void {\n    this.loading = true;\n    this.driversLoading = true;\n    this.deliveriesLoading = true;\n\n    // Load drivers\n    this.subscriptions.push(\n      this.driverService.getDrivers().subscribe({\n        next: (drivers) => {\n          this.drivers = drivers.filter(d => d.status === DriverStatus.Available);\n          this.driversLoading = false;\n          this.checkLoading();\n        },\n        error: (err) => {\n          console.error('Error loading drivers', err);\n          this.error = 'Erreur lors du chargement des livreurs';\n          this.driversLoading = false;\n          this.checkLoading();\n        }\n      })\n    );\n\n    // Load deliveries\n    this.subscriptions.push(\n      this.deliveryService.getDeliveries().subscribe({\n        next: (deliveries) => {\n          this.deliveries = deliveries;\n          this.pendingDeliveries = deliveries.filter(d =>\n            d.status === DeliveryStatus.Pending || d.status === DeliveryStatus.Delayed\n          );\n          this.deliveriesLoading = false;\n          this.checkLoading();\n        },\n        error: (err) => {\n          console.error('Error loading deliveries', err);\n          this.error = 'Erreur lors du chargement des livraisons';\n          this.deliveriesLoading = false;\n          this.checkLoading();\n        }\n      })\n    );\n  }\n\n  private checkLoading(): void {\n    this.loading = this.driversLoading || this.deliveriesLoading;\n  }\n\n  onDriverChange(): void {\n    const driverId = this.optimizationForm.get('driverId')?.value;\n    if (!driverId) {\n      this.selectedDriver = null;\n      return;\n    }\n\n    this.selectedDriver = this.drivers.find(d => d.id === driverId) || null;\n\n    if (this.selectedDriver) {\n      // Load existing route for this driver\n      this.loadOptimizedRoute(driverId);\n\n      // Load traffic and weather data for driver's location\n      this.loadTrafficData(this.selectedDriver.currentLocation.latitude, this.selectedDriver.currentLocation.longitude);\n      this.loadWeatherData(this.selectedDriver.currentLocation.latitude, this.selectedDriver.currentLocation.longitude);\n    }\n  }\n\n  loadOptimizedRoute(driverId: string): void {\n    this.optimizationLoading = true;\n\n    this.subscriptions.push(\n      this.optimizationService.getOptimizedRoute(driverId).subscribe({\n        next: (route) => {\n          this.optimizedRoute = route;\n          this.optimizationLoading = false;\n        },\n        error: (err) => {\n          console.error('Error loading optimized route', err);\n          this.optimizedRoute = null;\n          this.optimizationLoading = false;\n        }\n      })\n    );\n  }\n\n  loadTrafficData(latitude: number, longitude: number): void {\n    this.trafficLoading = true;\n\n    this.subscriptions.push(\n      this.optimizationService.getTrafficData(latitude, longitude).subscribe({\n        next: (data) => {\n          this.trafficData = data;\n          this.trafficLoading = false;\n        },\n        error: (err) => {\n          console.error('Error loading traffic data', err);\n          this.trafficData = null;\n          this.trafficLoading = false;\n        }\n      })\n    );\n  }\n\n  loadWeatherData(latitude: number, longitude: number): void {\n    this.weatherLoading = true;\n\n    this.subscriptions.push(\n      this.optimizationService.getWeatherData(latitude, longitude).subscribe({\n        next: (data) => {\n          this.weatherData = data;\n          this.weatherLoading = false;\n        },\n        error: (err) => {\n          console.error('Error loading weather data', err);\n          this.weatherData = null;\n          this.weatherLoading = false;\n        }\n      })\n    );\n  }\n\n  optimizeRoute(): void {\n    if (this.optimizationForm.invalid) {\n      return;\n    }\n\n    this.optimizationLoading = true;\n\n    const request: RouteOptimizationRequest = {\n      driverId: this.optimizationForm.get('driverId')?.value,\n      deliveryIds: this.optimizationForm.get('deliveryIds')?.value,\n      considerTraffic: this.optimizationForm.get('considerTraffic')?.value,\n      considerWeather: this.optimizationForm.get('considerWeather')?.value,\n      prioritizeUrgent: this.optimizationForm.get('prioritizeUrgent')?.value,\n      strategy: this.optimizationForm.get('strategy')?.value\n    };\n\n    this.subscriptions.push(\n      this.optimizationService.optimizeRoute(request).subscribe({\n        next: (route) => {\n          this.optimizedRoute = route;\n          this.optimizationLoading = false;\n        },\n        error: (err) => {\n          console.error('Error optimizing route', err);\n          this.error = 'Erreur lors de l\\'optimisation de l\\'itinéraire';\n          this.optimizationLoading = false;\n        }\n      })\n    );\n  }\n\n  getStrategyText(strategy: OptimizationObjective): string {\n    switch (strategy) {\n      case OptimizationObjective.MinimizeDistance:\n        return 'Minimiser la distance';\n      case OptimizationObjective.MinimizeTime:\n        return 'Minimiser le temps';\n      case OptimizationObjective.MaximizeDeliveries:\n        return 'Maximiser les livraisons';\n      case OptimizationObjective.BalanceWorkload:\n        return 'Équilibré';\n      default:\n        return strategy;\n    }\n  }\n\n  getTrafficConditionText(condition: TrafficCondition): string {\n    switch (condition) {\n      case TrafficCondition.Light:\n        return 'Fluide';\n      case TrafficCondition.Moderate:\n        return 'Modéré';\n      case TrafficCondition.Heavy:\n        return 'Dense';\n      case TrafficCondition.Severe:\n        return 'Très dense';\n      default:\n        return condition.toString();\n    }\n  }\n\n  getWeatherConditionText(condition: WeatherCondition): string {\n    switch (condition) {\n      case WeatherCondition.Clear:\n        return 'Dégagé';\n      case WeatherCondition.Cloudy:\n        return 'Nuageux';\n      case WeatherCondition.Rainy:\n        return 'Pluvieux';\n      case WeatherCondition.Snowy:\n        return 'Neigeux';\n      case WeatherCondition.Stormy:\n        return 'Orageux';\n      default:\n        return condition.toString();\n    }\n  }\n\n  getTrafficConditionClass(condition: TrafficCondition): 'success' | 'danger' | 'warning' | 'info' | 'default' {\n    switch (condition) {\n      case TrafficCondition.Light:\n        return 'success';\n      case TrafficCondition.Moderate:\n        return 'info';\n      case TrafficCondition.Heavy:\n        return 'warning';\n      case TrafficCondition.Severe:\n        return 'danger';\n      default:\n        return 'default';\n    }\n  }\n\n  getWeatherConditionClass(condition: WeatherCondition): 'success' | 'danger' | 'warning' | 'info' | 'default' {\n    switch (condition) {\n      case WeatherCondition.Clear:\n        return 'success';\n      case WeatherCondition.Cloudy:\n        return 'info';\n      case WeatherCondition.Rainy:\n        return 'warning';\n      case WeatherCondition.Snowy:\n        return 'warning';\n      case WeatherCondition.Stormy:\n        return 'danger';\n      default:\n        return 'default';\n    }\n  }\n\n  formatTime(minutes: number): string {\n    const hours = Math.floor(minutes / 60);\n    const mins = Math.floor(minutes % 60);\n\n    if (hours > 0) {\n      return `${hours}h ${mins}min`;\n    } else {\n      return `${mins} min`;\n    }\n  }\n\n  onDeliverySelectionChange(deliveryId: string, event: any): void {\n    const isChecked = event.target.checked;\n    const currentIds = this.optimizationForm.get('deliveryIds')?.value || [];\n\n    if (isChecked) {\n      if (!currentIds.includes(deliveryId)) {\n        this.optimizationForm.get('deliveryIds')?.setValue([...currentIds, deliveryId]);\n      }\n    } else {\n      this.optimizationForm.get('deliveryIds')?.setValue(\n        currentIds.filter((id: string) => id !== deliveryId)\n      );\n    }\n  }\n\n  getOptimizedDeliveries(): any[] {\n    if (!this.optimizedRoute) return [];\n    return this.deliveries.filter(d =>\n      this.optimizedRoute!.routes.some(r => r.deliveryId === d.id)\n    );\n  }\n\n  getSelectedDriverArray(): any[] {\n    return this.selectedDriver ? [this.selectedDriver] : [];\n  }\n\n  getOptimizedRouteArray(): any[] {\n    return this.optimizedRoute ? [this.optimizedRoute] : [];\n  }\n\n  getSortedRouteStops(): any[] {\n    if (!this.optimizedRoute) return [];\n    return [...this.optimizedRoute.routes].sort((a, b) => a.order - b.order);\n  }\n\n  getOptimizationMethodText(): string {\n    if (!this.optimizedRoute) return '';\n    return this.getStrategyText(this.optimizedRoute.optimizationMethod as OptimizationObjective);\n  }\n}\n", "<div class=\"optimization-container\">\n  <app-sidebar></app-sidebar>\n\n  <div class=\"optimization-content\">\n    <app-header\n      title=\"Optimisation des itinéraires\"\n      subtitle=\"Optimisez les trajets de vos livreurs pour maximiser l'efficacité\"\n    ></app-header>\n\n    <div class=\"optimization-body p-4\">\n      <div *ngIf=\"loading\" class=\"text-center py-5\">\n        <div class=\"spinner-border text-primary\" role=\"status\">\n          <span class=\"visually-hidden\">Chargement...</span>\n        </div>\n        <p class=\"mt-2\">Chargement des données...</p>\n      </div>\n\n      <div *ngIf=\"error\" class=\"alert alert-danger\">\n        {{ error }}\n      </div>\n\n      <ng-container *ngIf=\"!loading && !error\">\n        <div class=\"row g-4\">\n          <!-- Optimization Form -->\n          <div class=\"col-lg-4\">\n            <div class=\"card\">\n              <div class=\"card-header\">\n                <h5 class=\"card-title mb-0\">\n                  <i class=\"fa-solid fa-sliders me-2 text-primary\"></i>\n                  Paramètres d'optimisation\n                </h5>\n              </div>\n              <div class=\"card-body\">\n                <form [formGroup]=\"optimizationForm\" (ngSubmit)=\"optimizeRoute()\">\n                  <!-- Driver Selection -->\n                  <div class=\"mb-3\">\n                    <label for=\"driverSelect\" class=\"form-label\">Livreur</label>\n                    <select\n                      id=\"driverSelect\"\n                      class=\"form-select\"\n                      formControlName=\"driverId\"\n                      (change)=\"onDriverChange()\"\n                    >\n                      <option value=\"\">Sélectionnez un livreur</option>\n                      <option *ngFor=\"let driver of drivers\" [value]=\"driver.id\">\n                        {{ driver.name }} ({{ driver.vehicleType }})\n                      </option>\n                    </select>\n                    <div *ngIf=\"optimizationForm.get('driverId')?.invalid && optimizationForm.get('driverId')?.touched\" class=\"text-danger small mt-1\">\n                      Veuillez sélectionner un livreur\n                    </div>\n                  </div>\n\n                  <!-- Deliveries Selection -->\n                  <div class=\"mb-3\">\n                    <label class=\"form-label\">Livraisons à optimiser</label>\n                    <div class=\"form-check\" *ngFor=\"let delivery of pendingDeliveries\">\n                      <input\n                        class=\"form-check-input\"\n                        type=\"checkbox\"\n                        [id]=\"'delivery-' + delivery.id\"\n                        [value]=\"delivery.id\"\n                        (change)=\"onDeliverySelectionChange(delivery.id, $event)\"\n                      >\n                      <label class=\"form-check-label\" [for]=\"'delivery-' + delivery.id\">\n                        {{ delivery.customerName }} - {{ delivery.address }}\n                      </label>\n                    </div>\n                    <div *ngIf=\"optimizationForm.get('deliveryIds')?.invalid && optimizationForm.get('deliveryIds')?.touched\" class=\"text-danger small mt-1\">\n                      Veuillez sélectionner au moins une livraison\n                    </div>\n                    <div *ngIf=\"pendingDeliveries.length === 0\" class=\"text-muted small mt-1\">\n                      Aucune livraison en attente disponible\n                    </div>\n                  </div>\n\n                  <!-- Optimization Strategy -->\n                  <div class=\"mb-3\">\n                    <label for=\"strategySelect\" class=\"form-label\">Stratégie d'optimisation</label>\n                    <select id=\"strategySelect\" class=\"form-select\" formControlName=\"strategy\">\n                      <option [value]=\"'Balanced'\">Équilibré</option>\n                      <option [value]=\"'MinimizeDistance'\">Minimiser la distance</option>\n                      <option [value]=\"'MinimizeTime'\">Minimiser le temps</option>\n                      <option [value]=\"'MaximizeDeliveries'\">Maximiser les livraisons</option>\n                    </select>\n                  </div>\n\n                  <!-- Additional Options -->\n                  <div class=\"mb-4\">\n                    <label class=\"form-label\">Options supplémentaires</label>\n                    <div class=\"form-check mb-2\">\n                      <input class=\"form-check-input\" type=\"checkbox\" id=\"considerTraffic\" formControlName=\"considerTraffic\">\n                      <label class=\"form-check-label\" for=\"considerTraffic\">\n                        Prendre en compte le trafic\n                      </label>\n                    </div>\n                    <div class=\"form-check mb-2\">\n                      <input class=\"form-check-input\" type=\"checkbox\" id=\"considerWeather\" formControlName=\"considerWeather\">\n                      <label class=\"form-check-label\" for=\"considerWeather\">\n                        Prendre en compte la météo\n                      </label>\n                    </div>\n                    <div class=\"form-check\">\n                      <input class=\"form-check-input\" type=\"checkbox\" id=\"prioritizeUrgent\" formControlName=\"prioritizeUrgent\">\n                      <label class=\"form-check-label\" for=\"prioritizeUrgent\">\n                        Prioriser les livraisons urgentes\n                      </label>\n                    </div>\n                  </div>\n\n                  <!-- Submit Button -->\n                  <button\n                    type=\"submit\"\n                    class=\"btn btn-primary w-100\"\n                    [disabled]=\"optimizationForm.invalid || optimizationLoading\"\n                  >\n                    <span *ngIf=\"optimizationLoading\" class=\"spinner-border spinner-border-sm me-2\" role=\"status\" aria-hidden=\"true\"></span>\n                    Optimiser l'itinéraire\n                  </button>\n                </form>\n              </div>\n            </div>\n\n            <!-- External Conditions -->\n            <div class=\"card mt-4\" *ngIf=\"selectedDriver\">\n              <div class=\"card-header\">\n                <h5 class=\"card-title mb-0\">\n                  <i class=\"fa-solid fa-cloud me-2 text-primary\"></i>\n                  Conditions externes\n                </h5>\n              </div>\n              <div class=\"card-body\">\n                <!-- Traffic Conditions -->\n                <div class=\"mb-4\">\n                  <h6 class=\"text-muted mb-2\">Conditions de trafic</h6>\n                  <div *ngIf=\"trafficLoading\" class=\"text-center py-2\">\n                    <div class=\"spinner-border spinner-border-sm text-primary\" role=\"status\">\n                      <span class=\"visually-hidden\">Chargement...</span>\n                    </div>\n                  </div>\n                  <div *ngIf=\"!trafficLoading && trafficData\" class=\"d-flex align-items-center\">\n                    <div class=\"me-3\">\n                      <app-status-badge\n                        [status]=\"getTrafficConditionText(trafficData.condition)\"\n                        [variant]=\"getTrafficConditionClass(trafficData.condition)\"\n                      ></app-status-badge>\n                    </div>\n                    <div>\n                      <p class=\"mb-0 small\">Vitesse moyenne: {{ trafficData.averageSpeed | number:'1.0-0' }} km/h</p>\n                      <p class=\"mb-0 small\">Congestion: {{ (trafficData.congestion * 100) | number:'1.0-0' }}%</p>\n                    </div>\n                  </div>\n                  <div *ngIf=\"!trafficLoading && !trafficData\" class=\"text-muted small\">\n                    Données de trafic non disponibles\n                  </div>\n                </div>\n\n                <!-- Weather Conditions -->\n                <div>\n                  <h6 class=\"text-muted mb-2\">Conditions météo</h6>\n                  <div *ngIf=\"weatherLoading\" class=\"text-center py-2\">\n                    <div class=\"spinner-border spinner-border-sm text-primary\" role=\"status\">\n                      <span class=\"visually-hidden\">Chargement...</span>\n                    </div>\n                  </div>\n                  <div *ngIf=\"!weatherLoading && weatherData\" class=\"d-flex align-items-center\">\n                    <div class=\"me-3\">\n                      <app-status-badge\n                        [status]=\"getWeatherConditionText(weatherData.condition)\"\n                        [variant]=\"getWeatherConditionClass(weatherData.condition)\"\n                      ></app-status-badge>\n                    </div>\n                    <div>\n                      <p class=\"mb-0 small\">Température: {{ weatherData.temperature | number:'1.0-0' }}°C</p>\n                      <p class=\"mb-0 small\">Visibilité: {{ weatherData.visibility | number:'1.0-0' }} km</p>\n                    </div>\n                  </div>\n                  <div *ngIf=\"!weatherLoading && !weatherData\" class=\"text-muted small\">\n                    Données météo non disponibles\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- Optimization Results -->\n          <div class=\"col-lg-8\">\n            <div class=\"card mb-4\">\n              <div class=\"card-header d-flex justify-content-between align-items-center\">\n                <h5 class=\"card-title mb-0\">\n                  <i class=\"fa-solid fa-route me-2 text-primary\"></i>\n                  Itinéraire optimisé\n                </h5>\n                <div *ngIf=\"optimizedRoute\">\n                  <span class=\"badge bg-primary me-2\">{{ optimizedRoute.routes.length }} arrêts</span>\n                  <span class=\"badge bg-success\">{{ optimizedRoute.optimizationScore | number:'1.0-0' }}% d'efficacité</span>\n                </div>\n              </div>\n              <div class=\"card-body p-0\">\n                <div *ngIf=\"optimizationLoading\" class=\"text-center py-5\">\n                  <div class=\"spinner-border text-primary\" role=\"status\">\n                    <span class=\"visually-hidden\">Chargement...</span>\n                  </div>\n                  <p class=\"mt-2\">Optimisation de l'itinéraire...</p>\n                </div>\n\n                <div *ngIf=\"!optimizationLoading && optimizedRoute\">\n                  <!-- Map View -->\n                  <app-map-view\n                    [deliveries]=\"getOptimizedDeliveries()\"\n                    [drivers]=\"getSelectedDriverArray()\"\n                    [routes]=\"getOptimizedRouteArray()\"\n                    [height]=\"400\"\n                  ></app-map-view>\n\n                  <!-- Route Summary -->\n                  <div class=\"p-4\">\n                    <div class=\"row g-3 mb-4\">\n                      <div class=\"col-md-3\">\n                        <div class=\"metric-mini-card\">\n                          <div class=\"metric-mini-title\">Distance totale</div>\n                          <div class=\"metric-mini-value\">{{ optimizedRoute.totalDistance | number:'1.1-1' }} km</div>\n                        </div>\n                      </div>\n                      <div class=\"col-md-3\">\n                        <div class=\"metric-mini-card\">\n                          <div class=\"metric-mini-title\">Temps total</div>\n                          <div class=\"metric-mini-value\">{{ formatTime(optimizedRoute.totalTime) }}</div>\n                        </div>\n                      </div>\n                      <div class=\"col-md-3\">\n                        <div class=\"metric-mini-card\">\n                          <div class=\"metric-mini-title\">Efficacité carburant</div>\n                          <div class=\"metric-mini-value\">{{ optimizedRoute.fuelEfficiency | number:'1.1-1' }}%</div>\n                        </div>\n                      </div>\n                      <div class=\"col-md-3\">\n                        <div class=\"metric-mini-card\">\n                          <div class=\"metric-mini-title\">Méthode</div>\n                          <div class=\"metric-mini-value\">{{ getOptimizationMethodText() }}</div>\n                        </div>\n                      </div>\n                    </div>\n\n                    <!-- Route Stops -->\n                    <h6 class=\"mb-3\">Arrêts planifiés</h6>\n                    <div class=\"table-responsive\">\n                      <table class=\"table table-hover\">\n                        <thead>\n                          <tr>\n                            <th>#</th>\n                            <th>Client</th>\n                            <th>Adresse</th>\n                            <th>Priorité</th>\n                            <th>Distance</th>\n                            <th>Temps estimé</th>\n                            <th>Heure d'arrivée</th>\n                          </tr>\n                        </thead>\n                        <tbody>\n                          <tr *ngFor=\"let stop of getSortedRouteStops()\">\n                            <td>{{ stop.order }}</td>\n                            <td>{{ stop.customerName }}</td>\n                            <td>{{ stop.address }}</td>\n                            <td>\n                              <span class=\"badge\"\n                                [class.bg-danger]=\"stop.priority === 'Urgent'\"\n                                [class.bg-warning]=\"stop.priority === 'High'\"\n                                [class.bg-info]=\"stop.priority === 'Medium'\"\n                                [class.bg-secondary]=\"stop.priority === 'Low'\"\n                              >\n                                {{ stop.priority }}\n                              </span>\n                            </td>\n                            <td>{{ stop.distance | number:'1.1-1' }} km</td>\n                            <td>{{ formatTime(stop.estimatedTime) }}</td>\n                            <td>{{ stop.estimatedArrival | date:'HH:mm' }}</td>\n                          </tr>\n                        </tbody>\n                      </table>\n                    </div>\n                  </div>\n                </div>\n\n                <div *ngIf=\"!optimizationLoading && !optimizedRoute\" class=\"text-center py-5 text-muted\">\n                  <i class=\"fa-solid fa-route fa-3x mb-3\"></i>\n                  <h5>Aucun itinéraire optimisé</h5>\n                  <p>Sélectionnez un livreur et des livraisons, puis cliquez sur \"Optimiser l'itinéraire\"</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </ng-container>\n    </div>\n  </div>\n</div>\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AAKnE,SAGEC,qBAAqB,QAKhB,4CAA4C;AACnD,SAAmBC,cAAc,QAAQ,kCAAkC;AAC3E,SAAiBC,YAAY,QAAQ,gCAAgC;;;;;;;;;;;;;ICN/DC,EAAA,CAAAC,cAAA,aAA8C;IAEZD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEpDH,EAAA,CAAAC,cAAA,YAAgB;IAAAD,EAAA,CAAAE,MAAA,qCAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAG/CH,EAAA,CAAAC,cAAA,cAA8C;IAC5CD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,KAAA,MACF;;;;;IAyBgBP,EAAA,CAAAC,cAAA,iBAA2D;IACzDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAF8BH,EAAA,CAAAQ,UAAA,UAAAC,UAAA,CAAAC,EAAA,CAAmB;IACxDV,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAW,kBAAA,MAAAF,UAAA,CAAAG,IAAA,QAAAH,UAAA,CAAAI,WAAA,OACF;;;;;IAEFb,EAAA,CAAAC,cAAA,cAAmI;IACjID,EAAA,CAAAE,MAAA,8CACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAMNH,EAAA,CAAAC,cAAA,cAAmE;IAM/DD,EAAA,CAAAc,UAAA,oBAAAC,kFAAAC,MAAA;MAAA,MAAAC,WAAA,GAAAjB,EAAA,CAAAkB,aAAA,CAAAC,IAAA;MAAA,MAAAC,YAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,OAAA,GAAAtB,EAAA,CAAAuB,aAAA;MAAA,OAAUvB,EAAA,CAAAwB,WAAA,CAAAF,OAAA,CAAAG,yBAAA,CAAAL,YAAA,CAAAV,EAAA,EAAAM,MAAA,CAA8C;IAAA,EAAC;IAL3DhB,EAAA,CAAAG,YAAA,EAMC;IACDH,EAAA,CAAAC,cAAA,gBAAkE;IAChED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;;;;IANNH,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAQ,UAAA,qBAAAY,YAAA,CAAAV,EAAA,CAAgC,UAAAU,YAAA,CAAAV,EAAA;IAIFV,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAQ,UAAA,sBAAAY,YAAA,CAAAV,EAAA,CAAiC;IAC/DV,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAW,kBAAA,MAAAS,YAAA,CAAAM,YAAA,SAAAN,YAAA,CAAAO,OAAA,MACF;;;;;IAEF3B,EAAA,CAAAC,cAAA,cAAyI;IACvID,EAAA,CAAAE,MAAA,0DACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IACNH,EAAA,CAAAC,cAAA,cAA0E;IACxED,EAAA,CAAAE,MAAA,+CACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IA2CNH,EAAA,CAAA4B,SAAA,eAAwH;;;;;IAmB1H5B,EAAA,CAAAC,cAAA,cAAqD;IAEnBD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAGtDH,EAAA,CAAAC,cAAA,cAA8E;IAE1ED,EAAA,CAAA4B,SAAA,2BAGoB;IACtB5B,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,UAAK;IACmBD,EAAA,CAAAE,MAAA,GAAqE;;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC/FH,EAAA,CAAAC,cAAA,YAAsB;IAAAD,EAAA,CAAAE,MAAA,GAAkE;;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAN1FH,EAAA,CAAAI,SAAA,GAAyD;IAAzDJ,EAAA,CAAAQ,UAAA,WAAAqB,OAAA,CAAAC,uBAAA,CAAAD,OAAA,CAAAE,WAAA,CAAAC,SAAA,EAAyD,YAAAH,OAAA,CAAAI,wBAAA,CAAAJ,OAAA,CAAAE,WAAA,CAAAC,SAAA;IAKrChC,EAAA,CAAAI,SAAA,GAAqE;IAArEJ,EAAA,CAAAK,kBAAA,sBAAAL,EAAA,CAAAkC,WAAA,OAAAL,OAAA,CAAAE,WAAA,CAAAI,YAAA,oBAAqE;IACrEnC,EAAA,CAAAI,SAAA,GAAkE;IAAlEJ,EAAA,CAAAK,kBAAA,iBAAAL,EAAA,CAAAkC,WAAA,OAAAL,OAAA,CAAAE,WAAA,CAAAK,UAAA,sBAAkE;;;;;IAG5FpC,EAAA,CAAAC,cAAA,cAAsE;IACpED,EAAA,CAAAE,MAAA,+CACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAMNH,EAAA,CAAAC,cAAA,cAAqD;IAEnBD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAGtDH,EAAA,CAAAC,cAAA,cAA8E;IAE1ED,EAAA,CAAA4B,SAAA,2BAGoB;IACtB5B,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,UAAK;IACmBD,EAAA,CAAAE,MAAA,GAA6D;;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACvFH,EAAA,CAAAC,cAAA,YAAsB;IAAAD,EAAA,CAAAE,MAAA,GAA4D;;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IANpFH,EAAA,CAAAI,SAAA,GAAyD;IAAzDJ,EAAA,CAAAQ,UAAA,WAAA6B,OAAA,CAAAC,uBAAA,CAAAD,OAAA,CAAAE,WAAA,CAAAP,SAAA,EAAyD,YAAAK,OAAA,CAAAG,wBAAA,CAAAH,OAAA,CAAAE,WAAA,CAAAP,SAAA;IAKrChC,EAAA,CAAAI,SAAA,GAA6D;IAA7DJ,EAAA,CAAAK,kBAAA,uBAAAL,EAAA,CAAAkC,WAAA,OAAAG,OAAA,CAAAE,WAAA,CAAAE,WAAA,sBAA6D;IAC7DzC,EAAA,CAAAI,SAAA,GAA4D;IAA5DJ,EAAA,CAAAK,kBAAA,sBAAAL,EAAA,CAAAkC,WAAA,OAAAG,OAAA,CAAAE,WAAA,CAAAG,UAAA,kBAA4D;;;;;IAGtF1C,EAAA,CAAAC,cAAA,cAAsE;IACpED,EAAA,CAAAE,MAAA,qDACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAvDZH,EAAA,CAAAC,cAAA,cAA8C;IAGxCD,EAAA,CAAA4B,SAAA,YAAmD;IACnD5B,EAAA,CAAAE,MAAA,4BACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEPH,EAAA,CAAAC,cAAA,cAAuB;IAGSD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrDH,EAAA,CAAA2C,UAAA,IAAAC,+DAAA,kBAIM;IACN5C,EAAA,CAAA2C,UAAA,KAAAE,gEAAA,oBAWM;IACN7C,EAAA,CAAA2C,UAAA,KAAAG,gEAAA,kBAEM;IACR9C,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,WAAK;IACyBD,EAAA,CAAAE,MAAA,kCAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjDH,EAAA,CAAA2C,UAAA,KAAAI,gEAAA,kBAIM;IACN/C,EAAA,CAAA2C,UAAA,KAAAK,gEAAA,oBAWM;IACNhD,EAAA,CAAA2C,UAAA,KAAAM,gEAAA,kBAEM;IACRjD,EAAA,CAAAG,YAAA,EAAM;;;;IA7CEH,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAQ,UAAA,SAAA0C,MAAA,CAAAC,cAAA,CAAoB;IAKpBnD,EAAA,CAAAI,SAAA,GAAoC;IAApCJ,EAAA,CAAAQ,UAAA,UAAA0C,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAnB,WAAA,CAAoC;IAYpC/B,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAQ,UAAA,UAAA0C,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAnB,WAAA,CAAqC;IAQrC/B,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAQ,UAAA,SAAA0C,MAAA,CAAAE,cAAA,CAAoB;IAKpBpD,EAAA,CAAAI,SAAA,GAAoC;IAApCJ,EAAA,CAAAQ,UAAA,UAAA0C,MAAA,CAAAE,cAAA,IAAAF,MAAA,CAAAX,WAAA,CAAoC;IAYpCvC,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAQ,UAAA,UAAA0C,MAAA,CAAAE,cAAA,KAAAF,MAAA,CAAAX,WAAA,CAAqC;;;;;IAgB7CvC,EAAA,CAAAC,cAAA,UAA4B;IACUD,EAAA,CAAAE,MAAA,GAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACpFH,EAAA,CAAAC,cAAA,eAA+B;IAAAD,EAAA,CAAAE,MAAA,GAAqE;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADvEH,EAAA,CAAAI,SAAA,GAAyC;IAAzCJ,EAAA,CAAAK,kBAAA,KAAAgD,OAAA,CAAAC,cAAA,CAAAC,MAAA,CAAAC,MAAA,iBAAyC;IAC9CxD,EAAA,CAAAI,SAAA,GAAqE;IAArEJ,EAAA,CAAAK,kBAAA,KAAAL,EAAA,CAAAkC,WAAA,OAAAmB,OAAA,CAAAC,cAAA,CAAAG,iBAAA,kCAAqE;;;;;IAItGzD,EAAA,CAAAC,cAAA,aAA0D;IAExBD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEpDH,EAAA,CAAAC,cAAA,YAAgB;IAAAD,EAAA,CAAAE,MAAA,2CAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAyD3CH,EAAA,CAAAC,cAAA,SAA+C;IACzCD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChCH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,SAAI;IAOAD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAETH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAuC;;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChDH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAoC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7CH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAA0C;;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAf/CH,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAA0D,iBAAA,CAAAC,QAAA,CAAAC,KAAA,CAAgB;IAChB5D,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAA0D,iBAAA,CAAAC,QAAA,CAAAjC,YAAA,CAAuB;IACvB1B,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAA0D,iBAAA,CAAAC,QAAA,CAAAhC,OAAA,CAAkB;IAGlB3B,EAAA,CAAAI,SAAA,GAA8C;IAA9CJ,EAAA,CAAA6D,WAAA,cAAAF,QAAA,CAAAG,QAAA,cAA8C,eAAAH,QAAA,CAAAG,QAAA,wBAAAH,QAAA,CAAAG,QAAA,+BAAAH,QAAA,CAAAG,QAAA;IAK9C9D,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAsD,QAAA,CAAAG,QAAA,MACF;IAEE9D,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAAK,kBAAA,KAAAL,EAAA,CAAAkC,WAAA,SAAAyB,QAAA,CAAAI,QAAA,kBAAuC;IACvC/D,EAAA,CAAAI,SAAA,GAAoC;IAApCJ,EAAA,CAAA0D,iBAAA,CAAAM,OAAA,CAAAC,UAAA,CAAAN,QAAA,CAAAO,aAAA,EAAoC;IACpClE,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAA0D,iBAAA,CAAA1D,EAAA,CAAAkC,WAAA,SAAAyB,QAAA,CAAAQ,gBAAA,WAA0C;;;;;IAtE1DnE,EAAA,CAAAC,cAAA,UAAoD;IAElDD,EAAA,CAAA4B,SAAA,uBAKgB;IAGhB5B,EAAA,CAAAC,cAAA,cAAiB;IAIsBD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACpDH,EAAA,CAAAC,cAAA,cAA+B;IAAAD,EAAA,CAAAE,MAAA,GAAsD;;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAG/FH,EAAA,CAAAC,cAAA,eAAsB;IAEaD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAChDH,EAAA,CAAAC,cAAA,eAA+B;IAAAD,EAAA,CAAAE,MAAA,IAA0C;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGnFH,EAAA,CAAAC,cAAA,eAAsB;IAEaD,EAAA,CAAAE,MAAA,iCAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACzDH,EAAA,CAAAC,cAAA,eAA+B;IAAAD,EAAA,CAAAE,MAAA,IAAqD;;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAG9FH,EAAA,CAAAC,cAAA,eAAsB;IAEaD,EAAA,CAAAE,MAAA,oBAAO;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC5CH,EAAA,CAAAC,cAAA,eAA+B;IAAAD,EAAA,CAAAE,MAAA,IAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAM5EH,EAAA,CAAAC,cAAA,cAAiB;IAAAD,EAAA,CAAAE,MAAA,kCAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtCH,EAAA,CAAAC,cAAA,eAA8B;IAIlBD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACVH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACfH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,qBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,yBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,4BAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAG5BH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAA2C,UAAA,KAAAyB,+DAAA,mBAiBK;IACPpE,EAAA,CAAAG,YAAA,EAAQ;;;;IArEZH,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAAQ,UAAA,eAAA6D,OAAA,CAAAC,sBAAA,GAAuC,YAAAD,OAAA,CAAAE,sBAAA,cAAAF,OAAA,CAAAG,sBAAA;IAYFxE,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAAK,kBAAA,KAAAL,EAAA,CAAAkC,WAAA,QAAAmC,OAAA,CAAAf,cAAA,CAAAmB,aAAA,kBAAsD;IAMtDzE,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAA0D,iBAAA,CAAAW,OAAA,CAAAJ,UAAA,CAAAI,OAAA,CAAAf,cAAA,CAAAoB,SAAA,EAA0C;IAM1C1E,EAAA,CAAAI,SAAA,GAAqD;IAArDJ,EAAA,CAAAK,kBAAA,KAAAL,EAAA,CAAAkC,WAAA,SAAAmC,OAAA,CAAAf,cAAA,CAAAqB,cAAA,gBAAqD;IAMrD3E,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAA0D,iBAAA,CAAAW,OAAA,CAAAO,yBAAA,GAAiC;IAqB3C5E,EAAA,CAAAI,SAAA,IAAwB;IAAxBJ,EAAA,CAAAQ,UAAA,YAAA6D,OAAA,CAAAQ,mBAAA,GAAwB;;;;;IAwBvD7E,EAAA,CAAAC,cAAA,cAAyF;IACvFD,EAAA,CAAA4B,SAAA,YAA4C;IAC5C5B,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,0CAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,uGAAoF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;;IA1QvGH,EAAA,CAAA8E,uBAAA,GAAyC;IACvC9E,EAAA,CAAAC,cAAA,cAAqB;IAMXD,EAAA,CAAA4B,SAAA,YAAqD;IACrD5B,EAAA,CAAAE,MAAA,uCACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEPH,EAAA,CAAAC,cAAA,cAAuB;IACgBD,EAAA,CAAAc,UAAA,sBAAAiE,4EAAA;MAAA/E,EAAA,CAAAkB,aAAA,CAAA8D,IAAA;MAAA,MAAAC,OAAA,GAAAjF,EAAA,CAAAuB,aAAA;MAAA,OAAYvB,EAAA,CAAAwB,WAAA,CAAAyD,OAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAE/DlF,EAAA,CAAAC,cAAA,eAAkB;IAC6BD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC5DH,EAAA,CAAAC,cAAA,kBAKC;IADCD,EAAA,CAAAc,UAAA,oBAAAqE,6EAAA;MAAAnF,EAAA,CAAAkB,aAAA,CAAA8D,IAAA;MAAA,MAAAI,OAAA,GAAApF,EAAA,CAAAuB,aAAA;MAAA,OAAUvB,EAAA,CAAAwB,WAAA,CAAA4D,OAAA,CAAAC,cAAA,EAAgB;IAAA,EAAC;IAE3BrF,EAAA,CAAAC,cAAA,kBAAiB;IAAAD,EAAA,CAAAE,MAAA,oCAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACjDH,EAAA,CAAA2C,UAAA,KAAA2C,4DAAA,qBAES;IACXtF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAA2C,UAAA,KAAA4C,yDAAA,kBAEM;IACRvF,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAAkB;IACUD,EAAA,CAAAE,MAAA,mCAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACxDH,EAAA,CAAA2C,UAAA,KAAA6C,yDAAA,kBAWM;IACNxF,EAAA,CAAA2C,UAAA,KAAA8C,yDAAA,kBAEM;IACNzF,EAAA,CAAA2C,UAAA,KAAA+C,yDAAA,kBAEM;IACR1F,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAAkB;IAC+BD,EAAA,CAAAE,MAAA,qCAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC/EH,EAAA,CAAAC,cAAA,kBAA2E;IAC5CD,EAAA,CAAAE,MAAA,2BAAS;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC/CH,EAAA,CAAAC,cAAA,kBAAqC;IAAAD,EAAA,CAAAE,MAAA,6BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACnEH,EAAA,CAAAC,cAAA,kBAAiC;IAAAD,EAAA,CAAAE,MAAA,0BAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC5DH,EAAA,CAAAC,cAAA,kBAAuC;IAAAD,EAAA,CAAAE,MAAA,gCAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAK5EH,EAAA,CAAAC,cAAA,eAAkB;IACUD,EAAA,CAAAE,MAAA,oCAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACzDH,EAAA,CAAAC,cAAA,eAA6B;IAC3BD,EAAA,CAAA4B,SAAA,iBAAuG;IACvG5B,EAAA,CAAAC,cAAA,iBAAsD;IACpDD,EAAA,CAAAE,MAAA,qCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAEVH,EAAA,CAAAC,cAAA,eAA6B;IAC3BD,EAAA,CAAA4B,SAAA,iBAAuG;IACvG5B,EAAA,CAAAC,cAAA,iBAAsD;IACpDD,EAAA,CAAAE,MAAA,8CACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAEVH,EAAA,CAAAC,cAAA,eAAwB;IACtBD,EAAA,CAAA4B,SAAA,iBAAyG;IACzG5B,EAAA,CAAAC,cAAA,iBAAuD;IACrDD,EAAA,CAAAE,MAAA,2CACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAKZH,EAAA,CAAAC,cAAA,kBAIC;IACCD,EAAA,CAAA2C,UAAA,KAAAgD,0DAAA,mBAAwH;IACxH3F,EAAA,CAAAE,MAAA,qCACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAMfH,EAAA,CAAA2C,UAAA,KAAAiD,yDAAA,mBA0DM;IACR5F,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAAsB;IAIdD,EAAA,CAAA4B,SAAA,aAAmD;IACnD5B,EAAA,CAAAE,MAAA,uCACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAA2C,UAAA,KAAAkD,yDAAA,iBAGM;IACR7F,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA2B;IACzBD,EAAA,CAAA2C,UAAA,KAAAmD,yDAAA,iBAKM;IAEN9F,EAAA,CAAA2C,UAAA,KAAAoD,yDAAA,mBA4EM;IAEN/F,EAAA,CAAA2C,UAAA,KAAAqD,yDAAA,kBAIM;IACRhG,EAAA,CAAAG,YAAA,EAAM;IAIdH,EAAA,CAAAiG,qBAAA,EAAe;;;;;;IApQCjG,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAQ,UAAA,cAAA0F,MAAA,CAAAC,gBAAA,CAA8B;IAWHnG,EAAA,CAAAI,SAAA,GAAU;IAAVJ,EAAA,CAAAQ,UAAA,YAAA0F,MAAA,CAAAE,OAAA,CAAU;IAIjCpG,EAAA,CAAAI,SAAA,GAA4F;IAA5FJ,EAAA,CAAAQ,UAAA,WAAA6F,OAAA,GAAAH,MAAA,CAAAC,gBAAA,CAAAG,GAAA,+BAAAD,OAAA,CAAAE,OAAA,OAAAF,OAAA,GAAAH,MAAA,CAAAC,gBAAA,CAAAG,GAAA,+BAAAD,OAAA,CAAAG,OAAA,EAA4F;IAQrDxG,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAQ,UAAA,YAAA0F,MAAA,CAAAO,iBAAA,CAAoB;IAY3DzG,EAAA,CAAAI,SAAA,GAAkG;IAAlGJ,EAAA,CAAAQ,UAAA,WAAAkG,OAAA,GAAAR,MAAA,CAAAC,gBAAA,CAAAG,GAAA,kCAAAI,OAAA,CAAAH,OAAA,OAAAG,OAAA,GAAAR,MAAA,CAAAC,gBAAA,CAAAG,GAAA,kCAAAI,OAAA,CAAAF,OAAA,EAAkG;IAGlGxG,EAAA,CAAAI,SAAA,GAAoC;IAApCJ,EAAA,CAAAQ,UAAA,SAAA0F,MAAA,CAAAO,iBAAA,CAAAjD,MAAA,OAAoC;IAShCxD,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAQ,UAAA,qBAAoB;IACpBR,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAQ,UAAA,6BAA4B;IAC5BR,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAQ,UAAA,yBAAwB;IACxBR,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAQ,UAAA,+BAA8B;IA+BxCR,EAAA,CAAAI,SAAA,IAA4D;IAA5DJ,EAAA,CAAAQ,UAAA,aAAA0F,MAAA,CAAAC,gBAAA,CAAAI,OAAA,IAAAL,MAAA,CAAAS,mBAAA,CAA4D;IAErD3G,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAQ,UAAA,SAAA0F,MAAA,CAAAS,mBAAA,CAAyB;IAQhB3G,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAQ,UAAA,SAAA0F,MAAA,CAAAU,cAAA,CAAoB;IAqElC5G,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAQ,UAAA,SAAA0F,MAAA,CAAA5C,cAAA,CAAoB;IAMpBtD,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAQ,UAAA,SAAA0F,MAAA,CAAAS,mBAAA,CAAyB;IAOzB3G,EAAA,CAAAI,SAAA,GAA4C;IAA5CJ,EAAA,CAAAQ,UAAA,UAAA0F,MAAA,CAAAS,mBAAA,IAAAT,MAAA,CAAA5C,cAAA,CAA4C;IA8E5CtD,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAQ,UAAA,UAAA0F,MAAA,CAAAS,mBAAA,KAAAT,MAAA,CAAA5C,cAAA,CAA6C;;;ADrQnE,OAAM,MAAOuD,0BAA0B;EAsBrCC,YACUC,WAAwB,EACxBC,mBAAwC,EACxCC,eAAgC,EAChCC,aAA4B;IAH5B,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,aAAa,GAAbA,aAAa;IAzBvB,KAAAd,OAAO,GAAa,EAAE;IACtB,KAAAe,UAAU,GAAe,EAAE;IAC3B,KAAAV,iBAAiB,GAAe,EAAE;IAClC,KAAAG,cAAc,GAAkB,IAAI;IACpC,KAAAtD,cAAc,GAA6B,IAAI;IAC/C,KAAAvB,WAAW,GAAuB,IAAI;IACtC,KAAAQ,WAAW,GAAuB,IAAI;IAItC,KAAA6E,OAAO,GAAG,IAAI;IACd,KAAAC,cAAc,GAAG,KAAK;IACtB,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAX,mBAAmB,GAAG,KAAK;IAC3B,KAAAxD,cAAc,GAAG,KAAK;IACtB,KAAAC,cAAc,GAAG,KAAK;IAEtB,KAAA7C,KAAK,GAAG,EAAE;IAEF,KAAAgH,aAAa,GAAmB,EAAE;EAOtC;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACC,QAAQ,EAAE;IACf,IAAI,CAACC,QAAQ,EAAE;EACjB;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACJ,aAAa,CAACK,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;EACtD;EAEQL,QAAQA,CAAA;IACd,IAAI,CAACtB,gBAAgB,GAAG,IAAI,CAACY,WAAW,CAACgB,KAAK,CAAC;MAC7CC,QAAQ,EAAE,CAAC,EAAE,EAAEpI,UAAU,CAACqI,QAAQ,CAAC;MACnCC,WAAW,EAAE,CAAC,EAAE,EAAEtI,UAAU,CAACqI,QAAQ,CAAC;MACtCE,eAAe,EAAE,CAAC,IAAI,CAAC;MACvBC,eAAe,EAAE,CAAC,IAAI,CAAC;MACvBC,gBAAgB,EAAE,CAAC,IAAI,CAAC;MACxBC,QAAQ,EAAE,CAACzI,qBAAqB,CAAC0I,eAAe;KACjD,CAAC;EACJ;EAEQb,QAAQA,CAAA;IACd,IAAI,CAACN,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACC,iBAAiB,GAAG,IAAI;IAE7B;IACA,IAAI,CAACC,aAAa,CAACiB,IAAI,CACrB,IAAI,CAACtB,aAAa,CAACuB,UAAU,EAAE,CAACC,SAAS,CAAC;MACxCC,IAAI,EAAGvC,OAAO,IAAI;QAChB,IAAI,CAACA,OAAO,GAAGA,OAAO,CAACwC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,MAAM,KAAK/I,YAAY,CAACgJ,SAAS,CAAC;QACvE,IAAI,CAAC1B,cAAc,GAAG,KAAK;QAC3B,IAAI,CAAC2B,YAAY,EAAE;MACrB,CAAC;MACDzI,KAAK,EAAG0I,GAAG,IAAI;QACbC,OAAO,CAAC3I,KAAK,CAAC,uBAAuB,EAAE0I,GAAG,CAAC;QAC3C,IAAI,CAAC1I,KAAK,GAAG,wCAAwC;QACrD,IAAI,CAAC8G,cAAc,GAAG,KAAK;QAC3B,IAAI,CAAC2B,YAAY,EAAE;MACrB;KACD,CAAC,CACH;IAED;IACA,IAAI,CAACzB,aAAa,CAACiB,IAAI,CACrB,IAAI,CAACvB,eAAe,CAACkC,aAAa,EAAE,CAACT,SAAS,CAAC;MAC7CC,IAAI,EAAGxB,UAAU,IAAI;QACnB,IAAI,CAACA,UAAU,GAAGA,UAAU;QAC5B,IAAI,CAACV,iBAAiB,GAAGU,UAAU,CAACyB,MAAM,CAACC,CAAC,IAC1CA,CAAC,CAACC,MAAM,KAAKhJ,cAAc,CAACsJ,OAAO,IAAIP,CAAC,CAACC,MAAM,KAAKhJ,cAAc,CAACuJ,OAAO,CAC3E;QACD,IAAI,CAAC/B,iBAAiB,GAAG,KAAK;QAC9B,IAAI,CAAC0B,YAAY,EAAE;MACrB,CAAC;MACDzI,KAAK,EAAG0I,GAAG,IAAI;QACbC,OAAO,CAAC3I,KAAK,CAAC,0BAA0B,EAAE0I,GAAG,CAAC;QAC9C,IAAI,CAAC1I,KAAK,GAAG,0CAA0C;QACvD,IAAI,CAAC+G,iBAAiB,GAAG,KAAK;QAC9B,IAAI,CAAC0B,YAAY,EAAE;MACrB;KACD,CAAC,CACH;EACH;EAEQA,YAAYA,CAAA;IAClB,IAAI,CAAC5B,OAAO,GAAG,IAAI,CAACC,cAAc,IAAI,IAAI,CAACC,iBAAiB;EAC9D;EAEAjC,cAAcA,CAAA;IACZ,MAAM2C,QAAQ,GAAG,IAAI,CAAC7B,gBAAgB,CAACG,GAAG,CAAC,UAAU,CAAC,EAAEgD,KAAK;IAC7D,IAAI,CAACtB,QAAQ,EAAE;MACb,IAAI,CAACpB,cAAc,GAAG,IAAI;MAC1B;;IAGF,IAAI,CAACA,cAAc,GAAG,IAAI,CAACR,OAAO,CAACmD,IAAI,CAACV,CAAC,IAAIA,CAAC,CAACnI,EAAE,KAAKsH,QAAQ,CAAC,IAAI,IAAI;IAEvE,IAAI,IAAI,CAACpB,cAAc,EAAE;MACvB;MACA,IAAI,CAAC4C,kBAAkB,CAACxB,QAAQ,CAAC;MAEjC;MACA,IAAI,CAACyB,eAAe,CAAC,IAAI,CAAC7C,cAAc,CAAC8C,eAAe,CAACC,QAAQ,EAAE,IAAI,CAAC/C,cAAc,CAAC8C,eAAe,CAACE,SAAS,CAAC;MACjH,IAAI,CAACC,eAAe,CAAC,IAAI,CAACjD,cAAc,CAAC8C,eAAe,CAACC,QAAQ,EAAE,IAAI,CAAC/C,cAAc,CAAC8C,eAAe,CAACE,SAAS,CAAC;;EAErH;EAEAJ,kBAAkBA,CAACxB,QAAgB;IACjC,IAAI,CAACrB,mBAAmB,GAAG,IAAI;IAE/B,IAAI,CAACY,aAAa,CAACiB,IAAI,CACrB,IAAI,CAACxB,mBAAmB,CAAC8C,iBAAiB,CAAC9B,QAAQ,CAAC,CAACU,SAAS,CAAC;MAC7DC,IAAI,EAAGoB,KAAK,IAAI;QACd,IAAI,CAACzG,cAAc,GAAGyG,KAAK;QAC3B,IAAI,CAACpD,mBAAmB,GAAG,KAAK;MAClC,CAAC;MACDpG,KAAK,EAAG0I,GAAG,IAAI;QACbC,OAAO,CAAC3I,KAAK,CAAC,+BAA+B,EAAE0I,GAAG,CAAC;QACnD,IAAI,CAAC3F,cAAc,GAAG,IAAI;QAC1B,IAAI,CAACqD,mBAAmB,GAAG,KAAK;MAClC;KACD,CAAC,CACH;EACH;EAEA8C,eAAeA,CAACE,QAAgB,EAAEC,SAAiB;IACjD,IAAI,CAACzG,cAAc,GAAG,IAAI;IAE1B,IAAI,CAACoE,aAAa,CAACiB,IAAI,CACrB,IAAI,CAACxB,mBAAmB,CAACgD,cAAc,CAACL,QAAQ,EAAEC,SAAS,CAAC,CAAClB,SAAS,CAAC;MACrEC,IAAI,EAAGsB,IAAI,IAAI;QACb,IAAI,CAAClI,WAAW,GAAGkI,IAAI;QACvB,IAAI,CAAC9G,cAAc,GAAG,KAAK;MAC7B,CAAC;MACD5C,KAAK,EAAG0I,GAAG,IAAI;QACbC,OAAO,CAAC3I,KAAK,CAAC,4BAA4B,EAAE0I,GAAG,CAAC;QAChD,IAAI,CAAClH,WAAW,GAAG,IAAI;QACvB,IAAI,CAACoB,cAAc,GAAG,KAAK;MAC7B;KACD,CAAC,CACH;EACH;EAEA0G,eAAeA,CAACF,QAAgB,EAAEC,SAAiB;IACjD,IAAI,CAACxG,cAAc,GAAG,IAAI;IAE1B,IAAI,CAACmE,aAAa,CAACiB,IAAI,CACrB,IAAI,CAACxB,mBAAmB,CAACkD,cAAc,CAACP,QAAQ,EAAEC,SAAS,CAAC,CAAClB,SAAS,CAAC;MACrEC,IAAI,EAAGsB,IAAI,IAAI;QACb,IAAI,CAAC1H,WAAW,GAAG0H,IAAI;QACvB,IAAI,CAAC7G,cAAc,GAAG,KAAK;MAC7B,CAAC;MACD7C,KAAK,EAAG0I,GAAG,IAAI;QACbC,OAAO,CAAC3I,KAAK,CAAC,4BAA4B,EAAE0I,GAAG,CAAC;QAChD,IAAI,CAAC1G,WAAW,GAAG,IAAI;QACvB,IAAI,CAACa,cAAc,GAAG,KAAK;MAC7B;KACD,CAAC,CACH;EACH;EAEA8B,aAAaA,CAAA;IACX,IAAI,IAAI,CAACiB,gBAAgB,CAACI,OAAO,EAAE;MACjC;;IAGF,IAAI,CAACI,mBAAmB,GAAG,IAAI;IAE/B,MAAMwD,OAAO,GAA6B;MACxCnC,QAAQ,EAAE,IAAI,CAAC7B,gBAAgB,CAACG,GAAG,CAAC,UAAU,CAAC,EAAEgD,KAAK;MACtDpB,WAAW,EAAE,IAAI,CAAC/B,gBAAgB,CAACG,GAAG,CAAC,aAAa,CAAC,EAAEgD,KAAK;MAC5DnB,eAAe,EAAE,IAAI,CAAChC,gBAAgB,CAACG,GAAG,CAAC,iBAAiB,CAAC,EAAEgD,KAAK;MACpElB,eAAe,EAAE,IAAI,CAACjC,gBAAgB,CAACG,GAAG,CAAC,iBAAiB,CAAC,EAAEgD,KAAK;MACpEjB,gBAAgB,EAAE,IAAI,CAAClC,gBAAgB,CAACG,GAAG,CAAC,kBAAkB,CAAC,EAAEgD,KAAK;MACtEhB,QAAQ,EAAE,IAAI,CAACnC,gBAAgB,CAACG,GAAG,CAAC,UAAU,CAAC,EAAEgD;KAClD;IAED,IAAI,CAAC/B,aAAa,CAACiB,IAAI,CACrB,IAAI,CAACxB,mBAAmB,CAAC9B,aAAa,CAACiF,OAAO,CAAC,CAACzB,SAAS,CAAC;MACxDC,IAAI,EAAGoB,KAAK,IAAI;QACd,IAAI,CAACzG,cAAc,GAAGyG,KAAK;QAC3B,IAAI,CAACpD,mBAAmB,GAAG,KAAK;MAClC,CAAC;MACDpG,KAAK,EAAG0I,GAAG,IAAI;QACbC,OAAO,CAAC3I,KAAK,CAAC,wBAAwB,EAAE0I,GAAG,CAAC;QAC5C,IAAI,CAAC1I,KAAK,GAAG,iDAAiD;QAC9D,IAAI,CAACoG,mBAAmB,GAAG,KAAK;MAClC;KACD,CAAC,CACH;EACH;EAEAyD,eAAeA,CAAC9B,QAA+B;IAC7C,QAAQA,QAAQ;MACd,KAAKzI,qBAAqB,CAACwK,gBAAgB;QACzC,OAAO,uBAAuB;MAChC,KAAKxK,qBAAqB,CAACyK,YAAY;QACrC,OAAO,oBAAoB;MAC7B,KAAKzK,qBAAqB,CAAC0K,kBAAkB;QAC3C,OAAO,0BAA0B;MACnC,KAAK1K,qBAAqB,CAAC0I,eAAe;QACxC,OAAO,WAAW;MACpB;QACE,OAAOD,QAAQ;;EAErB;EAEAxG,uBAAuBA,CAACE,SAA2B;IACjD,QAAQA,SAAS;MACf,KAAKwI,gBAAgB,CAACC,KAAK;QACzB,OAAO,QAAQ;MACjB,KAAKD,gBAAgB,CAACE,QAAQ;QAC5B,OAAO,QAAQ;MACjB,KAAKF,gBAAgB,CAACG,KAAK;QACzB,OAAO,OAAO;MAChB,KAAKH,gBAAgB,CAACI,MAAM;QAC1B,OAAO,YAAY;MACrB;QACE,OAAO5I,SAAS,CAAC6I,QAAQ,EAAE;;EAEjC;EAEAvI,uBAAuBA,CAACN,SAA2B;IACjD,QAAQA,SAAS;MACf,KAAK8I,gBAAgB,CAACC,KAAK;QACzB,OAAO,QAAQ;MACjB,KAAKD,gBAAgB,CAACE,MAAM;QAC1B,OAAO,SAAS;MAClB,KAAKF,gBAAgB,CAACG,KAAK;QACzB,OAAO,UAAU;MACnB,KAAKH,gBAAgB,CAACI,KAAK;QACzB,OAAO,SAAS;MAClB,KAAKJ,gBAAgB,CAACK,MAAM;QAC1B,OAAO,SAAS;MAClB;QACE,OAAOnJ,SAAS,CAAC6I,QAAQ,EAAE;;EAEjC;EAEA5I,wBAAwBA,CAACD,SAA2B;IAClD,QAAQA,SAAS;MACf,KAAKwI,gBAAgB,CAACC,KAAK;QACzB,OAAO,SAAS;MAClB,KAAKD,gBAAgB,CAACE,QAAQ;QAC5B,OAAO,MAAM;MACf,KAAKF,gBAAgB,CAACG,KAAK;QACzB,OAAO,SAAS;MAClB,KAAKH,gBAAgB,CAACI,MAAM;QAC1B,OAAO,QAAQ;MACjB;QACE,OAAO,SAAS;;EAEtB;EAEApI,wBAAwBA,CAACR,SAA2B;IAClD,QAAQA,SAAS;MACf,KAAK8I,gBAAgB,CAACC,KAAK;QACzB,OAAO,SAAS;MAClB,KAAKD,gBAAgB,CAACE,MAAM;QAC1B,OAAO,MAAM;MACf,KAAKF,gBAAgB,CAACG,KAAK;QACzB,OAAO,SAAS;MAClB,KAAKH,gBAAgB,CAACI,KAAK;QACzB,OAAO,SAAS;MAClB,KAAKJ,gBAAgB,CAACK,MAAM;QAC1B,OAAO,QAAQ;MACjB;QACE,OAAO,SAAS;;EAEtB;EAEAlH,UAAUA,CAACmH,OAAe;IACxB,MAAMC,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACtC,MAAMI,IAAI,GAAGF,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IAErC,IAAIC,KAAK,GAAG,CAAC,EAAE;MACb,OAAO,GAAGA,KAAK,KAAKG,IAAI,KAAK;KAC9B,MAAM;MACL,OAAO,GAAGA,IAAI,MAAM;;EAExB;EAEA/J,yBAAyBA,CAACgK,UAAkB,EAAEC,KAAU;IACtD,MAAMC,SAAS,GAAGD,KAAK,CAACE,MAAM,CAACC,OAAO;IACtC,MAAMC,UAAU,GAAG,IAAI,CAAC3F,gBAAgB,CAACG,GAAG,CAAC,aAAa,CAAC,EAAEgD,KAAK,IAAI,EAAE;IAExE,IAAIqC,SAAS,EAAE;MACb,IAAI,CAACG,UAAU,CAACC,QAAQ,CAACN,UAAU,CAAC,EAAE;QACpC,IAAI,CAACtF,gBAAgB,CAACG,GAAG,CAAC,aAAa,CAAC,EAAE0F,QAAQ,CAAC,CAAC,GAAGF,UAAU,EAAEL,UAAU,CAAC,CAAC;;KAElF,MAAM;MACL,IAAI,CAACtF,gBAAgB,CAACG,GAAG,CAAC,aAAa,CAAC,EAAE0F,QAAQ,CAChDF,UAAU,CAAClD,MAAM,CAAElI,EAAU,IAAKA,EAAE,KAAK+K,UAAU,CAAC,CACrD;;EAEL;EAEAnH,sBAAsBA,CAAA;IACpB,IAAI,CAAC,IAAI,CAAChB,cAAc,EAAE,OAAO,EAAE;IACnC,OAAO,IAAI,CAAC6D,UAAU,CAACyB,MAAM,CAACC,CAAC,IAC7B,IAAI,CAACvF,cAAe,CAACC,MAAM,CAAC0I,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACT,UAAU,KAAK5C,CAAC,CAACnI,EAAE,CAAC,CAC7D;EACH;EAEA6D,sBAAsBA,CAAA;IACpB,OAAO,IAAI,CAACqC,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc,CAAC,GAAG,EAAE;EACzD;EAEApC,sBAAsBA,CAAA;IACpB,OAAO,IAAI,CAAClB,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc,CAAC,GAAG,EAAE;EACzD;EAEAuB,mBAAmBA,CAAA;IACjB,IAAI,CAAC,IAAI,CAACvB,cAAc,EAAE,OAAO,EAAE;IACnC,OAAO,CAAC,GAAG,IAAI,CAACA,cAAc,CAACC,MAAM,CAAC,CAAC4I,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACxI,KAAK,GAAGyI,CAAC,CAACzI,KAAK,CAAC;EAC1E;EAEAgB,yBAAyBA,CAAA;IACvB,IAAI,CAAC,IAAI,CAACtB,cAAc,EAAE,OAAO,EAAE;IACnC,OAAO,IAAI,CAAC8G,eAAe,CAAC,IAAI,CAAC9G,cAAc,CAACgJ,kBAA2C,CAAC;EAC9F;;;uBAxUWzF,0BAA0B,EAAA7G,EAAA,CAAAuM,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAzM,EAAA,CAAAuM,iBAAA,CAAAG,EAAA,CAAAC,mBAAA,GAAA3M,EAAA,CAAAuM,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAA7M,EAAA,CAAAuM,iBAAA,CAAAO,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAA1BlG,0BAA0B;MAAAmG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCvBvCtN,EAAA,CAAAC,cAAA,aAAoC;UAClCD,EAAA,CAAA4B,SAAA,kBAA2B;UAE3B5B,EAAA,CAAAC,cAAA,aAAkC;UAChCD,EAAA,CAAA4B,SAAA,oBAGc;UAEd5B,EAAA,CAAAC,cAAA,aAAmC;UACjCD,EAAA,CAAA2C,UAAA,IAAA6K,yCAAA,iBAKM;UAENxN,EAAA,CAAA2C,UAAA,IAAA8K,yCAAA,iBAEM;UAENzN,EAAA,CAAA2C,UAAA,IAAA+K,kDAAA,4BAgRe;UACjB1N,EAAA,CAAAG,YAAA,EAAM;;;UA5REH,EAAA,CAAAI,SAAA,GAAa;UAAbJ,EAAA,CAAAQ,UAAA,SAAA+M,GAAA,CAAAnG,OAAA,CAAa;UAObpH,EAAA,CAAAI,SAAA,GAAW;UAAXJ,EAAA,CAAAQ,UAAA,SAAA+M,GAAA,CAAAhN,KAAA,CAAW;UAIFP,EAAA,CAAAI,SAAA,GAAwB;UAAxBJ,EAAA,CAAAQ,UAAA,UAAA+M,GAAA,CAAAnG,OAAA,KAAAmG,GAAA,CAAAhN,KAAA,CAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}