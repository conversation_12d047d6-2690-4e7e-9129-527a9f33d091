using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using DeliveryDashOptimizer.API.Models;
using DeliveryDashOptimizer.API.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace DeliveryDashOptimizer.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class OptimizationController : ControllerBase
    {
        private readonly IRouteOptimizationService _optimizationService;
        private readonly ILogger<OptimizationController> _logger;

        public OptimizationController(IRouteOptimizationService optimizationService, ILogger<OptimizationController> logger)
        {
            _optimizationService = optimizationService;
            _logger = logger;
        }

        [HttpGet("routes/{driverId}")]
        [Authorize(Roles = "Admin,Manager,Dispatcher,Driver")]
        public async Task<ActionResult<RouteOptimization>> GetOptimizedRoute(string driverId)
        {
            try
            {
                var optimizedRoute = await _optimizationService.GetOptimizedRouteForDriverAsync(driverId);
                if (optimizedRoute == null)
                {
                    return NotFound();
                }
                return Ok(optimizedRoute);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error retrieving optimized route for driver with ID {driverId}");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpPost("routes")]
        [Authorize(Roles = "Admin,Manager,Dispatcher")]
        public async Task<ActionResult<RouteOptimization>> OptimizeRoute(RouteOptimizationRequest request)
        {
            try
            {
                var optimizedRoute = await _optimizationService.OptimizeRouteAsync(request);
                return Ok(optimizedRoute);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error optimizing route");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("predictions/delivery-time")]
        [Authorize(Roles = "Admin,Manager,Dispatcher,Driver")]
        public async Task<ActionResult<DeliveryTimePrediction>> PredictDeliveryTime([FromQuery] DeliveryTimePredictionRequest request)
        {
            try
            {
                var prediction = await _optimizationService.PredictDeliveryTimeAsync(request);
                return Ok(prediction);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error predicting delivery time");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("traffic")]
        [Authorize(Roles = "Admin,Manager,Dispatcher,Driver")]
        public async Task<ActionResult<TrafficData>> GetTrafficData([FromQuery] double latitude, [FromQuery] double longitude, [FromQuery] double radius = 5.0)
        {
            try
            {
                var trafficData = await _optimizationService.GetTrafficDataAsync(latitude, longitude, radius);
                return Ok(trafficData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving traffic data");
                return StatusCode(500, "Internal server error");
            }
        }

        [HttpGet("weather")]
        [Authorize(Roles = "Admin,Manager,Dispatcher,Driver")]
        public async Task<ActionResult<WeatherData>> GetWeatherData([FromQuery] double latitude, [FromQuery] double longitude)
        {
            try
            {
                var weatherData = await _optimizationService.GetWeatherDataAsync(latitude, longitude);
                return Ok(weatherData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving weather data");
                return StatusCode(500, "Internal server error");
            }
        }
    }
}
