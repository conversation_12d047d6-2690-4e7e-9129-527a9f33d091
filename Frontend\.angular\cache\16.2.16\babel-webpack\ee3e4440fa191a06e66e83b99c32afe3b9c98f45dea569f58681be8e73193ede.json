{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"../loading-spinner/loading-spinner.component\";\nfunction DriverListComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵelement(1, \"app-loading-spinner\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DriverListComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"p\");\n    i0.ɵɵtext(2, \"Aucun chauffeur trouv\\u00E9\");\n    i0.ɵɵelementEnd()();\n  }\n}\nconst _c0 = function (a0, a1, a2) {\n  return {\n    \"bg-success\": a0,\n    \"bg-warning\": a1,\n    \"bg-secondary\": a2\n  };\n};\nfunction DriverListComponent_div_3_tr_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"td\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"td\")(8, \"span\", 9);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"td\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const driver_r4 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(driver_r4.id);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(driver_r4.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(driver_r4.phone);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(6, _c0, driver_r4.status === 0, driver_r4.status === 1, driver_r4.status === 3));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", driver_r4.status, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(driver_r4.vehicleType);\n  }\n}\nfunction DriverListComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"table\", 7)(2, \"thead\")(3, \"tr\")(4, \"th\");\n    i0.ɵɵtext(5, \"ID\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7, \"Nom\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"T\\u00E9l\\u00E9phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Statut\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\");\n    i0.ɵɵtext(13, \"V\\u00E9hicule\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"tbody\");\n    i0.ɵɵtemplate(15, DriverListComponent_div_3_tr_15_Template, 12, 10, \"tr\", 8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.drivers);\n  }\n}\nexport class DriverListComponent {\n  constructor() {\n    this.drivers = [];\n    this.loading = false;\n  }\n  static {\n    this.ɵfac = function DriverListComponent_Factory(t) {\n      return new (t || DriverListComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DriverListComponent,\n      selectors: [[\"app-driver-list\"]],\n      inputs: {\n        drivers: \"drivers\",\n        loading: \"loading\"\n      },\n      decls: 4,\n      vars: 3,\n      consts: [[1, \"driver-list\"], [\"class\", \"text-center\", 4, \"ngIf\"], [\"class\", \"text-center text-muted\", 4, \"ngIf\"], [\"class\", \"table-responsive\", 4, \"ngIf\"], [1, \"text-center\"], [1, \"text-center\", \"text-muted\"], [1, \"table-responsive\"], [1, \"table\", \"table-striped\"], [4, \"ngFor\", \"ngForOf\"], [1, \"badge\", 3, \"ngClass\"]],\n      template: function DriverListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, DriverListComponent_div_1_Template, 2, 0, \"div\", 1);\n          i0.ɵɵtemplate(2, DriverListComponent_div_2_Template, 3, 0, \"div\", 2);\n          i0.ɵɵtemplate(3, DriverListComponent_div_3_Template, 16, 1, \"div\", 3);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.drivers.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading && ctx.drivers.length > 0);\n        }\n      },\n      dependencies: [i1.NgClass, i1.NgForOf, i1.NgIf, i2.LoadingSpinnerComponent],\n      styles: [\".driver-list[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n.driver-list[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2hhcmVkL2NvbXBvbmVudHMvZHJpdmVyLWxpc3QvZHJpdmVyLWxpc3QuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQ0U7RUFDRSxnQkFBQTtBQUFKO0FBR0U7RUFDRSxrQkFBQTtBQURKIiwic291cmNlc0NvbnRlbnQiOlsiLmRyaXZlci1saXN0IHtcbiAgLnRhYmxlIHtcbiAgICBtYXJnaW4tYm90dG9tOiAwO1xuICB9XG4gIFxuICAuYmFkZ2Uge1xuICAgIGZvbnQtc2l6ZTogMC43NXJlbTtcbiAgfVxufVxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate", "driver_r4", "id", "name", "phone", "ɵɵproperty", "ɵɵpureFunction3", "_c0", "status", "ɵɵtextInterpolate1", "vehicleType", "ɵɵtemplate", "DriverListComponent_div_3_tr_15_Template", "ctx_r2", "drivers", "DriverListComponent", "constructor", "loading", "selectors", "inputs", "decls", "vars", "consts", "template", "DriverListComponent_Template", "rf", "ctx", "DriverListComponent_div_1_Template", "DriverListComponent_div_2_Template", "DriverListComponent_div_3_Template", "length"], "sources": ["C:\\Users\\<USER>\\delivery-dash-optimiser\\frontend\\src\\app\\shared\\components\\driver-list\\driver-list.component.ts", "C:\\Users\\<USER>\\delivery-dash-optimiser\\frontend\\src\\app\\shared\\components\\driver-list\\driver-list.component.html"], "sourcesContent": ["import { Component, Input } from '@angular/core';\nimport { Driver } from '@core/models/driver.model';\n\n@Component({\n  selector: 'app-driver-list',\n  templateUrl: './driver-list.component.html',\n  styleUrls: ['./driver-list.component.scss']\n})\nexport class DriverListComponent {\n  @Input() drivers: Driver[] = [];\n  @Input() loading: boolean = false;\n\n  constructor() { }\n}\n", "<div class=\"driver-list\">\n  <div *ngIf=\"loading\" class=\"text-center\">\n    <app-loading-spinner></app-loading-spinner>\n  </div>\n\n  <div *ngIf=\"!loading && drivers.length === 0\" class=\"text-center text-muted\">\n    <p><PERSON>cun chauffeur trouvé</p>\n  </div>\n\n  <div *ngIf=\"!loading && drivers.length > 0\" class=\"table-responsive\">\n    <table class=\"table table-striped\">\n      <thead>\n        <tr>\n          <th>ID</th>\n          <th>Nom</th>\n          <th>Téléphone</th>\n          <th>Statut</th>\n          <th>Véhicule</th>\n        </tr>\n      </thead>\n      <tbody>\n        <tr *ngFor=\"let driver of drivers\">\n          <td>{{ driver.id }}</td>\n          <td>{{ driver.name }}</td>\n          <td>{{ driver.phone }}</td>\n          <td>\n            <span class=\"badge\" [ngClass]=\"{\n              'bg-success': driver.status === 0,\n              'bg-warning': driver.status === 1,\n              'bg-secondary': driver.status === 3\n            }\">\n              {{ driver.status }}\n            </span>\n          </td>\n          <td>{{ driver.vehicleType }}</td>\n        </tr>\n      </tbody>\n    </table>\n  </div>\n</div>\n"], "mappings": ";;;;;ICCEA,EAAA,CAAAC,cAAA,aAAyC;IACvCD,EAAA,CAAAE,SAAA,0BAA2C;IAC7CF,EAAA,CAAAG,YAAA,EAAM;;;;;IAENH,EAAA,CAAAC,cAAA,aAA6E;IACxED,EAAA,CAAAI,MAAA,kCAAsB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;;;;;;;;;IAezBH,EAAA,CAAAC,cAAA,SAAmC;IAC7BD,EAAA,CAAAI,MAAA,GAAe;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACxBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAI,MAAA,GAAiB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC1BH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAI,MAAA,GAAkB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,SAAI;IAMAD,EAAA,CAAAI,MAAA,GACF;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAETH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAI,MAAA,IAAwB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;;;;IAZ7BH,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAM,iBAAA,CAAAC,SAAA,CAAAC,EAAA,CAAe;IACfR,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAAM,iBAAA,CAAAC,SAAA,CAAAE,IAAA,CAAiB;IACjBT,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAM,iBAAA,CAAAC,SAAA,CAAAG,KAAA,CAAkB;IAEAV,EAAA,CAAAK,SAAA,GAIlB;IAJkBL,EAAA,CAAAW,UAAA,YAAAX,EAAA,CAAAY,eAAA,IAAAC,GAAA,EAAAN,SAAA,CAAAO,MAAA,QAAAP,SAAA,CAAAO,MAAA,QAAAP,SAAA,CAAAO,MAAA,QAIlB;IACAd,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAe,kBAAA,MAAAR,SAAA,CAAAO,MAAA,MACF;IAEEd,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAAM,iBAAA,CAAAC,SAAA,CAAAS,WAAA,CAAwB;;;;;IAzBpChB,EAAA,CAAAC,cAAA,aAAqE;IAIzDD,EAAA,CAAAI,MAAA,SAAE;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACXH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAI,MAAA,UAAG;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACZH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAI,MAAA,0BAAS;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAClBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAI,MAAA,cAAM;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IACfH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAI,MAAA,qBAAQ;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAGrBH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAAiB,UAAA,KAAAC,wCAAA,kBAcK;IACPlB,EAAA,CAAAG,YAAA,EAAQ;;;;IAfiBH,EAAA,CAAAK,SAAA,IAAU;IAAVL,EAAA,CAAAW,UAAA,YAAAQ,MAAA,CAAAC,OAAA,CAAU;;;ADbzC,OAAM,MAAOC,mBAAmB;EAI9BC,YAAA;IAHS,KAAAF,OAAO,GAAa,EAAE;IACtB,KAAAG,OAAO,GAAY,KAAK;EAEjB;;;uBAJLF,mBAAmB;IAAA;EAAA;;;YAAnBA,mBAAmB;MAAAG,SAAA;MAAAC,MAAA;QAAAL,OAAA;QAAAG,OAAA;MAAA;MAAAG,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCRhC/B,EAAA,CAAAC,cAAA,aAAyB;UACvBD,EAAA,CAAAiB,UAAA,IAAAgB,kCAAA,iBAEM;UAENjC,EAAA,CAAAiB,UAAA,IAAAiB,kCAAA,iBAEM;UAENlC,EAAA,CAAAiB,UAAA,IAAAkB,kCAAA,kBA6BM;UACRnC,EAAA,CAAAG,YAAA,EAAM;;;UAtCEH,EAAA,CAAAK,SAAA,GAAa;UAAbL,EAAA,CAAAW,UAAA,SAAAqB,GAAA,CAAAT,OAAA,CAAa;UAIbvB,EAAA,CAAAK,SAAA,GAAsC;UAAtCL,EAAA,CAAAW,UAAA,UAAAqB,GAAA,CAAAT,OAAA,IAAAS,GAAA,CAAAZ,OAAA,CAAAgB,MAAA,OAAsC;UAItCpC,EAAA,CAAAK,SAAA,GAAoC;UAApCL,EAAA,CAAAW,UAAA,UAAAqB,GAAA,CAAAT,OAAA,IAAAS,GAAA,CAAAZ,OAAA,CAAAgB,MAAA,KAAoC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}