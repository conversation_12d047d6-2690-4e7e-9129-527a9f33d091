{"ast": null, "code": "import { HttpErrorResponse } from '@angular/common/http';\nimport { throwError, BehaviorSubject } from 'rxjs';\nimport { catchError, filter, take, switchMap } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@core/services/auth.service\";\nexport let AuthInterceptor = /*#__PURE__*/(() => {\n  class AuthInterceptor {\n    constructor(authService) {\n      this.authService = authService;\n      this.isRefreshing = false;\n      this.refreshTokenSubject = new BehaviorSubject(null);\n    }\n    intercept(request, next) {\n      // Skip auth header for logout requests since they handle their own authorization\n      const isLogoutRequest = request.url.includes('/auth/logout');\n      // Add auth header if user is logged in and token exists (but not for logout)\n      if (this.authService.accessToken && !isLogoutRequest) {\n        request = this.addTokenHeader(request, this.authService.accessToken);\n      }\n      return next.handle(request).pipe(catchError(error => {\n        if (error instanceof HttpErrorResponse && error.status === 401 && !isLogoutRequest) {\n          return this.handle401Error(request, next);\n        }\n        return throwError(error);\n      }));\n    }\n    addTokenHeader(request, token) {\n      return request.clone({\n        headers: request.headers.set('Authorization', `Bearer ${token}`)\n      });\n    }\n    handle401Error(request, next) {\n      if (!this.isRefreshing) {\n        this.isRefreshing = true;\n        this.refreshTokenSubject.next(null);\n        if (this.authService.refreshToken) {\n          return this.authService.refreshAccessToken().pipe(switchMap(response => {\n            this.isRefreshing = false;\n            if (response && response.success) {\n              this.refreshTokenSubject.next(response.accessToken);\n              return next.handle(this.addTokenHeader(request, response.accessToken));\n            }\n            // Refresh failed, logout user\n            this.authService.logout();\n            return throwError('Token refresh failed');\n          }), catchError(error => {\n            this.isRefreshing = false;\n            this.authService.logout();\n            return throwError(error);\n          }));\n        }\n      }\n      return this.refreshTokenSubject.pipe(filter(token => token !== null), take(1), switchMap(token => next.handle(this.addTokenHeader(request, token))));\n    }\n    static {\n      this.ɵfac = function AuthInterceptor_Factory(t) {\n        return new (t || AuthInterceptor)(i0.ɵɵinject(i1.AuthService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: AuthInterceptor,\n        factory: AuthInterceptor.ɵfac\n      });\n    }\n  }\n  return AuthInterceptor;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}