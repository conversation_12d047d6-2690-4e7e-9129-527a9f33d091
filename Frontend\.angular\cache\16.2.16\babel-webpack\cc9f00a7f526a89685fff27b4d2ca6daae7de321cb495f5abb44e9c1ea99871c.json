{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { DashboardComponent } from './pages/dashboard/dashboard.component';\nimport { CustomerPortalComponent } from './pages/customer-portal/customer-portal.component';\nimport { PublicLandingComponent } from './pages/public-landing/public-landing.component';\nimport { DeliveryTrackingComponent } from './pages/delivery-tracking/delivery-tracking.component';\nimport { DriversManagementComponent } from './pages/drivers-management/drivers-management.component';\nimport { AnalyticsComponent } from './pages/analytics/analytics.component';\nimport { RouteOptimizationComponent } from './pages/route-optimization/route-optimization.component';\nimport { SettingsComponent } from './pages/settings/settings.component';\nimport { UserManagementComponent } from './pages/user-management/user-management.component';\nimport { LoginComponent } from './pages/login/login.component';\nimport { SignupComponent } from './pages/signup/signup.component';\nimport { NotFoundComponent } from './pages/not-found/not-found.component';\nimport { AuthGuard } from './core/guards/auth.guard';\nimport { PublicGuard } from './core/guards/public.guard';\nimport { RoleGuard } from './core/guards/role.guard';\nimport { UserRole } from './core/models/user.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [\n// Public routes\n{\n  path: '',\n  component: PublicLandingComponent,\n  canActivate: [PublicGuard]\n}, {\n  path: 'public-track',\n  component: CustomerPortalComponent\n}, {\n  path: 'public-track/:trackingId',\n  component: CustomerPortalComponent\n}, {\n  path: 'login',\n  component: LoginComponent\n}, {\n  path: 'signup',\n  component: SignupComponent\n},\n// Protected routes with role-based access\n{\n  path: 'dashboard',\n  component: DashboardComponent,\n  canActivate: [AuthGuard, RoleGuard],\n  data: {\n    roles: [UserRole.Admin, UserRole.Manager, UserRole.Dispatcher]\n  }\n}, {\n  path: 'deliveries',\n  component: DeliveryTrackingComponent,\n  canActivate: [AuthGuard, RoleGuard],\n  data: {\n    roles: [UserRole.Admin, UserRole.Manager, UserRole.Dispatcher]\n  }\n}, {\n  path: 'drivers',\n  component: DriversManagementComponent,\n  canActivate: [AuthGuard, RoleGuard],\n  data: {\n    roles: [UserRole.Admin, UserRole.Manager]\n  }\n}, {\n  path: 'analytics',\n  component: AnalyticsComponent,\n  canActivate: [AuthGuard, RoleGuard],\n  data: {\n    roles: [UserRole.Admin, UserRole.Manager]\n  }\n}, {\n  path: 'optimization',\n  component: RouteOptimizationComponent,\n  canActivate: [AuthGuard, RoleGuard],\n  data: {\n    roles: [UserRole.Admin, UserRole.Manager, UserRole.Dispatcher]\n  }\n}, {\n  path: 'settings',\n  component: SettingsComponent,\n  canActivate: [AuthGuard, RoleGuard],\n  data: {\n    roles: [UserRole.Admin, UserRole.Manager]\n  }\n},\n// Driver-specific routes\n{\n  path: 'driver-dashboard',\n  component: DashboardComponent,\n  canActivate: [AuthGuard, RoleGuard],\n  data: {\n    roles: [UserRole.Driver]\n  }\n}, {\n  path: 'driver-deliveries',\n  component: DeliveryTrackingComponent,\n  canActivate: [AuthGuard, RoleGuard],\n  data: {\n    roles: [UserRole.Driver]\n  }\n}, {\n  path: 'driver-profile',\n  component: SettingsComponent,\n  canActivate: [AuthGuard, RoleGuard],\n  data: {\n    roles: [UserRole.Driver]\n  }\n},\n// Customer-specific routes\n{\n  path: 'track',\n  component: CustomerPortalComponent,\n  canActivate: [AuthGuard, RoleGuard],\n  data: {\n    roles: [UserRole.Customer]\n  }\n}, {\n  path: 'customer-portal',\n  component: CustomerPortalComponent,\n  canActivate: [AuthGuard, RoleGuard],\n  data: {\n    roles: [UserRole.Customer]\n  }\n},\n// Admin-only routes\n{\n  path: 'user-management',\n  component: UserManagementComponent,\n  canActivate: [AuthGuard, RoleGuard],\n  data: {\n    roles: [UserRole.Admin]\n  }\n},\n// Fallback routes\n{\n  path: 'not-found',\n  component: NotFoundComponent\n}, {\n  path: '**',\n  redirectTo: '/not-found'\n}];\nexport let AppRoutingModule = /*#__PURE__*/(() => {\n  class AppRoutingModule {\n    static {\n      this.ɵfac = function AppRoutingModule_Factory(t) {\n        return new (t || AppRoutingModule)();\n      };\n    }\n    static {\n      this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n        type: AppRoutingModule\n      });\n    }\n    static {\n      this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n        imports: [RouterModule.forRoot(routes), RouterModule]\n      });\n    }\n  }\n  return AppRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}