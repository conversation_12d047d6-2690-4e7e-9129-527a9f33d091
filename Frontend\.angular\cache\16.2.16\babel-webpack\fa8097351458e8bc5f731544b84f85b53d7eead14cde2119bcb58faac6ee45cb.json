{"ast": null, "code": "import { UserRole } from '../models/user.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../services/auth.service\";\nexport let RoleGuard = /*#__PURE__*/(() => {\n  class RoleGuard {\n    constructor(router, authService) {\n      this.router = router;\n      this.authService = authService;\n    }\n    canActivate(route, state) {\n      const currentUser = this.authService.currentUserValue;\n      if (!currentUser) {\n        this.router.navigate(['/login']);\n        return false;\n      }\n      // Get required roles from route data\n      const requiredRoles = route.data['roles'];\n      if (!requiredRoles || requiredRoles.length === 0) {\n        return true; // No role restriction\n      }\n      // Check if user has any of the required roles\n      if (requiredRoles.includes(currentUser.role)) {\n        return true;\n      }\n      // User doesn't have required role, redirect based on their role\n      this.redirectBasedOnRole(currentUser.role);\n      return false;\n    }\n    redirectBasedOnRole(userRole) {\n      switch (userRole) {\n        case UserRole.Admin:\n        case UserRole.Manager:\n        case UserRole.Dispatcher:\n          this.router.navigate(['/dashboard']);\n          break;\n        case UserRole.Driver:\n          this.router.navigate(['/driver-dashboard']);\n          break;\n        case UserRole.Customer:\n          this.router.navigate(['/customer-portal']);\n          break;\n        default:\n          this.router.navigate(['/login']);\n          break;\n      }\n    }\n    static {\n      this.ɵfac = function RoleGuard_Factory(t) {\n        return new (t || RoleGuard)(i0.ɵɵinject(i1.Router), i0.ɵɵinject(i2.AuthService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: RoleGuard,\n        factory: RoleGuard.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return RoleGuard;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}