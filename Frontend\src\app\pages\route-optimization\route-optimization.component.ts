import { Compo<PERSON>, <PERSON><PERSON>ni<PERSON>, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, Validators } from '@angular/forms';
import { Subscription } from 'rxjs';
import { OptimizationService } from '../../core/services/optimization.service';
import { DeliveryService } from '../../core/services/delivery.service';
import { DriverService } from '../../core/services/driver.service';
import {
  RouteOptimization,
  RouteOptimizationRequest,
  OptimizationObjective,
  DeliveryTimePrediction,
  DeliveryTimePredictionRequest,
  TrafficData,
  WeatherData
} from '../../core/models/route-optimization.model';
import { Delivery, DeliveryStatus, TrafficCondition, WeatherCondition } from '../../core/models/delivery.model';
import { Driver, DriverStatus } from '../../core/models/driver.model';

@Component({
  selector: 'app-route-optimization',
  templateUrl: './route-optimization.component.html',
  styleUrls: ['./route-optimization.component.scss']
})
export class RouteOptimizationComponent implements OnInit, OnDestroy {
  drivers: Driver[] = [];
  deliveries: Delivery[] = [];
  pendingDeliveries: Delivery[] = [];
  selectedDriver: Driver | null = null;
  optimizedRoute: RouteOptimization | null = null;
  trafficData: TrafficData | null = null;
  weatherData: WeatherData | null = null;

  optimizationForm!: FormGroup;

  loading = true;
  driversLoading = false;
  deliveriesLoading = false;
  optimizationLoading = false;
  trafficLoading = false;
  weatherLoading = false;

  error = '';

  private subscriptions: Subscription[] = [];

  constructor(
    private formBuilder: FormBuilder,
    private optimizationService: OptimizationService,
    private deliveryService: DeliveryService,
    private driverService: DriverService
  ) { }

  ngOnInit(): void {
    this.initForm();
    this.loadData();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  private initForm(): void {
    this.optimizationForm = this.formBuilder.group({
      driverId: ['', Validators.required],
      deliveryIds: [[], Validators.required],
      considerTraffic: [true],
      considerWeather: [true],
      prioritizeUrgent: [true],
      strategy: [OptimizationObjective.BalanceWorkload]
    });
  }

  private loadData(): void {
    this.loading = true;
    this.driversLoading = true;
    this.deliveriesLoading = true;

    // Load drivers
    this.subscriptions.push(
      this.driverService.getDrivers().subscribe({
        next: (drivers) => {
          this.drivers = drivers.filter(d => d.status === DriverStatus.Available);
          this.driversLoading = false;
          this.checkLoading();
        },
        error: (err) => {
          console.error('Error loading drivers', err);
          this.error = 'Erreur lors du chargement des livreurs';
          this.driversLoading = false;
          this.checkLoading();
        }
      })
    );

    // Load deliveries
    this.subscriptions.push(
      this.deliveryService.getDeliveries().subscribe({
        next: (deliveries) => {
          this.deliveries = deliveries;
          this.pendingDeliveries = deliveries.filter(d =>
            d.status === DeliveryStatus.Pending || d.status === DeliveryStatus.Delayed
          );
          this.deliveriesLoading = false;
          this.checkLoading();
        },
        error: (err) => {
          console.error('Error loading deliveries', err);
          this.error = 'Erreur lors du chargement des livraisons';
          this.deliveriesLoading = false;
          this.checkLoading();
        }
      })
    );
  }

  private checkLoading(): void {
    this.loading = this.driversLoading || this.deliveriesLoading;
  }

  onDriverChange(): void {
    const driverId = this.optimizationForm.get('driverId')?.value;
    if (!driverId) {
      this.selectedDriver = null;
      return;
    }

    this.selectedDriver = this.drivers.find(d => d.id === driverId) || null;

    if (this.selectedDriver) {
      // Load existing route for this driver
      this.loadOptimizedRoute(driverId);

      // Load traffic and weather data for driver's location
      this.loadTrafficData(this.selectedDriver.currentLocation.latitude, this.selectedDriver.currentLocation.longitude);
      this.loadWeatherData(this.selectedDriver.currentLocation.latitude, this.selectedDriver.currentLocation.longitude);
    }
  }

  loadOptimizedRoute(driverId: string): void {
    this.optimizationLoading = true;

    this.subscriptions.push(
      this.optimizationService.getOptimizedRoute(driverId).subscribe({
        next: (route) => {
          this.optimizedRoute = route;
          this.optimizationLoading = false;
        },
        error: (err) => {
          console.error('Error loading optimized route', err);
          this.optimizedRoute = null;
          this.optimizationLoading = false;
        }
      })
    );
  }

  loadTrafficData(latitude: number, longitude: number): void {
    this.trafficLoading = true;

    this.subscriptions.push(
      this.optimizationService.getTrafficData(latitude, longitude).subscribe({
        next: (data) => {
          this.trafficData = data;
          this.trafficLoading = false;
        },
        error: (err) => {
          console.error('Error loading traffic data', err);
          this.trafficData = null;
          this.trafficLoading = false;
        }
      })
    );
  }

  loadWeatherData(latitude: number, longitude: number): void {
    this.weatherLoading = true;

    this.subscriptions.push(
      this.optimizationService.getWeatherData(latitude, longitude).subscribe({
        next: (data) => {
          this.weatherData = data;
          this.weatherLoading = false;
        },
        error: (err) => {
          console.error('Error loading weather data', err);
          this.weatherData = null;
          this.weatherLoading = false;
        }
      })
    );
  }

  optimizeRoute(): void {
    if (this.optimizationForm.invalid) {
      return;
    }

    this.optimizationLoading = true;

    const request: RouteOptimizationRequest = {
      driverId: this.optimizationForm.get('driverId')?.value,
      deliveryIds: this.optimizationForm.get('deliveryIds')?.value,
      considerTraffic: this.optimizationForm.get('considerTraffic')?.value,
      considerWeather: this.optimizationForm.get('considerWeather')?.value,
      prioritizeUrgent: this.optimizationForm.get('prioritizeUrgent')?.value,
      strategy: this.optimizationForm.get('strategy')?.value
    };

    this.subscriptions.push(
      this.optimizationService.optimizeRoute(request).subscribe({
        next: (route) => {
          this.optimizedRoute = route;
          this.optimizationLoading = false;
        },
        error: (err) => {
          console.error('Error optimizing route', err);
          this.error = 'Erreur lors de l\'optimisation de l\'itinéraire';
          this.optimizationLoading = false;
        }
      })
    );
  }

  getStrategyText(strategy: OptimizationObjective): string {
    switch (strategy) {
      case OptimizationObjective.MinimizeDistance:
        return 'Minimiser la distance';
      case OptimizationObjective.MinimizeTime:
        return 'Minimiser le temps';
      case OptimizationObjective.MaximizeDeliveries:
        return 'Maximiser les livraisons';
      case OptimizationObjective.BalanceWorkload:
        return 'Équilibré';
      default:
        return strategy;
    }
  }

  getTrafficConditionText(condition: TrafficCondition): string {
    switch (condition) {
      case TrafficCondition.Light:
        return 'Fluide';
      case TrafficCondition.Moderate:
        return 'Modéré';
      case TrafficCondition.Heavy:
        return 'Dense';
      case TrafficCondition.Severe:
        return 'Très dense';
      default:
        return String(condition);
    }
  }

  getWeatherConditionText(condition: WeatherCondition): string {
    switch (condition) {
      case WeatherCondition.Clear:
        return 'Dégagé';
      case WeatherCondition.Cloudy:
        return 'Nuageux';
      case WeatherCondition.Rainy:
        return 'Pluvieux';
      case WeatherCondition.Snowy:
        return 'Neigeux';
      case WeatherCondition.Stormy:
        return 'Orageux';
      default:
        return String(condition);
    }
  }

  getTrafficConditionClass(condition: TrafficCondition): 'success' | 'danger' | 'warning' | 'info' | 'default' {
    switch (condition) {
      case TrafficCondition.Light:
        return 'success';
      case TrafficCondition.Moderate:
        return 'info';
      case TrafficCondition.Heavy:
        return 'warning';
      case TrafficCondition.Severe:
        return 'danger';
      default:
        return 'default';
    }
  }

  getWeatherConditionClass(condition: WeatherCondition): 'success' | 'danger' | 'warning' | 'info' | 'default' {
    switch (condition) {
      case WeatherCondition.Clear:
        return 'success';
      case WeatherCondition.Cloudy:
        return 'info';
      case WeatherCondition.Rainy:
        return 'warning';
      case WeatherCondition.Snowy:
        return 'warning';
      case WeatherCondition.Stormy:
        return 'danger';
      default:
        return 'default';
    }
  }

  formatTime(minutes: number): string {
    const hours = Math.floor(minutes / 60);
    const mins = Math.floor(minutes % 60);

    if (hours > 0) {
      return `${hours}h ${mins}min`;
    } else {
      return `${mins} min`;
    }
  }

  onDeliverySelectionChange(deliveryId: string, event: any): void {
    const isChecked = event.target.checked;
    const currentIds = this.optimizationForm.get('deliveryIds')?.value || [];

    if (isChecked) {
      if (!currentIds.includes(deliveryId)) {
        this.optimizationForm.get('deliveryIds')?.setValue([...currentIds, deliveryId]);
      }
    } else {
      this.optimizationForm.get('deliveryIds')?.setValue(
        currentIds.filter((id: string) => id !== deliveryId)
      );
    }
  }

  getOptimizedDeliveries(): any[] {
    if (!this.optimizedRoute) return [];
    return this.deliveries.filter(d =>
      this.optimizedRoute!.routes.some(r => r.deliveryId === d.id)
    );
  }

  getSelectedDriverArray(): any[] {
    return this.selectedDriver ? [this.selectedDriver] : [];
  }

  getOptimizedRouteArray(): any[] {
    return this.optimizedRoute ? [this.optimizedRoute] : [];
  }

  getSortedRouteStops(): any[] {
    if (!this.optimizedRoute) return [];
    return [...this.optimizedRoute.routes].sort((a, b) => a.order - b.order);
  }

  getOptimizationMethodText(): string {
    if (!this.optimizedRoute) return '';
    const method = parseInt(this.optimizedRoute.optimizationMethod, 10) as OptimizationObjective;
    return this.getStrategyText(method);
  }
}
