{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../services/auth.service\";\nexport let PublicGuard = /*#__PURE__*/(() => {\n  class PublicGuard {\n    constructor(router, authService) {\n      this.router = router;\n      this.authService = authService;\n    }\n    canActivate(route, state) {\n      if (this.authService.isAuthenticated()) {\n        // User is logged in, redirect based on their role\n        const user = this.authService.getCurrentUser();\n        if (user) {\n          this.redirectBasedOnRole(user.role);\n        } else {\n          this.router.navigate(['/dashboard']);\n        }\n        return false;\n      }\n      // User is not logged in, allow access to public page\n      return true;\n    }\n    redirectBasedOnRole(userRole) {\n      switch (userRole) {\n        case 0: // Admin\n        case 1: // Manager\n        case 2:\n          // Dispatcher\n          this.router.navigate(['/dashboard']);\n          break;\n        case 3:\n          // Driver\n          this.router.navigate(['/driver-dashboard']);\n          break;\n        case 4:\n          // Customer\n          this.router.navigate(['/customer-portal']);\n          break;\n        default:\n          this.router.navigate(['/dashboard']);\n          break;\n      }\n    }\n    static {\n      this.ɵfac = function PublicGuard_Factory(t) {\n        return new (t || PublicGuard)(i0.ɵɵinject(i1.Router), i0.ɵɵinject(i2.AuthService));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: PublicGuard,\n        factory: PublicGuard.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return PublicGuard;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}