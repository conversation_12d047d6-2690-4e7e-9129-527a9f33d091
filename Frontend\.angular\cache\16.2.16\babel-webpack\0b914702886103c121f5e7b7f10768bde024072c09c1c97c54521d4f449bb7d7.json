{"ast": null, "code": "export var OptimizationObjective;\n(function (OptimizationObjective) {\n  OptimizationObjective[OptimizationObjective[\"MinimizeDistance\"] = 0] = \"MinimizeDistance\";\n  OptimizationObjective[OptimizationObjective[\"MinimizeTime\"] = 1] = \"MinimizeTime\";\n  OptimizationObjective[OptimizationObjective[\"MaximizeDeliveries\"] = 2] = \"MaximizeDeliveries\";\n  OptimizationObjective[OptimizationObjective[\"BalanceWorkload\"] = 3] = \"BalanceWorkload\";\n})(OptimizationObjective || (OptimizationObjective = {}));", "map": {"version": 3, "names": ["OptimizationObjective"], "sources": ["C:\\Users\\<USER>\\delivery-dash-optimiser\\frontend\\src\\app\\core\\models\\route-optimization.model.ts"], "sourcesContent": ["import { DeliveryPriority, GeoCoordinates, TrafficCondition, WeatherCondition } from './delivery.model';\n\nexport interface RouteOptimization {\n  id: string;\n  driverId: string;\n  routes: RouteStop[];\n  totalDistance: number;\n  totalTime: number;\n  fuelEfficiency: number;\n  optimizationScore: number;\n  createdAt: Date;\n  completedAt?: Date;\n  isActive: boolean;\n  optimizationMethod: string;\n  weatherCondition?: WeatherCondition;\n  trafficCondition?: TrafficCondition;\n}\n\nexport interface RouteStop {\n  deliveryId: string;\n  order: number;\n  estimatedTime: number;\n  distance: number;\n  location: GeoCoordinates;\n  address: string;\n  customerName: string;\n  priority: DeliveryPriority;\n  estimatedArrival: Date;\n  actualArrival?: Date;\n}\n\nexport interface RouteOptimizationRequest {\n  driverId: string;\n  deliveryIds: string[];\n  startLocation?: GeoCoordinates;\n  endLocation?: GeoCoordinates;\n  considerTraffic: boolean;\n  considerWeather: boolean;\n  prioritizeUrgent: boolean;\n  strategy: OptimizationObjective;\n}\n\nexport enum OptimizationObjective {\n  MinimizeDistance = 0,\n  MinimizeTime = 1,\n  MaximizeDeliveries = 2,\n  BalanceWorkload = 3\n}\n\nexport interface DeliveryTimePredictionRequest {\n  deliveryId: string;\n  driverId?: string;\n  currentLocation?: GeoCoordinates;\n  currentTime: Date;\n}\n\nexport interface DeliveryTimePrediction {\n  deliveryId: string;\n  predictedDeliveryTime: Date;\n  confidenceScore: number;\n  estimatedTimeInMinutes: number;\n  factorImpacts: FactorImpact[];\n}\n\nexport interface FactorImpact {\n  factor: string;\n  impact: number;\n}\n\nexport interface TrafficData {\n  location: string;\n  condition: TrafficCondition;\n  averageSpeed: number;\n  freeFlowSpeed: number;\n  delayInMinutes: number;\n  congestion: number;\n  description: string;\n  timestamp: Date;\n  incidents: TrafficIncident[];\n  roadSegments: RoadSegment[];\n}\n\nexport interface TrafficIncident {\n  id: string;\n  type: string;\n  description: string;\n  location: GeoCoordinates;\n  startTime: Date;\n  endTime?: Date;\n  severity: string;\n  impactRadius: number;\n}\n\nexport interface RoadSegment {\n  id: string;\n  name: string;\n  startLocation: GeoCoordinates;\n  endLocation: GeoCoordinates;\n  currentSpeed: number;\n  freeFlowSpeed: number;\n  length: number;\n  condition: TrafficCondition;\n}\n\nexport interface WeatherData {\n  location: string;\n  condition: WeatherCondition;\n  temperature: number;\n  humidity: number;\n  windSpeed: number;\n  windDirection: string;\n  visibility: number;\n  pressure: number;\n  description: string;\n  timestamp: Date;\n  forecast: WeatherForecast[];\n}\n\nexport interface WeatherForecast {\n  dateTime: Date;\n  condition: WeatherCondition;\n  temperature: number;\n  precipitationProbability: number;\n  windSpeed: number;\n}\n"], "mappings": "AA0CA,WAAYA,qBAKX;AALD,WAAYA,qBAAqB;EAC/BA,qBAAA,CAAAA,qBAAA,8CAAoB;EACpBA,qBAAA,CAAAA,qBAAA,sCAAgB;EAChBA,qBAAA,CAAAA,qBAAA,kDAAsB;EACtBA,qBAAA,CAAAA,qBAAA,4CAAmB;AACrB,CAAC,EALWA,qBAAqB,KAArBA,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}