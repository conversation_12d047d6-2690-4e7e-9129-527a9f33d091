{"ast": null, "code": "import { UserRole } from '../../../core/models/user.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../core/services/auth.service\";\nimport * as i3 from \"@angular/common\";\nfunction SidebarComponent_li_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 12)(1, \"a\", 13);\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵelementStart(3, \"span\", 14);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"active\", ctx_r0.isActive(item_r2.route));\n    i0.ɵɵproperty(\"routerLink\", item_r2.route)(\"title\", ctx_r0.isCollapsed ? item_r2.title : \"\");\n    i0.ɵɵattribute(\"data-bs-toggle\", ctx_r0.isCollapsed ? \"tooltip\" : null)(\"data-bs-placement\", ctx_r0.isCollapsed ? \"right\" : null);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(item_r2.icon + \" sidebar-icon\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"d-none\", ctx_r0.isCollapsed);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(item_r2.title);\n  }\n}\nfunction SidebarComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"div\", 3)(2, \"div\", 16);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 17)(5, \"p\", 18);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 19);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 20)(10, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function SidebarComponent_div_12_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.logout());\n    });\n    i0.ɵɵelement(11, \"i\", 22);\n    i0.ɵɵelementStart(12, \"span\");\n    i0.ɵɵtext(13, \"D\\u00E9connexion\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"justify-content-center\", ctx_r1.isCollapsed);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r1.currentUser.firstName.charAt(0), \"\", ctx_r1.currentUser.lastName.charAt(0), \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"d-none\", ctx_r1.isCollapsed);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r1.currentUser.firstName, \" \", ctx_r1.currentUser.lastName, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.currentUser.email);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"w-100\", !ctx_r1.isCollapsed)(\"btn-icon-only\", ctx_r1.isCollapsed);\n    i0.ɵɵproperty(\"title\", ctx_r1.isCollapsed ? \"D\\u00E9connexion\" : \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"me-2\", !ctx_r1.isCollapsed);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"d-none\", ctx_r1.isCollapsed);\n  }\n}\nexport let SidebarComponent = /*#__PURE__*/(() => {\n  class SidebarComponent {\n    get sidebarItems() {\n      if (!this.currentUser) {\n        return [];\n      }\n      return this.allSidebarItems.filter(item => item.roles.includes(this.currentUser.role));\n    }\n    constructor(router, authService) {\n      this.router = router;\n      this.authService = authService;\n      this.currentUser = null;\n      this.isCollapsed = false;\n      this.allSidebarItems = [{\n        title: 'Tableau de bord',\n        icon: 'fa-solid fa-gauge-high',\n        route: '/dashboard',\n        roles: [UserRole.Admin, UserRole.Manager, UserRole.Dispatcher]\n      }, {\n        title: 'Livraisons',\n        icon: 'fa-solid fa-box',\n        route: '/deliveries',\n        roles: [UserRole.Admin, UserRole.Manager, UserRole.Dispatcher]\n      }, {\n        title: 'Livreurs',\n        icon: 'fa-solid fa-users',\n        route: '/drivers',\n        roles: [UserRole.Admin, UserRole.Manager]\n      }, {\n        title: 'Analyses',\n        icon: 'fa-solid fa-chart-bar',\n        route: '/analytics',\n        roles: [UserRole.Admin, UserRole.Manager]\n      }, {\n        title: 'Optimisation',\n        icon: 'fa-solid fa-arrow-trend-up',\n        route: '/optimization',\n        roles: [UserRole.Admin, UserRole.Manager, UserRole.Dispatcher]\n      }, {\n        title: 'Mes livraisons',\n        icon: 'fa-solid fa-truck',\n        route: '/driver-deliveries',\n        roles: [UserRole.Driver]\n      }, {\n        title: 'Mon profil',\n        icon: 'fa-solid fa-user',\n        route: '/driver-profile',\n        roles: [UserRole.Driver]\n      }, {\n        title: 'Gestion utilisateurs',\n        icon: 'fa-solid fa-users-cog',\n        route: '/user-management',\n        roles: [UserRole.Admin]\n      }, {\n        title: 'Paramètres',\n        icon: 'fa-solid fa-gear',\n        route: '/settings',\n        roles: [UserRole.Admin, UserRole.Manager]\n      }];\n    }\n    ngOnInit() {\n      this.authService.currentUser$.subscribe(user => {\n        this.currentUser = user;\n        // Redirect users to appropriate dashboard based on role\n        if (user && this.router.url === '/dashboard') {\n          this.redirectToRoleDashboard(user.role);\n        }\n      });\n    }\n    redirectToRoleDashboard(role) {\n      switch (role) {\n        case UserRole.Driver:\n          if (this.router.url === '/dashboard') {\n            this.router.navigate(['/driver-deliveries']);\n          }\n          break;\n        case UserRole.Customer:\n          if (this.router.url === '/dashboard') {\n            this.router.navigate(['/customer-portal']);\n          }\n          break;\n        // Admin, Manager, Dispatcher stay on dashboard\n      }\n    }\n\n    isActive(route) {\n      if (route === '/dashboard') {\n        return this.router.url === '/dashboard' || this.router.url === '/';\n      }\n      return this.router.url.startsWith(route);\n    }\n    logout() {\n      this.authService.logout().subscribe({\n        next: () => {\n          // Small delay to ensure authentication state is cleared\n          setTimeout(() => {\n            this.router.navigate(['/login']);\n          }, 100);\n        },\n        error: error => {\n          console.error('Logout error:', error);\n          // Navigate anyway since local storage is already cleared\n          setTimeout(() => {\n            this.router.navigate(['/login']);\n          }, 100);\n        }\n      });\n    }\n    toggleSidebar() {\n      this.isCollapsed = !this.isCollapsed;\n    }\n    static {\n      this.ɵfac = function SidebarComponent_Factory(t) {\n        return new (t || SidebarComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.AuthService));\n      };\n    }\n    static {\n      this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n        type: SidebarComponent,\n        selectors: [[\"app-sidebar\"]],\n        decls: 13,\n        vars: 10,\n        consts: [[1, \"sidebar\", \"h-100\", \"d-flex\", \"flex-column\"], [1, \"sidebar-header\", \"p-4\", \"border-bottom\"], [1, \"d-flex\", \"align-items-center\", \"justify-content-between\"], [1, \"d-flex\", \"align-items-center\"], [1, \"fa-solid\", \"fa-truck\", \"text-primary\", \"fs-4\", \"me-2\"], [1, \"fs-4\", \"fw-bold\", \"text-primary\", \"sidebar-title\"], [1, \"btn\", \"btn-link\", \"text-white\", \"p-0\", \"sidebar-toggle\", 3, \"click\"], [1, \"fa-solid\"], [1, \"sidebar-nav\", \"flex-grow-1\", \"p-3\"], [1, \"nav\", \"flex-column\"], [\"class\", \"nav-item mb-2\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"sidebar-footer p-3 border-top\", 4, \"ngIf\"], [1, \"nav-item\", \"mb-2\"], [1, \"sidebar-link\", 3, \"routerLink\", \"title\"], [1, \"sidebar-text\"], [1, \"sidebar-footer\", \"p-3\", \"border-top\"], [1, \"avatar\"], [1, \"ms-3\"], [1, \"mb-0\", \"fw-medium\"], [1, \"text-muted\", \"small\", \"mb-0\"], [1, \"mt-3\"], [1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", 3, \"title\", \"click\"], [1, \"fa-solid\", \"fa-sign-out-alt\"]],\n        template: function SidebarComponent_Template(rf, ctx) {\n          if (rf & 1) {\n            i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n            i0.ɵɵelement(4, \"i\", 4);\n            i0.ɵɵelementStart(5, \"span\", 5);\n            i0.ɵɵtext(6, \"DeliveryDash\");\n            i0.ɵɵelementEnd()();\n            i0.ɵɵelementStart(7, \"button\", 6);\n            i0.ɵɵlistener(\"click\", function SidebarComponent_Template_button_click_7_listener() {\n              return ctx.toggleSidebar();\n            });\n            i0.ɵɵelement(8, \"i\", 7);\n            i0.ɵɵelementEnd()()();\n            i0.ɵɵelementStart(9, \"nav\", 8)(10, \"ul\", 9);\n            i0.ɵɵtemplate(11, SidebarComponent_li_11_Template, 5, 11, \"li\", 10);\n            i0.ɵɵelementEnd()();\n            i0.ɵɵtemplate(12, SidebarComponent_div_12_Template, 14, 18, \"div\", 11);\n            i0.ɵɵelementEnd();\n          }\n          if (rf & 2) {\n            i0.ɵɵclassProp(\"collapsed\", ctx.isCollapsed);\n            i0.ɵɵadvance(5);\n            i0.ɵɵclassProp(\"d-none\", ctx.isCollapsed);\n            i0.ɵɵadvance(3);\n            i0.ɵɵclassProp(\"fa-bars\", ctx.isCollapsed)(\"fa-times\", !ctx.isCollapsed);\n            i0.ɵɵadvance(3);\n            i0.ɵɵproperty(\"ngForOf\", ctx.sidebarItems);\n            i0.ɵɵadvance(1);\n            i0.ɵɵproperty(\"ngIf\", ctx.currentUser);\n          }\n        },\n        dependencies: [i3.NgForOf, i3.NgIf, i1.RouterLink],\n        styles: [\".sidebar[_ngcontent-%COMP%]{width:280px;background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);box-shadow:0 4px 20px #00000026;position:relative;transition:width .3s ease}.sidebar.collapsed[_ngcontent-%COMP%]{width:80px}.sidebar[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;inset:0;background:rgba(255,255,255,.1);-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);z-index:-1}.sidebar-header[_ngcontent-%COMP%]{height:70px;border-bottom:1px solid rgba(255,255,255,.2)}.sidebar-header[_ngcontent-%COMP%]   .fs-4[_ngcontent-%COMP%]{color:#fff!important;text-shadow:0 2px 4px rgba(0,0,0,.3)}.sidebar-header[_ngcontent-%COMP%]   .fa-truck[_ngcontent-%COMP%]{color:#fbbf24!important;filter:drop-shadow(0 2px 4px rgba(0,0,0,.3))}.sidebar-toggle[_ngcontent-%COMP%]{color:#fffc!important;font-size:1.2rem;transition:all .3s ease}.sidebar-toggle[_ngcontent-%COMP%]:hover{color:#fff!important;transform:scale(1.1)}.sidebar-title[_ngcontent-%COMP%]{transition:opacity .3s ease}.sidebar-nav[_ngcontent-%COMP%]{overflow-y:auto}.sidebar-link[_ngcontent-%COMP%]{display:flex;align-items:center;padding:.875rem 1.25rem;color:#fffc;border-radius:.75rem;text-decoration:none;transition:all .3s cubic-bezier(.4,0,.2,1);margin:.25rem .75rem;position:relative;overflow:hidden}.sidebar-link[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.1),transparent);transition:left .5s}.sidebar-link[_ngcontent-%COMP%]:hover:before{left:100%}.sidebar-link[_ngcontent-%COMP%]:hover{background-color:#ffffff26;color:#fff;transform:translate(4px);box-shadow:0 4px 12px #00000026}.sidebar-link.active[_ngcontent-%COMP%]{background:linear-gradient(135deg,rgba(255,255,255,.2),rgba(255,255,255,.1));color:#fff;font-weight:600;box-shadow:0 4px 12px #0003;border:1px solid rgba(255,255,255,.3)}.sidebar-icon[_ngcontent-%COMP%]{width:20px;margin-right:12px;text-align:center}.sidebar-footer[_ngcontent-%COMP%]{background:rgba(255,255,255,.1);border-top:1px solid rgba(255,255,255,.2);-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.sidebar-footer[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#fff!important}.sidebar-footer[_ngcontent-%COMP%]   p.text-muted[_ngcontent-%COMP%]{color:#ffffffb3!important}.sidebar-footer[_ngcontent-%COMP%]   .btn-outline-secondary[_ngcontent-%COMP%]{border-color:#ffffff4d;color:#fff;background:rgba(255,255,255,.1);transition:all .3s ease}.sidebar-footer[_ngcontent-%COMP%]   .btn-outline-secondary[_ngcontent-%COMP%]:hover{background:rgba(255,255,255,.2);border-color:#ffffff80;color:#fff;transform:translateY(-2px);box-shadow:0 4px 12px #0003}.avatar[_ngcontent-%COMP%]{width:45px;height:45px;border-radius:50%;background:linear-gradient(135deg,#fbbf24,#f59e0b);color:#fff;display:flex;align-items:center;justify-content:center;font-weight:700;font-size:.875rem;box-shadow:0 4px 12px #0003;border:2px solid rgba(255,255,255,.3)}.btn-icon-only[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%!important;display:flex;align-items:center;justify-content:center;padding:0!important}.sidebar-text[_ngcontent-%COMP%]{transition:opacity .3s ease}@media (max-width: 768px){.sidebar[_ngcontent-%COMP%]{position:fixed;top:0;left:0;height:100vh;z-index:1050;transform:translate(-100%);transition:transform .3s ease}.sidebar[_ngcontent-%COMP%]:not(.collapsed){transform:translate(0)}.sidebar.collapsed[_ngcontent-%COMP%]{width:280px;transform:translate(-100%)}}\"]\n      });\n    }\n  }\n  return SidebarComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}